/**
 * @fileoverview Vue插件注册模块
 * @description 集中管理应用中所有第三方插件的注册和配置
 */

import VueWechatTitle from 'vue-wechat-title'
import VideoJsPlayer from '@videojs-player/vue'
import 'video.js/dist/video-js.css'
import { Lazyload } from 'vant'

// 图片资源
import loadingImgPlaceHolder from '@/assets/images/placeholder-loading.png'
import errorImgPlaceHolder from '@/assets/images/placeholder-error.png'

/**
 * 注册所有Vue插件
 * @description 统一注册应用所需的所有第三方插件，包括微信标题、视频播放器、图片懒加载等
 * @function registerPlugins
 * @param {import('vue').App} app - Vue应用实例
 * @returns {void}
 * @example
 * import { createApp } from 'vue'
 * import { registerPlugins } from './plugins'
 *
 * const app = createApp(App)
 * registerPlugins(app)
 * app.mount('#app')
 */
export function registerPlugins (app) {
  // 注册 vue-wechat-title
  app.use(VueWechatTitle)

  // 注册 VideoJsPlayer 组件
  app.component('VideoJsPlayer', VideoJsPlayer)

  // 注册 Vant Lazyload 插件，配置加载中和加载失败的占位图
  app.use(Lazyload, {
    loading: loadingImgPlaceHolder,
    error: errorImgPlaceHolder
  })
}
