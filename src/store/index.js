/**
 * @fileoverview Pinia根状态管理模块
 * @description 定义应用的根级状态管理，包含全局共享的状态和操作
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getCurLoanCode, setCurLoanCode } from '@/utils/storage'

/**
 * 根状态管理Store
 * @description 管理应用级别的全局状态，主要包含贷款产品代码的管理
 * @function useRootStore
 * @returns {Object} Pinia store实例
 * @example
 * import { useRootStore } from '@/store'
 *
 * const rootStore = useRootStore()
 * console.log(rootStore.loanProductCode) // 获取当前贷款产品代码
 * rootStore.changeLoanProductCode('NEW_CODE') // 更新贷款产品代码
 */
export const useRootStore = defineStore('root', () => {
  /**
   * 贷款产品代码
   * @type {import('vue').Ref<number|string>}
   * @description 当前选中的贷款产品代码，默认值为-1（避免默认选中"不分期"选项）
   */
  const loanProductCode = ref(getCurLoanCode() || -1)

  /**
   * 更改贷款产品代码
   * @description 更新当前贷款产品代码并同步到本地存储
   * @function changeLoanProductCode
   * @param {number|string} payload - 新的贷款产品代码
   * @returns {void}
   * @example
   * const rootStore = useRootStore()
   * rootStore.changeLoanProductCode('LOAN_001')
   */
  const changeLoanProductCode = (payload) => {
    setCurLoanCode(payload)
    loanProductCode.value = payload
  }

  return {
    loanProductCode,
    changeLoanProductCode
  }
})
