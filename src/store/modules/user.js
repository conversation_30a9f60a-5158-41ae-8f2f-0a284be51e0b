/**
 * @fileoverview 用户状态管理模块
 * @description 管理用户登录状态、地址信息等用户相关数据
 */

import { defineStore } from 'pinia'
import { urlAppend, useLogin, log } from 'commonkit'
import { queryUserAddrList, queryUserDefaultAddr } from '@api/interface/address.js'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz, curTempAddrInfo, loginType } from '@utils/storage.js'

/**
 * 用户状态管理Store
 * @description 管理用户登录状态、地址信息、用户ID等相关状态和操作
 * @function useUserStore
 * @returns {Object} Pinia store实例
 * @example
 * import { useUserStore } from '@/store/modules/user'
 *
 * const userStore = useUserStore()
 * await userStore.queryLoginStatus() // 查询登录状态
 * const currentAddr = userStore.curAddressInfo // 获取当前地址信息
 */
export const useUserStore = defineStore('user', {
  /**
   * 用户状态数据
   * @returns {Object} 状态对象
   * @property {boolean|null} isLogin - 登录状态 (null: 未查询, true: 已登录, false: 未登录)
   * @property {string|null} cifUserId - 用户CIF ID
   * @property {Array} addressInfoTemp - 用户临时选择的地址 (Vant-Cascader 数据结构)
   * @property {Object|null} addressInfo - 用户的真实地址信息
   * @property {Array|null} addressList - 用户地址列表
   * @property {Object} addressInfoDefault - 默认地址信息 (北京-朝阳区-管庄地区)
   */
  state: () => ({
    isLogin: null, // null-未查询，true-已登录，false-未登录
    cifUserId: null,
    addressInfoTemp: curTempAddrInfo.get() || [], // 用户临时选择的地址（数组形式，Vant-Cascader 数据结构）
    addressInfo: null, // 用户的地址信息
    addressList: null, // 用户地址列表
    addressInfoDefault: {
      provinceId: '1',
      provinceName: '北京',
      cityId: '72',
      cityName: '朝阳区',
      countyId: '4137',
      countyName: '管庄地区',
      townId: '0',
      townName: ''
    } // 用户没有地址情况下，默认的地址
  }),
  getters: {
    /**
     * 获取当前地址信息
     * @description 根据优先级返回当前有效的地址信息：临时地址 > 用户真实地址 > 默认地址
     * @returns {Object} 地址信息对象
     * @property {number} type - 地址类型 (1: 用户真实地址, 2: 默认地址, 3: 临时地址)
     * @property {string} addressId - 地址ID
     * @property {boolean} isDefault - 是否为默认地址
     * @property {string} provinceId - 省份ID
     * @property {string} provinceName - 省份名称
     * @property {string} cityId - 城市ID
     * @property {string} cityName - 城市名称
     * @property {string} countyId - 区县ID
     * @property {string} countyName - 区县名称
     * @property {string} townId - 乡镇ID
     * @property {string} townName - 乡镇名称
     * @property {string} addrDetail - 详细地址
     * @property {string} recName - 收货人姓名
     * @property {string} recPhone - 收货人电话
     * @example
     * const userStore = useUserStore()
     * const currentAddr = userStore.curAddressInfo
     * console.log(currentAddr.type) // 1, 2, 或 3
     */
    curAddressInfo() {
      const addrInfo = this.addressInfo
      const addrInfoDefault = this.addressInfoDefault
      const addrInfoTemp = this.addressInfoTemp
      if (addrInfoTemp[0]) {
        // 存在临时地址，直接返回临时地址数据
        const province = addrInfoTemp[0]
        const city = addrInfoTemp[1]
        const county = addrInfoTemp[2]
        const town = addrInfoTemp[3]
        return {
          type: 3,
          addressId: '',
          isDefault: false,
          provinceId: province ? province.areaId : '0',
          provinceName: province ? province.areaName : '',
          cityId: city ? city.areaId : '0',
          cityName: city ? city.areaName : '',
          countyId: county ? county.areaId : '0',
          countyName: county ? county.areaName : '',
          townId: town ? town.areaId : '0',
          townName: town ? town.areaName : '',
          addrDetail: '',
          recName: '',
          recPhone: ''
        }
      } else if (addrInfo?.provinceId) {
        // 存在用户真实地址
        return {
          type: 1,
          addressId: addrInfo.addressId,
          isDefault: addrInfo.isDefault === '1',
          provinceId: addrInfo.provinceId || '0',
          provinceName: addrInfo.provinceName,
          cityId: addrInfo.cityId || '0',
          cityName: addrInfo.cityName,
          countyId: addrInfo.countyId || '0',
          countyName: addrInfo.countyName,
          townId: addrInfo.townId || '0',
          townName: addrInfo.townName,
          addrDetail: addrInfo.addrDetail,
          recName: addrInfo.recName,
          recPhone: addrInfo.recPhone
        }
      } else {
        // 返回默认北京-管庄地址
        return {
          type: 2,
          addressId: '',
          isDefault: false,
          provinceId: addrInfoDefault.provinceId,
          provinceName: addrInfoDefault.provinceName,
          cityId: addrInfoDefault.cityId,
          cityName: addrInfoDefault.cityName,
          countyId: addrInfoDefault.countyId,
          countyName: addrInfoDefault.countyName,
          townId: addrInfoDefault.townId,
          townName: addrInfoDefault.townName,
          addrDetail: '',
          recName: '',
          recPhone: ''
        }
      }
    },
    /**
     * 获取用户地址信息
     * @description 返回用户的真实地址信息
     * @returns {Object|null} 用户地址信息对象，如果没有则返回null
     * @example
     * const userStore = useUserStore()
     * const addressInfo = userStore.getAddressInfo
     */
    getAddressInfo() {
      return this.addressInfo
    }
  },
  actions: {
    /**
     * 设置登录状态
     * @description 更新用户的登录状态
     * @param {boolean} isLogin - 登录状态 (true: 已登录, false: 未登录)
     * @example
     * const userStore = useUserStore()
     * userStore.setLoginStatus(true) // 设置为已登录
     */
    setLoginStatus(payload) {
      this.isLogin = payload
    },
    /**
     * 设置默认地址
     * @description 设置用户的默认地址信息
     * @param {Object} payload - 地址信息对象
     * @example
     * const userStore = useUserStore()
     * userStore.setDefaultAddr(addressData)
     */
    setDefaultAddr(payload) {
      if (!payload) return
      this.addressInfo = payload
    },
    /**
     * 设置临时地址
     * @description 设置用户临时选择的地址，并同步到本地存储
     * @param {Array} payload - 临时地址数组 (Vant-Cascader 数据结构)
     * @example
     * const userStore = useUserStore()
     * userStore.setTempAddr([{text: '北京', value: '1'}, {text: '朝阳区', value: '72'}])
     */
    setTempAddr(payload) {
      this.addressInfoTemp = payload || []
      curTempAddrInfo.set(payload)
    },
    /**
     * 设置地址列表
     * @description 设置用户的地址列表
     * @param {Array} payload - 地址列表数组
     * @example
     * const userStore = useUserStore()
     * userStore.setAddrList(addressListData)
     */
    setAddrList(payload) {
      if (!payload) return
      this.addressList = payload
    },
    /**
     * 设置用户CIF ID
     * @description 设置用户的CIF用户ID
     * @param {string} payload - 用户CIF ID
     * @example
     * const userStore = useUserStore()
     * userStore.setCifUserId('123456789')
     */
    setCifUserId(payload) {
      this.cifUserId = payload
    },
    /**
     * 查询默认地址
     * @description 异步查询用户的默认地址信息
     * @async
     * @param {Object} options - 查询选项
     * @param {boolean} options.force - 是否强制查询，默认为false
     * @returns {Promise<void>} 查询完成后更新store中的地址信息
     * @example
     * const userStore = useUserStore()
     * await userStore.queryDefaultAddr({ force: true })
     * console.log('默认地址已更新:', userStore.addressInfo)
     */
    async queryDefaultAddr({ force = false } = {}) {
      log('user.js queryDefaultAddr 查询登录')
      await this.queryLoginStatus()
      if (!this.isLogin) return // 未登录
      if (!force && this.addressInfo?.provinceId) return // 已经查询过默认地址
      const [err, json] = await queryUserDefaultAddr()
      if (!err) {
        this.setDefaultAddr(json || {})
      }
    },
    /**
     * 查询地址列表
     * @description 异步查询用户的地址列表
     * @async
     * @param {Object} options - 查询选项
     * @param {boolean} options.force - 是否强制查询，默认为false
     * @returns {Promise<void>} 查询完成后更新store中的地址列表
     * @example
     * const userStore = useUserStore()
     * await userStore.queryAddrList()
     * console.log('地址列表:', userStore.addressList)
     */
    async queryAddrList({ force = false } = {}) {
      log('user.js queryAddrList 查询登录')
      await this.queryLoginStatus()
      if (!this.isLogin) return // 未登录
      if (!force && this.addressList) return // 已经查询过默认地址
      const [err, json] = await queryUserAddrList()
      if (!err) {
        this.setAddrList(json || [])
      }
    },
    /**
     * 查询登录状态
     * @description 异步查询用户的登录状态，如果已登录则获取用户信息
     * @async
     * @returns {Promise<void>} 查询完成后更新store中的登录状态和用户信息
     * @example
     * const userStore = useUserStore()
     * await userStore.queryLoginStatus()
     * if (userStore.isLogin) {
     *   console.log('用户已登录，CIF ID:', userStore.cifUserId)
     * }
     */
    async queryLoginStatus() {
      if (typeof this.isLogin === 'boolean') {
        // 已经有了明确结果，不在查询了
        log('user.js queryLoginStatus 当前已存在登录状态', this.isLogin)
        return
      }
      const [loginState, queryStatus] = useLogin()
      // 查询登录状态
      await queryStatus({ useHistoryReplace: true, loginType: loginType.get() || '0' })
      // loginState.status 用户登录状态，0-未检查，-1-登录错误，1-未登录，2-已登录
      const isLogin = loginState.status === 2
      const cifId = loginState.cifId
      log('user.js queryLoginStatus 查询登录状态', isLogin)
      this.setLoginStatus(isLogin)
      this.setCifUserId(cifId)
    },
    /**
     * 执行登录操作
     * @description 调用登录服务进行用户登录
     * @async
     * @returns {Promise<void>} 登录操作完成
     * @example
     * const userStore = useUserStore()
     * await userStore.login()
     */
    async login(payload = {}) {
      const reload = typeof payload.reload === 'boolean' ? payload.reload : true
      if (this.isLogin) {
        // 已登录，直接刷新防止页面没处理状态
        if (reload) window.location.reload()
        return true
      }

      const [, , toLogin] = useLogin()
      const url = window.location.href
      const callbackUrl = urlAppend(url, { distri_biz_code: getBizCode(), biz_channel_code: curChannelBiz.get() })
      return new Promise((resolve) => {
        toLogin({
          callbackUrl,
          callback: (res) => {
            resolve(res === 2)
          }
        }, { useHistoryReplace: true, loginType: loginType.get() || '0' })
      })
    }
  }
})
