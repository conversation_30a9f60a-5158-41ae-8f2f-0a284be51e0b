/**
 * @fileoverview 省份服务商状态管理模块
 * @description 管理省份和服务商选择相关的状态信息，包括省份列表、服务商列表的获取和选择状态的持久化
 */

import { defineStore } from 'pinia'
import { storage } from 'commonkit'
import { zqAllSupplierList } from '@/api/interface/zq'

/**
 * 创建sessionStorage缓存（保留其他缓存，只移除serviceList缓存）
 * @description 用于持久化用户选择的区域ID和服务商ID
 */
const selectedAreaIdCache = storage('PS_CCMS_SELECTED_AREA_ID', false)
const selectedIsvIdCache = storage('PS_CCMS_SELECTED_ISV_ID', false)

/**
 * 省份服务商状态管理Store
 * @description 用于管理省份和服务商的选择状态，提供省份列表、服务商列表的管理功能
 * @example
 * ```javascript
 * import { useProvinceServiceStore } from '@/store/modules/provinceService'
 *
 * const provinceServiceStore = useProvinceServiceStore()
 *
 * // 选择省份
 * provinceServiceStore.selectProvince('1') // 选择北京
 *
 * // 选择服务商
 * provinceServiceStore.selectService('isv001')
 *
 * // 获取服务商列表
 * await provinceServiceStore.fetchServiceList({ areaId: '1' })
 *
 * // 重置选择
 * provinceServiceStore.resetSelection()
 *
 * // 获取选中的省份名称
 * console.log(provinceServiceStore.selectedProvinceName) // '北京'
 * ```
 */
export const useProvinceServiceStore = defineStore('provinceService', {
  /**
   * 省份服务商状态
   * @returns {Object} 省份服务商相关的状态对象
   */
  state: () => ({
    /**
     * 选中的区域ID
     * @type {string}
     * @description 当前选中的省份区域ID，从缓存中恢复数据，如果没有缓存则使用默认值
     */
    selectedAreaId: selectedAreaIdCache.get() || '',

    /**
     * 选中的服务商ID
     * @type {string}
     * @description 当前选中的服务商ID，从缓存中恢复数据，如果没有缓存则使用默认值
     */
    selectedIsvId: selectedIsvIdCache.get() || '',
    /**
     * 省份列表
     * @type {Array<Object>}
     * @description 包含中国所有省份的静态列表
     * @property {string} areaId - 省份区域ID
     * @property {string} areaName - 省份名称
     */
    provinceList: [
      { areaId: '1', areaName: '北京' },
      { areaId: '2', areaName: '上海' },
      { areaId: '3', areaName: '天津' },
      { areaId: '4', areaName: '重庆' },
      { areaId: '5', areaName: '河北' },
      { areaId: '6', areaName: '山西' },
      { areaId: '7', areaName: '河南' },
      { areaId: '8', areaName: '辽宁' },
      { areaId: '9', areaName: '吉林' },
      { areaId: '10', areaName: '黑龙江' },
      { areaId: '11', areaName: '内蒙古' },
      { areaId: '12', areaName: '江苏' },
      { areaId: '13', areaName: '山东' },
      { areaId: '14', areaName: '安徽' },
      { areaId: '15', areaName: '浙江' },
      { areaId: '16', areaName: '福建' },
      { areaId: '17', areaName: '湖北' },
      { areaId: '18', areaName: '湖南' },
      { areaId: '19', areaName: '广东' },
      { areaId: '20', areaName: '广西' },
      { areaId: '21', areaName: '江西' },
      { areaId: '22', areaName: '四川' },
      { areaId: '23', areaName: '海南' },
      { areaId: '24', areaName: '贵州' },
      { areaId: '25', areaName: '云南' },
      { areaId: '26', areaName: '西藏' },
      { areaId: '27', areaName: '陕西' },
      { areaId: '28', areaName: '甘肃' },
      { areaId: '29', areaName: '青海' },
      { areaId: '30', areaName: '宁夏' },
      { areaId: '31', areaName: '新疆' }
    ],
    /**
     * 服务商列表
     * @type {Array<Object>}
     * @description 服务商列表，每次都从接口获取，不使用缓存
     * @property {string} isvId - 服务商ID
     * @property {string} isvName - 服务商名称
     */
    serviceList: [],

    /**
     * 加载状态
     * @type {boolean}
     * @description 标识是否正在加载服务商列表
     */
    loading: false
  }),
  getters: {
    /**
     * 获取选中的省份名称
     * @description 根据选中的区域ID获取对应的省份名称
     * @param {Object} state - 当前状态
     * @returns {string} 省份名称，如果未选择则返回'全部'
     * @example
     * ```javascript
     * // 假设selectedAreaId为'1'
     * console.log(selectedProvinceName) // '北京'
     *
     * // 如果未选择省份
     * console.log(selectedProvinceName) // '全部'
     * ```
     */
    selectedProvinceName: (state) => {
      if (!state.selectedAreaId) return '全部'
      const province = state.provinceList.find(p => p.areaId === state.selectedAreaId)
      return province ? province.areaName : '全部'
    },

    /**
     * 获取选中的服务商名称
     * @description 根据选中的服务商ID获取对应的服务商名称
     * @param {Object} state - 当前状态
     * @returns {string} 服务商名称，如果未选择则返回'全部'
     * @example
     * ```javascript
     * // 假设selectedIsvId为'isv001'
     * console.log(selectedServiceName) // '某服务商名称'
     *
     * // 如果未选择服务商
     * console.log(selectedServiceName) // '全部'
     * ```
     */
    selectedServiceName: (state) => {
      if (!state.selectedIsvId) return '全部'
      const service = state.serviceList.find(s => s.isvId === state.selectedIsvId)
      return service ? service.isvName : '全部'
    }
  },
  actions: {
    /**
     * 选择省份
     * @description 设置选中的省份区域ID并同步更新缓存
     * @param {string} areaId - 省份区域ID
     * @example
     * ```javascript
     * // 选择北京
     * selectProvince('1')
     *
     * // 清空选择
     * selectProvince('')
     * ```
     */
    selectProvince (areaId) {
      this.selectedAreaId = areaId
      // 同步更新缓存
      selectedAreaIdCache.set(areaId)
    },
    /**
     * 选择服务商
     * @description 设置选中的服务商ID并同步更新缓存
     * @param {string} isvId - 服务商ID
     * @example
     * ```javascript
     * // 选择特定服务商
     * selectService('isv001')
     *
     * // 清空选择
     * selectService('')
     * ```
     */
    selectService (isvId) {
      this.selectedIsvId = isvId
      // 同步更新缓存
      selectedIsvIdCache.set(isvId)
    },
    /**
     * 重置选择
     * @description 重置所有选择状态并清空缓存
     * @example
     * ```javascript
     * // 重置所有选择
     * resetSelection()
     * ```
     */
    resetSelection () {
      this.selectedAreaId = ''
      this.selectedIsvId = ''
      // 同步清空缓存
      selectedAreaIdCache.set('')
      selectedIsvIdCache.set('')
    },
    /**
     * 获取服务商列表
     * @description 异步获取服务商列表，每次都从接口获取最新数据，不使用缓存
     * @param {Object} params - 查询参数
     * @param {string} [params.areaId] - 省份区域ID，用于筛选特定省份的服务商
     * @returns {Promise<Array>} 返回服务商列表数组
     * @example
     * ```javascript
     * // 获取所有服务商
     * const allServices = await fetchServiceList()
     *
     * // 获取特定省份的服务商
     * const beijingServices = await fetchServiceList({ areaId: '1' })
     *
     * // 处理返回结果
     * if (allServices.length > 0) {
     *   console.log('获取到服务商列表:', allServices)
     * }
     * ```
     */
    async fetchServiceList (params = {}) {
      this.loading = true
      try {
        const [error, data] = await zqAllSupplierList(params)
        if (!error && data) {
          this.serviceList = data
          return data
        } else {
          console.error('获取服务商列表失败:', error)
          return []
        }
      } catch (error) {
        console.error('获取服务商列表异常:', error)
        return []
      } finally {
        this.loading = false
      }
    }
  }
})
