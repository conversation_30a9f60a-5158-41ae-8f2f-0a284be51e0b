/**
 * @fileoverview 京东自营状态管理模块
 * @description 管理京东自营相关的状态信息，主要用于控制tabbar的显示状态
 * <AUTHOR>
 * @since 1.0.0
 */

import { defineStore } from 'pinia'

/**
 * 京东自营状态管理Store
 * @description 用于管理京东自营页面的tabbar状态
 * @example
 * ```javascript
 * import { useJdzyStore } from '@/store/modules/jdzy'
 * 
 * const jdzyStore = useJdzyStore()
 * 
 * // 切换tabbar状态
 * jdzyStore.changeJDtabbar('1')
 * 
 * // 获取当前tabbar状态
 * console.log(jdzyStore.JDtabbar) // '1'
 * ```
 */
export const useJdzyStore = defineStore('jdzy', {
  /**
   * 京东自营状态
   * @returns {Object} 京东自营相关的状态对象
   */
  state: () => ({
    /**
     * 京东tabbar状态
     * @type {string}
     * @description 控制京东自营页面tabbar的显示状态，'0'表示默认状态，'1'表示激活状态
     */
    JDtabbar: '0'
  }),
  actions: {
    /**
     * 切换京东tabbar状态
     * @description 更新京东自营页面的tabbar显示状态
     * @param {string} payload - 新的tabbar状态值
     * @example
     * ```javascript
     * // 设置为激活状态
     * changeJDtabbar('1')
     * 
     * // 设置为默认状态
     * changeJDtabbar('0')
     * ```
     */
    changeJDtabbar(payload) {
      this.JDtabbar = payload
    }
  }
})