/**
 * @fileoverview 分销商（发展人）状态管理模块
 * @description 管理分销商相关的状态信息，包括能人ID、发展人ID、收益信息、订单列表等
 */

import { defineStore } from 'pinia'
import { queryIncomeInfo, queryOrderList, checkSignature } from '@api/interface/fxd.js'
import { curDeveloperId } from '@utils/storage.js'

const storage = window.sessionStorage

/**
 * 分销商状态管理Store
 * @description 用于管理分销商业务相关的状态信息，包括能人收益、订单管理等功能
 * @example
 * ```javascript
 * import { useFxdStore } from '@/store/modules/fxd'
 *
 * const fxdStore = useFxdStore()
 *
 * // 查询收益信息
 * await fxdStore.queryIncomeInfo({ fxdtoken: 'token123' })
 *
 * // 查询订单列表
 * await fxdStore.queryOrderList({ orderType: '1' })
 *
 * // 设置能人ID
 * fxdStore.setSalesmanId({ salesmanId: 'salesman123', distribizCode: 'ziying' })
 * ```
 */
export const useFxdStore = defineStore('fxd', {
  /**
   * 分销商状态
   * @returns {Object} 分销商相关的状态对象
   */
  state: () => ({
    /**
     * 能人ID
     * @type {string}
     * @description 用于查看能人收益和订单的唯一标识
     */
    salesmanId: '',

    /**
     * 发展人ID
     * @type {string}
     * @description 能人也属于发展人，用于用户购买商品时数据统计
     */
    developerId: '',

    /**
     * 分销业务代码
     * @type {string}
     * @description 当前选中的分销业务类型代码
     */
    distribizCode: '',

    /**
     * 分销业务代码列表
     * @type {string[]}
     * @description 支持的分销业务类型列表
     */
    distribizCodeList: ['ziying', 'st-dbgj'],

    /**
     * 收益信息
     * @type {Object}
     * @description 分销商的收益统计信息
     */
    info: {
      /**
       * 预估收益
       * @type {string}
       * @description 预估的收益金额（元）
       */
      expect: '0.00',

      /**
       * 实际收益
       * @type {string}
       * @description 已到账的实际收益金额（元）
       */
      real: '0.00',

      /**
       * 订单数量
       * @type {string}
       * @description 产生收益的订单数量
       */
      count: '0'
    },

    /**
     * 订单列表
     * @type {Array}
     * @description 分销商的订单列表数据
     */
    orderList: [],

    /**
     * 有效性状态
     * @type {boolean}
     * @description 当前分销商状态是否有效
     */
    isValid: false
  }),
  actions: {
    /**
     * 设置能人ID和分销业务代码
     * @description 设置能人ID和分销业务代码，并同步到sessionStorage
     * @param {Object} payload - 设置参数
     * @param {string} payload.salesmanId - 能人ID
     * @param {string} payload.distribizCode - 分销业务代码
     * @example
     * ```javascript
     * setSalesmanId({
     *   salesmanId: 'salesman123',
     *   distribizCode: 'ziying'
     * })
     * ```
     */
    setSalesmanId(payload) {
      this.salesmanId = payload.salesmanId
      this.distribizCode = payload.distribizCode
      storage.setItem('salesmanId', payload.salesmanId)
      storage.setItem('distribizCode', payload.distribizCode)
    },
    /**
     * 设置收益信息
     * @description 更新分销商的收益统计信息，金额从分转换为元
     * @param {Object} payload - 收益信息对象
     * @param {number} [payload.estimatedIncome] - 预估收益（分）
     * @param {number} [payload.payedIncome] - 实际收益（分）
     * @param {number} [payload.estimatedIncomeCount] - 订单数量
     * @example
     * ```javascript
     * setIncomeInfo({
     *   estimatedIncome: 12500, // 125.00元
     *   payedIncome: 10000,     // 100.00元
     *   estimatedIncomeCount: 5
     * })
     * ```
     */
    setIncomeInfo(payload) {
      payload.estimatedIncome && (this.info.expect = (payload.estimatedIncome / 100).toFixed(2))
      payload.payedIncome && (this.info.real = (payload.payedIncome / 100).toFixed(2))
      payload.estimatedIncomeCount && (this.info.count = payload.estimatedIncomeCount)
    },
    /**
     * 设置订单列表
     * @description 将后台接口返回的订单数据转换为前端页面所需的格式
     * @param {Array} payload - 后台返回的原始订单数据
     * @example
     * ```javascript
     * setOrderList([
     *   {
     *     orderId: '123456',
     *     grandResultBz: '1',
     *     supplierOrder: [{
     *       supplierOrderId: 'SUP123',
     *       supplierOrderState: '1',
     *       price: 10000, // 分
     *       commission: 500 // 分
     *     }]
     *   }
     * ])
     * ```
     */
    setOrderList(payload) {
      // 数据转换，将后台接口数据转换成前端页面数据
      const list = payload.map(item => {
        let count = 0
        let supplierName = ''
        let totalAmt = 0
        const supplier = item.supplierOrder[0] // 目前一个主订单下只有一个供应商订单，因此取第一个数据即可

        if (!supplier) {
          return {}
        }
        const _item = {
          parentId: item.orderId,
          grandResultBz: item.grandResultBz, // 佣金是否发放：0未申请  1申请成功 2已发放
          id: supplier.supplierOrderId,
          key: `${item.orderId}_${supplier.supplierOrderId}`,
          name: supplierName,
          status: supplier.supplierOrderState, // 订单状态：1 待收货；2 已签收；3 已取消
          totalCount: '',
          totalAmt: supplier.price / 100,
          totalIncome: supplier.commission / 100,
          list: (supplier.skuNumInfo || []).map(item => {
            count += Number(item.skuNum)
            if (item.sku.supplierName) supplierName = item.sku.supplierName
            const promotion = item.sku.skuPromotionList && item.sku.skuPromotionList[0]

            if (promotion) {
              totalAmt += (promotion.promotionPrice / 100) * item.skuNum
            } else {
              totalAmt += (item.sku.price / 100) * item.skuNum
            }

            let price = item.sku.price / 100
            if (item.loanOrder && item.loanOrder.loanProduct && item.loanOrder.loanProduct.loanAmount) {
              // 花呗支付，展示借款金额
              price = item.loanOrder.loanProduct.loanAmount / 100
            } else if (promotion && promotion.promotionPrice) {
              // 促销产品，展示促销价格
              price = promotion.promotionPrice / 100
            }

            return {
              id: item.sku.skuId,
              img: item.sku.detailImageUrl,
              name: item.sku.name,
              price,
              count: item.skuNum,
              // 后台不返回是否是推广商品的状态，目前一个订单只有一个商品，
              // 因此根据订单的佣金 commission 来判断
              type: Number(supplier.commission) === 0 ? '0' : '1'
            }
          })
        }

        if (totalAmt) _item.totalAmt = totalAmt
        if (item.loanOrder && item.loanOrder.loanProduct && item.loanOrder.loanProduct.loanAmount) {
          // 花呗订单需要展示借款金额
          _item.totalAmt = item.loanOrder.loanProduct.loanAmount / 100
        }
        if (_item.status === '2' || _item.status === '10') {
          // 已取消（未支付）、已退款订单，需要将佣金收益重置为 0
          _item.totalIncome = 0
        }
        _item.name = supplierName
        _item.totalCount = count

        return _item
      }).filter(item => !!item.id)

      this.orderList = Object.freeze(list)
    },
    /**
     * 设置发展人ID
     * @description 设置发展人ID并同步到本地存储
     * @param {Object} payload - 设置参数
     * @param {string} payload.developerId - 发展人ID
     * @example
     * ```javascript
     * setDeveloperId({ developerId: 'developer123' })
     * ```
     */
    setDeveloperId(payload) {
      this.developerId = payload.developerId || ''
      curDeveloperId.set(this.developerId)
    },
    /**
     * 查询能人ID
     * @description 根据fxdtoken查询能人ID，如果没有token则从缓存读取
     * @param {Object} [payload] - 查询参数
     * @param {string} [payload.fxdtoken] - 分销token
     * @param {string} [payload.distribizCode] - 分销业务代码
     * @example
     * ```javascript
     * // 使用token查询
     * await querySalesmanId({ fxdtoken: 'token123', distribizCode: 'ziying' })
     *
     * // 从缓存读取
     * await querySalesmanId()
     * ```
     */
    async querySalesmanId(payload) {
      let salesmanId = ''
      let distribizCode = ''

      if (payload && payload.fxdtoken) {
        distribizCode = payload.distribizCode || ''
        const [, res] = await checkSignature({
          fxdtoken: payload.fxdtoken,
          distribizCode: distribizCode || 'st-dbgj'
        })

        if (res && res.code === '0000') {
          salesmanId = res.data
        }
        // mock
        // salesmanId = 'fxd248dc6433f65ffa25c2e79f6bf4631f3'
      } else {
        salesmanId = storage.getItem('salesmanId') || ''
        distribizCode = storage.getItem('distribizCode') || ''
      }

      this.setSalesmanId({ salesmanId, distribizCode })
    },
    /**
     * 查询收益信息
     * @description 查询分销商的收益统计信息，支持指定业务代码或查询全部
     * @param {Object} payload - 查询参数
     * @param {string} [payload.fxdtoken] - 分销token
     * @param {string} [payload.distribizCode] - 分销业务代码
     * @example
     * ```javascript
     * // 查询指定业务代码的收益
     * await queryIncomeInfo({ fxdtoken: 'token123', distribizCode: 'ziying' })
     *
     * // 查询全部业务代码的收益
     * await queryIncomeInfo({ fxdtoken: 'token123' })
     * ```
     */
    async queryIncomeInfo(payload) {
      await this.querySalesmanId({ fxdtoken: payload.fxdtoken, distribizCode: payload.distribizCode })

      if (!this.salesmanId) return

      if (this.distribizCode) {
        // 指定了 distribizCode
        const [err, json] = await queryIncomeInfo({ shopCode: this.salesmanId, distribizCode: this.distribizCode })

        if (!err) {
          this.setIncomeInfo(json || {})
        }
      } else {
        // 未指定 distribizCode，后台不支持查询全部，只能遍历查询
        const dataLists = await Promise.all(this.distribizCodeList.map(code => {
          return queryIncomeInfo({
            shopCode: this.salesmanId,
            distribizCode: code
          }).then(res => {
            const [err, json] = res
            return !err && json ? json : {}
          })
        }))

        const data = dataLists.reduce((acc, curr) => {
          return {
            estimatedIncome: Number(acc.estimatedIncome) + Number(curr.estimatedIncome),
            payedIncome: Number(acc.payedIncome) + Number(curr.payedIncome),
            estimatedIncomeCount: Number(acc.estimatedIncomeCount) + Number(curr.estimatedIncomeCount)
          }
        })
        this.setIncomeInfo(data)
      }
    },
    /**
     * 查询订单列表
     * @description 查询分销商的订单列表，支持按订单状态筛选
     * @param {Object} payload - 查询参数
     * @param {string} payload.orderType - 订单状态类型
     * @example
     * ```javascript
     * // 查询待收货订单
     * await queryOrderList({ orderType: '1' })
     *
     * // 查询已签收订单
     * await queryOrderList({ orderType: '2' })
     * ```
     */
    async queryOrderList(payload) {
      await this.querySalesmanId()

      if (!this.salesmanId) return

      if (this.distribizCode) {
        // 指定了 distribizCode
        const [err, json] = await queryOrderList(
          { shopCode: this.salesmanId, orderState: payload.orderType, distribizCode: this.distribizCode })

        if (!err && json) {
          this.setOrderList(json || [])
        }
      } else {
        // 未指定 distribizCode，后台不支持查询全部，只能遍历查询
        const dataLists = await Promise.all(this.distribizCodeList.map(code => {
          return queryOrderList({
            shopCode: this.salesmanId,
            orderState: payload.orderType,
            distribizCode: code
          }).then(res => {
            const [err, json] = res
            return !err && json ? json : []
          })
        }))

        const list = dataLists.reduce((acc, curr) => {
          return acc.concat(curr)
        }, []).sort((prev, next) => next.orderId - prev.orderId)
        this.setOrderList(list)
      }
    },
    /**
     * 获取发展人ID
     * @description 从本地存储获取发展人ID并设置到状态中
     * @example
     * ```javascript
     * await getDeveloperId()
     * console.log(fxdStore.developerId) // 输出缓存的发展人ID
     * ```
     */
    async getDeveloperId() {
      const cache = curDeveloperId.get()
      if (cache) {
        this.setDeveloperId({ developerId: cache })
      }
    }
  }
})
