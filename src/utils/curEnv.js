import { warn } from 'commonkit'
import { get } from 'es-toolkit/compat'
import { curDistriBiz } from '@utils/storage.js'

/**
 * 分销业务映射配置
 * @typedef {Object} DistriBizConfig
 * @property {string} DEFAULT - 默认值，用于业务区分、分享等
 * @property {string} QUERY - 查询公告、ICON等接口调用使用
 * @property {string} GOODS - 查询商品及商品展示
 * @property {string} ORDER - 购物车及下单
 */

// 默认空白字符
const EMPTY_CHAR = '\u200E'

/**
 * 创建具有相同值的分销业务配置
 * @param {string} code - 业务码
 * @returns {DistriBizConfig} 分销业务配置
 */
const createUniformBizConfig = (code) => ({
  DEFAULT: code,
  QUERY: code,
  GOODS: code,
  ORDER: code
})

/**
 * 分销业务码映射表
 * @type {Object.<string, DistriBizConfig>}
 */
const DISTRIBIZ_MAP = {
  // 沃百富自营
  ziying: createUniformBizConfig('ziying'),
  // 助农（扶贫）
  fupin: createUniformBizConfig('fupin'),
  // 员工慰问福利
  welfaresop: createUniformBizConfig('welfaresop'),
  // 福利汇
  fulihui: createUniformBizConfig('fulihui'),
  // 京东自营（京东开普勒）
  jdkpl: createUniformBizConfig('jdkpl'),
  // 数字乡村
  szxc: {
    DEFAULT: 'szxc',
    QUERY: 'szxc',
    GOODS: 'szxc',
    ORDER: 'ziying'
  },
  // 省分助农
  sfzn: {
    DEFAULT: 'sfzn',
    QUERY: 'welfaresop',
    GOODS: 'welfaresop',
    ORDER: 'fupin'
  },
  // 劳保商城
  labor: createUniformBizConfig('labor'),
  // 京东商城
  ygjd: createUniformBizConfig('ygjd'),
  // 联农智选
  lnzx: createUniformBizConfig('lnzx'),
  // 政企商城
  zq: createUniformBizConfig('zq')
}

/**
 * 获取当前模块对应的分销业务码
 * @param {string} [module] - 模块名称
 * @returns {string} 分销业务码
 */
export const getBizCode = module => {
  const bizCode = curDistriBiz.get()
  const maps = DISTRIBIZ_MAP[bizCode]

  if (!maps) {
    return ''
  }

  if (module) {
    if (maps[module]) {
      return maps[module]
    }

    warn(`getBizCode 不存在模块：${module}`)
    return maps.DEFAULT
  }

  return bizCode ? maps.DEFAULT : ''
}

/**
 * 渠道标题映射表
 * @type {Object.<string, string>}
 */
const BIZ_TITLE_MAP = {
  ziying: '沃百富商城',
  fupin: '帮扶商城',
  welfaresop: EMPTY_CHAR, // 员工慰问
  fulihui: '福利汇',
  jdkpl: '京东自营',
  szxc: '数字乡村商城频道',
  sfzn: '省分助农',
  labor: '劳保商城',
  ygjd: '京东商城',
  lnzx: '联农智选',
  zq: '政企商城'
}

/**
 * 获取当前渠道标题
 * @returns {string} 渠道标题
 */
export const getBizTitle = () => {
  const bizCode = getBizCode()
  return BIZ_TITLE_MAP[bizCode] || EMPTY_CHAR
}

/**
 * 商城名称映射表
 * @type {Object.<string, string>}
 */
const MALL_NAME_MAP = {
  ziying: '沃百富商城',
  fupin: '帮扶商城',
  welfaresop: '沃百富商城',
  fulihui: '福利汇',
  jdkpl: '京东自营',
  szxc: '数字乡村商城频道',
  sfzn: '省分助农',
  labor: '福利商城',
  ygjd: '京东商城',
  lnzx: '联农智选'
}

/**
 * 获取当前渠道商城名称
 * @returns {string} 商城名称
 */
export const getMallName = () => {
  const bizCode = getBizCode()
  return MALL_NAME_MAP[bizCode] || EMPTY_CHAR
}

/**
 * 供应商编码映射表
 * @type {Object.<string, string>}
 */
export const supplierNameMap = {
  wqbzyshds: '华盛',
  wqbzyshds1: '华盛',
  bjhbkjyxgs1: '天猫优购',
  bjhbkjyxgs: '天猫优购',
  jd_wbf: '京东',
  jd_yg: '京东',
  jd: '京东',
  xgwybjdzswyxgs: '星光物语'
}

/**
 * 获取供应商名称
 * @param {string} supplierCode - 供应商编码
 * @returns {string} 供应商名称
 */
export const getSupplierName = (supplierCode) => {
  return get(supplierNameMap, supplierCode, '其他')
}
