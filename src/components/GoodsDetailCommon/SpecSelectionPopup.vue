<!--
/**
 * 商品规格选择弹窗组件
 *
 * 主要功能：
 * 1. 底部弹窗形式展示商品规格选择界面
 * 2. 显示商品基本信息（图片、价格、库存等）
 * 3. 提供多规格选择功能，支持多组规格
 * 4. 数量选择器，支持最小/最大购买限制
 * 5. 购买限制提示（库存、限购、起购等）
 * 6. 商品编码复制功能
 * 7. 多种操作模式（加购物车、立即购买、确认）
 *
 * 技术特点：
 * - Vant弹窗组件封装，支持底部滑出动画
 * - 响应式数据绑定，实时更新商品信息
 * - 防抖处理数量变更，避免频繁触发
 * - 完整的规格选择验证逻辑
 * - 剪贴板集成，支持一键复制
 * - 灵活的操作按钮配置
 *
 * 使用场景：
 * - 商品详情页的规格选择
 * - 购物车商品规格修改
 * - 快速购买流程
 */
-->

<template>
  <!-- 规格选择弹窗容器 -->
  <van-popup :show="visible" position="bottom" round @close="handleClose"
    @update:show="(value) => emit('update:visible', value)">
    <div class="spec-popup">
      <!-- 弹窗头部 -->
      <div class="popup-header">
        <h3 class="popup-title">选择规格</h3>
        <!-- 关闭按钮 -->
        <div class="close-btn" @click="handleClose">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6L18 18" stroke="#999" stroke-width="2" stroke-linecap="round" />
          </svg>
        </div>
      </div>

      <!-- 商品信息展示区域 -->
      <div class="goods-info">
        <!-- 商品详细信息 -->
        <div class="goods-detail">
          <!-- 商品图片 -->
          <img :src="goodsInfo.image" alt="商品图片" class="goods-image" />
          <!-- 商品内容区域 -->
          <div class="goods-content">
            <!-- 价格显示 -->
            <PriceDisplay :price="goodsInfo.price" size="large" color="orange" />
            <!-- 数量选择区域 -->
            <div class="quantity-section">
              <!-- 数量步进器 -->
              <van-stepper class="quantity-stepper" v-model="quantity" :min="quantityRange.min" :max="quantityRange.max"
                :disabled="quantityRange.max === 0" @change="handleQuantityChange" />
              <!-- 购买限制提示 -->
              <div class="purchase-limit">{{ purchaseLimitText }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 规格选择区域 -->
      <div class="spec-section">
        <!-- 规格头部信息 -->
        <div class="spec-header">
<!--          <span class="spec-title">规格</span>-->
          <!-- 商品编码复制功能 -->
          <div class="goods-code" v-if="goodsInfo.supplierSkuId">
            <span class="code-label">商品编码：</span>
            <span class="code-value">{{ goodsInfo.supplierSkuId }}</span>
            <img src="./assets/copy.png" alt="复制" class="copy-icon" width="16" height="16" @click="handleCopyCode" />
          </div>
        </div>

        <!-- 规格选项列表 -->
        <div class="spec-options">
          <!-- 规格组循环 -->
          <div class="radio-wrapper" v-for="(specs, groupIndex) in displaySpecsList" :key="groupIndex">
            <!-- 规格组标题 -->
            <div class="spec-group-title" v-if="specs.length > 0 && hasSpecs">
              {{ getSpecGroupName(groupIndex) }}
            </div>
            <!-- 规格选项按钮 -->
            <button v-for="(spec, specIndex) in specs" :key="specIndex"
              :class="{ active: specOptions.curSpecs.indexOf(spec) >= 0, disabled: specOptions.curDisabledSpecs.indexOf(spec) >= 0 }"
              @click="selectSpec(spec)">
              {{ removeSpecPrefix(spec) }}
            </button>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <WoActionBar>
        <div class="action-buttons">
          <!-- 确定按钮（修改规格或快速购买模式） -->
          <WoButton v-if="actionType === 1 || actionType === 2" block type="gradient" size="large" class="confirm-btn"
            :disabled="cartButtonDisabled" @click="handleConfirm">
            确定
          </WoButton>
          <!-- 双按钮模式（加购物车 + 立即购买） -->
          <template v-else-if="actionType === 0">
            <WoButton type="gradient" size="large" class="add-cart-btn" :disabled="cartButtonDisabled"
              @click="handleAddToCart">
              加入购物车
            </WoButton>
            <WoButton type="gradient" size="large" class="buy-now-btn" :disabled="cartButtonDisabled"
              @click="handleBuyNow">
              立即购买
            </WoButton>
          </template>
        </div>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
import { computed, watch, nextTick, toRefs, shallowRef } from 'vue'
import { debounce } from 'es-toolkit'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import useClipboard from 'vue-clipboard3'
import { showToast } from 'vant'
import { removeSpecPrefix } from '@utils/goodsDetail.js'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 弹窗显示状态
  visible: {
    type: Boolean,
    required: true
  },
  // 收货地址信息
  addressInfo: {
    type: Object,
    required: true
  },
  // 商品基本信息
  goodsInfo: {
    type: Object,
    required: true
  },
  // 规格选择相关数据
  specOptions: {
    type: Object,
    required: true
  },
  // 初始购买数量
  initialQuantity: {
    type: Number,
    required: true
  },
  // 操作类型：0-正常购买，1-修改规格，2-快速购买
  actionType: {
    type: Number,
    required: true,
    validator: (value) => value === 1 || value === 2 || value === 0
  },
  // 购物车按钮禁用状态
  cartButtonDisabled: {
    type: Boolean,
    required: true
  }
})

// 定义组件事件
const emit = defineEmits(['update:visible', 'select-spec', 'add-to-cart', 'buy-now', 'confirm', 'quantity-change'])

// 使用toRefs解构props，保持响应性
const {
  visible,
  goodsInfo,
  specOptions,
  initialQuantity,
  actionType,
  cartButtonDisabled
} = toRefs(props)

// ===================== 状态管理 ======================
// 当前选择的购买数量
const quantity = shallowRef(initialQuantity.value)

// 剪贴板功能实例
const { toClipboard } = useClipboard()

// ===================== 计算属性 ======================
// 购买限制提示文本
const purchaseLimitText = computed(() => {
  const { xgObj, lowestBuyObj, purchaseLimitText, stock } = goodsInfo.value

  const messages = []

  if (stock <= 0) {
    messages.push('暂无库存')
  }

  if (lowestBuyObj?.isLowestBuy) {
    messages.push(lowestBuyObj.lowestBuyText)
  }

  if (xgObj?.isXg && xgObj?.limitText) {
    messages.push(xgObj.limitText)
  }

  if (purchaseLimitText) {
    messages.push(purchaseLimitText)
  }

  return messages.join('，')
})

// 购买数量范围计算
const quantityRange = computed(() => {
  const { xgObj, lowestBuyObj, stock } = goodsInfo.value

  let min = 1
  if (lowestBuyObj?.isLowestBuy) {
    min = lowestBuyObj.lowestBuyNum
  }

  let max = 999

  if (stock > 0) {
    max = Math.min(max, stock)
  }

  if (xgObj?.isXg && xgObj?.limitNum) {
    max = Math.min(max, xgObj.limitNum)
  }

  return { min, max }
})

// 是否有规格选项
const hasSpecs = computed(() => {
  const specs = specOptions.value
  return specs && specs.specsList && specs.specsList.length > 0 && specs.specsList[0].length > 0
})

// 显示的规格列表（包含默认规格处理）
const displaySpecsList = computed(() => {
  return hasSpecs.value ? specOptions.value.specsList : [['默认规格']]
})

// ===================== 工具函数 ======================
// 复制商品编码到剪贴板
const handleCopyCode = async () => {
  try {
    await toClipboard(goodsInfo.value.supplierSkuId)
    showToast('复制成功');
  } catch (error) {
    console.error('复制失败:', error)
    showToast('复制失败');
  }
}

// 获取规格组名称
const getSpecGroupName = (groupIndex) => {
  return hasSpecs.value ? `规格${groupIndex + 1}` : '规格'
}

// 检查规格选择是否完整
const isSpecsComplete = () => {
  const { specsList = [], curSpecs = [] } = specOptions.value

  if (!hasSpecs.value) {
    return true
  }

  const validSpecsGroupCount = specsList.reduce((count, group) => {
    return group && group.length > 0 ? count + 1 : count
  }, 0)

  return validSpecsGroupCount === curSpecs.length
}

// 创建SKU信息对象
const createSkuInfo = () => ({
  quantity: quantity.value,
  selectedSpecs: specOptions.value.curSpecs,
  goodsId: goodsInfo.value.goodsId,
  supplierSkuId: goodsInfo.value.supplierSkuId,
  price: goodsInfo.value.price,
  stock: goodsInfo.value.stock,
  currSku: goodsInfo.value.currSku,
})
// ===================== 事件处理函数 ======================
// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 选择规格
const selectSpec = (spec) => {
  const { curDisabledSpecs, curSpecs } = specOptions.value

  if (curDisabledSpecs.indexOf(spec) >= 0) return

  if (spec === '默认规格' && curSpecs.indexOf(spec) >= 0) {
    return
  }

  emit('select-spec', spec)
}

// 数量变更处理（防抖）
const handleQuantityChange = debounce((value) => {
  const { min, max } = quantityRange.value
  const { xgObj, lowestBuyObj, stock } = goodsInfo.value

  if (value < min) {
    if (lowestBuyObj?.isLowestBuy) {
      showToast(`最少购买${min}件哦！`)
    }
    quantity.value = min
    return
  }

  if (value > max) {
    if (stock > 0 && value > stock) {
      showToast(`库存不足，仅剩${stock}件`)
    } else if (xgObj?.isXg && value > xgObj.limitNum) {
      showToast(`超出限购数量：${xgObj.limitText}`)
    }
    quantity.value = max
    return
  }

  quantity.value = value
}, 100)

// 加入购物车
const handleAddToCart = () => {
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  emit('add-to-cart', createSkuInfo())
}

// 立即购买
const handleBuyNow = () => {
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  emit('buy-now', createSkuInfo())
}

// 确认操作（根据actionType决定具体行为）
const handleConfirm = () => {
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  const skuInfo = createSkuInfo()

  if (actionType.value === 2) {
    emit('buy-now', skuInfo)
  } else {
    emit('add-to-cart', skuInfo)
  }
}

// ===================== 监听器 ======================
// 监听商品信息变化，自动调整数量范围
watch(goodsInfo, () => {
  const { min, max } = quantityRange.value

  if (quantity.value < min) {
    quantity.value = min
  }

  if (quantity.value > max) {
    quantity.value = max
  }
}, { deep: true })

// 监听初始数量变化
watch(initialQuantity, (newVal) => {
  const { min } = quantityRange.value
  quantity.value = Math.max(newVal, min)
})

// 监听数量变化，向父组件发送事件
watch(quantity, (newVal) => {
  emit('quantity-change', newVal)
})

// 监听弹窗显示状态，自动处理默认规格
watch(visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (!hasSpecs.value && specOptions.value.curSpecs.length === 0) {
        emit('select-spec', '默认规格')
      }
    })
  }
})


</script>

<style scoped lang="less">
.spec-popup {
  background-color: #FFFFFF;
  border-radius: 12px 12px 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 70px;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    flex-shrink: 0;

    .popup-title {
      font-size: 16px;
      color: #171E24;
      font-weight: 500;
      margin: 0;
    }

    .close-btn {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .goods-info {
    padding: 0 17px 10px 17px;
    flex-shrink: 0;
    box-sizing: border-box;

    .goods-basic {
      margin-bottom: 16px;

      .address-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;

        .address-content {
          flex: 1;

          .receiver-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 4px;

            .receiver-name {
              font-size: 14px;
              color: #4A5568;
              font-weight: 400;
            }

            .receiver-phone {
              font-size: 14px;
              color: #4A5568;
              font-weight: 400;
            }
          }

          .address-detail {
            .address-text {
              font-size: 15px;
              color: #171E24;
              font-weight: 600;
              line-height: 1.4;
            }
          }
        }

        .arrow-right {
          width: 6px;
          height: 12px;
          flex-shrink: 0;
          margin-left: 8px;
        }
      }
    }

    .goods-detail {
      display: flex;
      gap: 12px;

      .goods-image {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        object-fit: cover;
        flex-shrink: 0;
      }

      .goods-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .price-display {
          margin-bottom: 8px;
        }

        .quantity-section {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .quantity-stepper {
            flex-shrink: 0;
          }

          .purchase-limit {
            margin-left: 25px;
            font-size: 13px;
            color: var(--wo-biz-theme-color);
          }
        }
      }
    }
  }

  .spec-section {
    padding: 0 17px;
    flex: 1;
    overflow-y: auto;
    box-sizing: border-box;

    .spec-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .spec-title {
        font-size: 16px;
        color: #171E24;
        font-weight: 500;
      }

      .goods-code {
        display: flex;
        align-items: center;
        gap: 4px;

        .code-label {
          font-size: 12px;
          color: #999999;
        }

        .code-value {
          font-size: 12px;
          color: #999999;
        }

        .copy-icon {
          width: 10px;
          height: 10px;
          flex-shrink: 0;
          margin-left: 2px;
          cursor: pointer;
        }
      }
    }

    .spec-options {
      flex: 1;
      overflow-y: scroll;
      max-height: 300px;
      min-height: 150px;

      .radio-wrapper {
        line-height: 40px;

        .spec-group-title {
          font-size: 14px;
          color: #171E24;
          font-weight: 500;
          margin-bottom: 8px;
          margin-top: 16px;

          &:first-child {
            margin-top: 0;
          }
        }

        .specs-button-division-line {
          width: 100%;
          height: 1px;
          background: rgba(229, 229, 229, 0.64);
        }

        &:last-child .specs-button-division-line {
          height: 0;
        }

        button {
          display: inline-block;
          min-width: 75px;
          font-size: 13px;
          color: #171E24;
          line-height: 1.2;
          background: #F7F7F7;
          border-radius: 4px;
          padding: 8px 12px;
          margin-right: 8px;
          margin-bottom: 8px;
          outline: none;
          border: 1px solid transparent;
          cursor: pointer;
          transition: all 0.2s ease;

          &.active {
            background: rgba(255, 120, 10, 0.10);
            border: 1px solid var(--wo-biz-theme-color);
            color: var(--wo-biz-theme-color);
          }

          &.disabled {
            background: #F8F9FA;
            color: #CBD5E0;
            cursor: not-allowed;
          }
        }
      }
    }
  }

  .action-buttons {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .add-cart-btn {
      background-image: var(--wo-biz-theme-gradient-6);
    }

    .buy-now-btn {
      background-image: var(--wo-biz-theme-gradient-7);
    }
  }
}
</style>
