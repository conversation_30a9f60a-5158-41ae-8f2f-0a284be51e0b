<!--
/**
 * 商品介绍组件
 *
 * 主要功能：
 * 1. 智能展示商品介绍内容，支持HTML富文本和图片列表两种模式
 * 2. 自适应响应式缩放，确保内容在不同屏幕尺寸下正确显示
 * 3. 动态计算HTML内容尺寸并进行缩放处理
 * 4. 图片懒加载和错误处理，优化页面性能
 * 5. 加载状态指示，提升用户体验
 *
 * 技术特点：
 * - 复杂的DOM尺寸计算和缩放算法
 * - 防抖处理窗口大小变化和内容更新
 * - 深度样式处理，移除内联样式确保响应式效果
 * - 多重重试机制，确保内容正确渲染
 * - 图片加载完成检测，精确计算最终尺寸
 *
 * 使用场景：
 * - 商品详情页的商品介绍展示
 * - 需要自适应显示富文本内容的场景
 * - 商品图片介绍的瀑布流展示
 */
-->

<template>
  <!-- 商品介绍容器 -->
  <div class="goods-introduce">
    <!-- 标题区域 -->
    <h2 class="goods-introduce__title">商品信息</h2>

    <!-- 内容展示区域，支持动态高度调整 -->
    <div ref="contentWrapperRef" class="goods-introduce__content" :style="contentWrapperStyle">
      <!-- 加载状态指示器 -->
      <!-- 仅在有HTML内容且正在计算尺寸时显示 -->
      <div v-if="hasHtmlContent && isCalculating" class="goods-introduce__loading">
        <div class="goods-introduce__loading-spinner"></div>
      </div>

      <!-- HTML富文本内容展示 -->
      <!-- 当存在HTML介绍内容时显示，支持动态缩放 -->
      <div v-if="hasHtmlContent" v-html="htmlContent" ref="htmlContentRef" class="goods-introduce__html-content"
        :style="scaleStyle" />

      <!-- 图片列表展示 -->
      <!-- 当没有HTML内容时，展示图片列表形式的介绍 -->
      <div v-else class="goods-introduce__image-list">
        <img v-for="(imageUrl, index) in imageUrls" :key="index" :src="imageUrl" :alt="`商品介绍图片${index + 1}`"
          loading="lazy" decoding="async" class="goods-introduce__image" @error="handleImageError" />
      </div>
    </div>

    <!-- 营业执照图片 -->
    <img src="./assets/businessLicense.png" alt="营业执照" class="goods-introduce__license" loading="lazy"
         decoding="async" />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick, toRefs, watchEffect } from 'vue'
import { debounce } from 'es-toolkit'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 当前选中的SKU对象，包含商品介绍相关数据
  currentSKU: {
    type: Object,
    default: () => ({})
  }
})

// 使用toRefs解构props，保持响应性
// 解构后可以直接使用currentSKU，同时保持响应式特性
const { currentSKU } = toRefs(props)

// ===================== DOM引用和状态管理 ======================
// 内容包装器的DOM引用，用于获取容器尺寸
const contentWrapperRef = ref(null)
// HTML内容的DOM引用，用于计算实际内容尺寸
const htmlContentRef = ref(null)

// 内容缩放比例，用于响应式适配不同屏幕尺寸
const scale = ref(1)
// 内容容器的动态高度，根据缩放后的尺寸计算
const contentHeight = ref('auto')
// 是否正在计算尺寸的状态标识
const isCalculating = ref(false)

// ===================== 计算属性 ======================
// 判断是否存在HTML格式的商品介绍内容
const hasHtmlContent = computed(() => Boolean(currentSKU.value?.introduction))

// 处理图片列表，将字符串格式的图片URL列表转换为数组
const imageUrls = computed(() => {
  const introduceList = currentSKU.value?.introduceList
  if (!introduceList) return []

  // 确保introduceList为字符串格式
  const urlString = typeof introduceList === 'string'
    ? introduceList
    : String(introduceList)

  // 按逗号分割并过滤空值，得到图片URL数组
  return urlString.split(',').filter(Boolean)
})

// 获取HTML格式的商品介绍内容
const htmlContent = computed(() => currentSKU.value?.introduction || '')

// HTML内容的缩放样式，包含变换、透明度和过渡效果
const scaleStyle = computed(() => ({
  transform: `scale(${scale.value})`,
  transformOrigin: '0 0',
  opacity: isCalculating.value ? 0 : 1,
  transition: isCalculating.value ? 'none' : 'opacity 0.2s ease-in-out'
}))

// 内容包装器的样式，主要控制动态高度
const contentWrapperStyle = computed(() => ({
  height: contentHeight.value
}))

// ===================== 工具函数 ======================
// 递归移除元素的内联样式，确保响应式布局正确
// 特别处理图片和表格元素，设置为100%宽度自适应
const removeInlineStyles = (element) => {
  if (!element?.childNodes) return

  for (const child of element.childNodes) {
    // 只处理元素节点（nodeType === 1）
    if (child.nodeType === 1) {
      if (child.tagName === 'IMG' || child.tagName === 'TABLE') {
        // 图片和表格设置为100%宽度，高度自适应
        child.setAttribute('width', '100%')
        child.setAttribute('height', 'auto')
      } else {
        // 其他元素清除宽度样式
        child.style.width = ''
      }
      // 递归处理子元素
      removeInlineStyles(child)
    }
  }
}

// ===================== 核心业务逻辑 ======================
// 处理HTML内容的尺寸计算和缩放
// 这是组件的核心功能，确保内容在不同设备上正确显示
const processHtmlContent = async () => {
  isCalculating.value = true
  await nextTick()

  // 计算内容尺寸并应用缩放的核心函数
  const calculateDimensions = () => {
    // 按优先级查找目标元素，支持多种HTML结构
    const $el = htmlContentRef.value?.querySelector('.ssd-module-wrap') ||
      htmlContentRef.value?.querySelector('.ssd-module-mobile-wrap') ||
      htmlContentRef.value?.querySelector('.wrapper-jd-inner') ||
      htmlContentRef.value?.querySelector('.wrapper-jd-inner')?.firstElementChild ||
      htmlContentRef.value?.firstElementChild ||
      htmlContentRef.value

    if (!$el) {
      console.warn('未找到目标元素')
      return false
    }

    // 移除内联样式，确保响应式布局
    removeInlineStyles($el)

    // 获取元素的实际尺寸
    const contentComputedStyle = window.getComputedStyle($el)
    const realWidth = parseInt(contentComputedStyle.width)
    const realHeight = parseInt(contentComputedStyle.height)

    // 验证尺寸有效性
    if (!realWidth || !realHeight || realWidth <= 0 || realHeight <= 0) {
      console.warn('元素尺寸无效:', { realWidth, realHeight, display: contentComputedStyle.display, visibility: contentComputedStyle.visibility })
      return false
    }

    // 获取视口宽度并判断屏幕类型
    const clientWidth = document.documentElement.clientWidth
    // 计算缩放比例和缩放后的高度
    // 大屏和小屏使用相同的缩放逻辑
    const newScale = clientWidth / realWidth
    const scaleHeight = newScale * realHeight

    // 应用缩放设置
    scale.value = newScale
    contentHeight.value = `${scaleHeight}px`

    console.log('尺寸计算完成:', { realWidth, realHeight, scale: scale.value, contentHeight: contentHeight.value })
    isCalculating.value = false
    return true
  }

  // 多重重试机制，确保内容正确渲染
  // 首次尝试计算尺寸
  setTimeout(() => {
    if (!calculateDimensions()) {
      // 首次失败，等待更长时间后重试
      setTimeout(() => {
        if (!calculateDimensions()) {
          // 第二次失败，检查是否有图片需要加载
          const images = htmlContentRef.value?.querySelectorAll('img') || []
          if (images.length > 0) {
            // 等待所有图片加载完成后再次计算
            let loadedCount = 0
            const totalImages = images.length

            // 图片加载完成检查函数
            const checkAllLoaded = () => {
              loadedCount++
              if (loadedCount === totalImages) {
                // 所有图片加载完成，最后一次尝试计算
                setTimeout(() => {
                  if (!calculateDimensions()) {
                    isCalculating.value = false
                  }
                }, 50)
              }
            }

            // 为每个图片添加加载事件监听
            images.forEach(img => {
              if (img.complete) {
                // 图片已加载完成
                checkAllLoaded()
              } else {
                // 图片未加载，添加事件监听
                img.addEventListener('load', checkAllLoaded, { once: true })
                img.addEventListener('error', checkAllLoaded, { once: true })
              }
            })
          } else {
            // 没有图片，直接进行最后一次重试
            setTimeout(() => {
              if (!calculateDimensions()) {
                isCalculating.value = false
              }
            }, 200)
          }
        }
      }, 300)
    }
  }, 100)
}

// ===================== 事件处理函数 ======================
// 窗口大小变化处理，使用防抖优化性能
const handleResize = debounce(() => {
  if (hasHtmlContent.value) {
    processHtmlContent()
  }
}, 150)

// 图片加载错误处理，隐藏加载失败的图片
const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src)
  event.target.style.display = 'none'
}

// 使用防抖处理内容变化，避免频繁重新计算
const debouncedProcessHtml = debounce(processHtmlContent, 100)

// ===================== 监听器和生命周期 ======================
// 监听HTML内容变化，自动触发重新计算
watchEffect(() => {
  if (hasHtmlContent.value && htmlContent.value) {
    nextTick(() => {
      debouncedProcessHtml()
    })
  }
})

// 监听SKU介绍内容变化，重置状态并重新计算
watch(
  () => currentSKU.value?.introduction,
  (newIntroduction) => {
    if (newIntroduction) {
      // 重置所有状态到初始值
      scale.value = 1
      contentHeight.value = 'auto'
      isCalculating.value = true
      // 等待DOM更新后重新计算
      nextTick(() => {
        debouncedProcessHtml()
      })
    }
  }
)

// 组件挂载时初始化
onMounted(() => {
  if (hasHtmlContent.value) {
    processHtmlContent()
  }
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('resize', handleResize)
  // 取消防抖函数，避免内存泄漏
  handleResize.cancel()
  debouncedProcessHtml.cancel()
})
</script>

<style scoped lang="less">
.goods-introduce {
  width: 100%;
  font-size: 16px;
  contain: layout style paint;



  &__title {
    height: 43px;
    line-height: 43px;
    font-size: 15px;
    color: #171e24;
    text-align: center;
    font-weight: 500;
    margin: 0;
  }

  &__content {
    position: relative;
    overflow: hidden;
  }

  &__html-content {
    will-change: transform;

    :deep(img),
    :deep(table) {
      width: 100% !important;
      height: auto !important;
      max-width: 100%;
    }

    :deep(br) {
      display: none;
    }
  }

  &__image-list {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  &__image {
    display: block;
    width: 100%;
    height: auto;
    object-fit: cover;
  }

  &__license {
    display: block;
    width: 100%;
    max-width: 400px;
    height: auto;
    margin: 20px auto 0;
    object-fit: contain;
  }

  &__loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }

  &__loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--wo-biz-theme-color);
    border-top: 2px solid var(--wo-biz-theme-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
