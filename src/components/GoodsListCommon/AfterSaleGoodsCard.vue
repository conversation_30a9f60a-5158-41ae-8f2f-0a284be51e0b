<!--
/**
 * 售后商品卡片组件
 *
 * 主要功能：
 * 1. 展示售后订单中的商品信息，支持单商品和多商品两种展示模式
 * 2. 单商品模式：显示商品图片、名称、规格、价格和数量信息
 * 3. 多商品模式：以横向滚动方式展示多个商品图片，显示总价和总数量
 * 4. 支持自定义操作按钮区域，通过插槽提供灵活的操作功能
 * 5. 提供响应式图片尺寸设置和最小高度控制
 * 6. 集成价格显示组件，统一价格展示格式
 *
 * 技术特点：
 * - 根据商品数量自动切换单商品/多商品展示模式
 * - 支持图片懒加载和错误处理
 * - 使用插槽机制提供高度可定制的操作区域
 * - 响应式设计，适配不同屏幕尺寸
 *
 * 使用场景：
 * - 售后订单列表中的商品展示
 * - 退款、退货、换货等售后流程中的商品信息展示
 * - 需要展示商品详细信息和操作按钮的场景
 */
-->

<template>
  <!-- 售后商品卡片主容器 -->
  <!-- 根据是否有操作按钮添加对应样式类，设置最小高度确保布局一致性 -->
  <div
    class="goods-item"
    id="goods-item"
    :class="{ 'has-actions': showActions }"
    :style="{ minHeight: minHeight + 'px' }"
  >
    <!-- 商品详情展示区域 -->
    <div class="goods-detail">
      <!-- 多商品图片展示区域 -->
      <!-- 当商品数量大于1时，以横向滚动方式展示所有商品图片 -->
      <div class="multiple-goods-images" v-if="isMultipleGoods">
        <!-- 图片滚动容器，支持横向滚动浏览多个商品 -->
        <div class="images-scroll-container">
          <!-- 遍历多商品数据，为每个商品创建图片展示项 -->
          <div
            v-for="(goodsItem, index) in multiGoodsData"
            :key="index"
            class="single-goods-image"
            :style="imageStyle"
          >
            <!-- 商品图片，使用统一的图片处理函数获取图片URL -->
            <img :src="getImageUrl(goodsItem.detailImageUrl)" :alt="goodsItem.name" />
          </div>
        </div>
      </div>

      <!-- 单商品图片展示区域 -->
      <!-- 当只有一个商品时，显示单个商品图片 -->
      <div v-else class="single-goods-image" :style="imageStyle">
        <!-- 单商品图片展示 -->
        <img :src="getImageUrl(singleGoodsData.detailImageUrl)" :alt="item.name" />
      </div>

      <!-- 商品信息展示区域 -->
      <!-- 根据是否为单商品调整布局样式 -->
      <div class="goods-info" :class="{ 'single-goods-flex': !isMultipleGoods }">
        <!-- 单商品信息左侧区域：商品名称和规格 -->
        <!-- 只在单商品模式下显示，多商品模式不显示具体商品信息 -->
        <div class="goods-info-left" v-if="!isMultipleGoods">
          <!-- 商品名称展示 -->
          <div class="goods-name">{{ singleGoodsData.name }}</div>
          <!-- 商品规格参数展示，由多个参数字段组合而成 -->
          <div class="goods-spec">{{ singleGoodsData.params }}</div>
        </div>

        <!-- 商品信息右侧区域：价格和数量 -->
        <div class="goods-info-right">
          <!-- 价格展示组件，根据单商品/多商品模式显示对应价格 -->
          <PriceDisplay :price="displayPrice" size="small" class="goods-price" />
          <!-- 商品数量展示，显示总件数 -->
          <span class="goods-quantity">共{{ displayQuantity }}件</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <!-- 只在showActions为true时显示操作区域 -->
    <div v-if="showActions" class="goods-action">
      <!-- 左侧提示按钮组 -->
      <!-- 通过插槽提供自定义提示按钮，只在showTips为true时显示 -->
      <div v-if="showTips" class="action-group-left">
        <slot name="tips" :item="item">
          <!-- 默认提示内容插槽 -->
        </slot>
      </div>

      <!-- 右侧操作按钮组 -->
      <!-- 通过插槽提供自定义操作按钮 -->
      <div class="action-group-right">
        <slot name="actions" :item="item">
          <!-- 默认操作按钮插槽 -->
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { toRefs } from 'vue'
import { compact } from 'es-toolkit'
import PriceDisplay from '@components/Common/PriceDisplay.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 售后商品项数据对象，包含商品信息、SKU列表、价格等完整信息
  item: {
    type: Object,
    required: true
  },
  // 商品项唯一标识符，用于区分不同的商品卡片实例
  itemId: {
    type: [String, Number],
    default: Math.random()
  },
  // 商品图片显示尺寸，单位为像素，控制图片的宽度和高度
  imageSize: {
    type: Number,
    default: 90
  },
  // 商品卡片最小高度，单位为像素，确保卡片布局一致性
  minHeight: {
    type: Number,
    default: 135
  },
  // 是否显示操作按钮区域，控制底部操作区域的显示状态
  showActions: {
    type: Boolean,
    default: false
  },
  // 是否显示提示按钮，控制左侧提示按钮组的显示状态
  showTips: {
    type: Boolean,
    default: true
  }
})

// 使用toRefs解构props，保持响应性
// 确保在组件内部使用这些值时能够正确响应props的变化
const { item, itemId, imageSize, minHeight, showActions, showTips } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['select-action'])

// ==================== 图片处理工具函数 ====================
// 图片URL处理函数，统一处理不同格式的图片数据
// 支持数组格式和字符串格式的图片URL，确保返回有效的图片地址
const getImageUrl = (imageUrl) => {
  // 如果图片URL是数组格式，取第一个有效的图片地址
  if (Array.isArray(imageUrl)) {
    return imageUrl.length > 0 ? imageUrl[0] : ''
  }
  // 如果是字符串格式，直接返回，空值时返回空字符串
  return imageUrl || ''
}

// 计算图片样式对象，根据传入的图片尺寸设置宽度和高度
// 确保所有商品图片保持统一的显示尺寸
const imageStyle = computed(() => ({
  width: imageSize.value + 'px',
  height: imageSize.value + 'px'
}))

// ==================== 商品数据处理和计算 ====================
// 计算标准化的商品列表数据
// 兼容新旧两种数据结构，统一数据格式便于后续处理
const goodsList = computed(() => {
  // 从商品项中获取SKU信息列表，优先使用skuNumInfoList，其次使用skuList
  const { skuNumInfoList, skuList } = item.value
  const list = skuNumInfoList || skuList

  // 如果没有商品列表数据或数据格式不正确，返回空数组
  if (!list || !Array.isArray(list)) {
    return []
  }

  // 标准化商品数据格式，兼容新旧数据结构
  return list.map(listItem => {
    if (listItem.sku) {
      // 新数据结构：SKU信息嵌套在sku字段中，需要展平并添加数量信息
      return { ...listItem.sku, skuNum: listItem.skuNum }
    } else {
      // 旧数据结构：SKU信息直接在顶层，默认数量为1
      return { ...listItem, skuNum: '1' }
    }
  })
})

// 判断是否为多商品模式
// 根据商品列表长度判断显示模式，影响页面布局和信息展示方式
const isMultipleGoods = computed(() => {
  // 优先检查skuNumInfoList数组长度
  if (Array.isArray(item.value.skuNumInfoList)) {
    return item.value.skuNumInfoList.length > 1
  }
  // 其次检查skuList数组长度
  if (Array.isArray(item.value.skuList)) {
    return item.value.skuList.length > 1
  }
  // 都不存在时默认为单商品模式
  return false
})

// 多商品展示数据
// 在多商品模式下返回完整的商品列表，用于图片轮播展示
const multiGoodsData = computed(() => {
  const list = goodsList.value
  // 多商品情况下返回完整列表供图片滚动展示使用
  if (list.length > 1) {
    return list
  }
  // 单商品情况下返回第一个商品数据
  else if (list.length === 1) {
    return list[0]
  }
  // 无商品数据时返回null
  return null
})

// 单商品展示数据
// 始终返回第一个商品的详细信息，用于单商品模式的信息展示
const singleGoodsData = computed(() => {
  const list = goodsList.value
  if (list.length > 0) {
    const firstItem = list[0]
    // 组合商品规格参数，将多个参数字段合并为一个完整的规格描述
    // 使用compact函数过滤空值，然后用空格连接所有有效参数
    firstItem.params = compact([
      firstItem.param,
      firstItem.param1,
      firstItem.param2,
      firstItem.param3,
      firstItem.param4
    ]).join(' ')
    return firstItem
  }
  // 无商品数据时返回null
  return null
})

// ==================== 价格和数量计算 ====================
// 计算显示价格
// 根据单商品/多商品模式选择合适的价格字段进行展示
const displayPrice = computed(() => {
  if (isMultipleGoods.value) {
    // 多商品模式：显示总价，优先使用price字段，其次使用totalPrice字段
    return item.value.price || item.value.totalPrice
  }
  // 单商品模式：直接显示商品价格
  return item.value.price
})

// 计算显示数量
// 根据单商品/多商品模式计算总数量，确保数量信息的准确性
const displayQuantity = computed(() => {
  const list = goodsList.value

  if (isMultipleGoods.value) {
    // 多商品模式：累加所有商品的数量，得到总件数
    return list.reduce((sum, goods) => sum + (parseInt(goods.skuNum) || 0), 0)
  } else {
    // 单商品模式：返回第一个商品的数量，确保数值类型正确
    return list.length > 0 ? (parseInt(list[0].skuNum) || 0) : 0
  }
})

</script>

<style scoped lang="less">
.goods-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 20px;

  :deep(.van-popover__action) {
    width: 110px;
    height: 32px;
  }


  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .goods-detail {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .multiple-goods-images {
    width: 100%;
    margin-right: 10px;
    flex: 1;
    overflow-x: auto;
    scroll-behavior: smooth;

    .images-scroll-container {
      width: 100%;
      display: flex;
      gap: 2px;

      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        height: 2px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 1px;
      }

      .single-goods-image {
        border-radius: 4px;
        overflow: hidden;
        flex-shrink: 0;
        margin-right: 5px;

        &:last-child {
          margin-right: 0;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  .single-goods-image {
    border-radius: 4px;
    overflow: hidden;
    margin-right: 10px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .goods-info {
    display: flex;
    justify-content: flex-end;
    flex: 1;

    &.single-goods-flex {
      flex: 1;
    }

    .goods-info-left {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-right: 20px;
      flex-shrink: 0;

      .goods-name {
        font-size: 13px;
        color: #171E24;
        line-height: 1.5;
        margin-bottom: 7px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
      }

      .goods-spec {
        font-size: 11px;
        color: #718096;
        margin-bottom: 7px;
      }
    }

    .goods-info-right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .goods-price {
        margin-bottom: 7px;
      }

      .goods-quantity {
        font-size: 11px;
        color: #718096;
        margin-bottom: 7px;
      }
    }
  }

  .logistics-info {
    width: 100%;
    margin: 7px 0;

    .logistics-content {
      display: flex;
      align-items: center;
      font-size: 11px;
      color: #718096;
      line-height: 1.5;
      padding: 7px;
      box-sizing: border-box;
      background: #f5f5f5;
      border-radius: 4px;

      .logistics-label {
        margin-right: 4px;
        color: #4A5568;
      }

      .logistics-text {
        margin-right: 8px;
        color: #171E24;
      }

      .logistics-company,
      .logistics-number {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .goods-action {
    width: 100%;
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;

    .more-action {
      flex-shrink: 0;
    }

    .more-button {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #666666;
      cursor: pointer;
    }

    .action-group-left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }

    .action-group-right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }

  }
}
</style>
