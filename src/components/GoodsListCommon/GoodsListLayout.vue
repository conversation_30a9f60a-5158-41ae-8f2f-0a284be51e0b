<!--
/**
 * 商品列表布局组件
 *
 * 主要功能：
 * 1. 提供商品列表的统一布局容器，支持列表和瀑布流两种展示模式
 * 2. 集成骨架屏加载状态，提供良好的用户体验
 * 3. 支持无限滚动加载，自动处理分页逻辑
 * 4. 响应式设计，适配不同屏幕尺寸和设备
 * 5. 统一的商品交互事件处理（点击、加购等）
 * 6. 可自定义的空状态展示，支持自定义图片、描述和操作按钮
 *
 * 技术特点：
 * - 基于Vue 3 Composition API开发
 * - 支持列表模式（van-list）和瀑布流模式切换
 * - 集成Vant UI组件，保持设计一致性
 * - 使用事件发射机制与父组件通信
 * - 支持响应式断点配置
 * - 模块化的事件处理逻辑
 * - 支持空状态的完全自定义配置
 *
 * 布局模式：
 * - 列表模式：使用van-list组件，适合标准商品列表展示
 * - 瀑布流模式：使用WaterfallSection组件，适合图片类商品展示
 *
 * 空状态自定义：
 * - 支持自定义空状态图片（预设类型或自定义URL）
 * - 支持自定义空状态描述文字
 * - 支持添加自定义操作按钮（如重新加载、去购物等）
 * - 按钮支持完整的样式配置（类型、尺寸、颜色等）
 *
 * 使用场景：
 * - 商品分类页面的商品列表
 * - 搜索结果页面的商品展示
 * - 推荐商品的瀑布流展示
 * - 任何需要商品列表展示的页面
 */
-->

<template>
  <!-- 商品列表布局主容器 -->
  <div class="goods-list-layout">
    <!-- 骨架屏加载状态 -->
    <!-- 在数据加载时显示骨架屏，根据展示模式调整骨架屏数量 -->
    <GoodsSkeletonLoader v-if="isLoading" :is-waterfall="isWaterfall" :skeleton-count="isWaterfall ? 4 : 3" />

    <!-- 列表模式布局 -->
    <!-- 当有商品数据且非瀑布流模式时显示标准列表布局 -->
    <section v-show="goodsList.length > 0 && !isWaterfall" class="list-layout">
      <!-- van-list组件提供无限滚动加载功能 -->
      <!-- 监听滚动事件，自动触发数据加载和状态更新 -->
      <van-list :loading="loading" :finished="finished" finished-text="没有更多了" @load="handleLoadMore"
        @update:loading="handleUpdateLoading">
        <!-- 商品列表容器，使用无序列表结构 -->
        <ul class="goods-list-container">
          <!-- 遍历商品数据，为每个商品创建列表项 -->
          <!-- 使用skuId作为key确保列表项的唯一性和更新性能 -->
          <ProductListItem v-for="(item, index) in goodsList" :key="`goods-${item.skuId || index}`" :item="item"
            @item-click="handleItemClick" @add-cart="handleAddCart" />
        </ul>
      </van-list>
    </section>

    <!-- 瀑布流模式布局 -->
    <!-- 当有商品数据且为瀑布流模式时显示瀑布流布局 -->
    <section v-show="goodsList.length > 0 && isWaterfall" class="waterfall-layout">
      <!-- 瀑布流组件，提供瀑布流布局和滚动加载功能 -->
      <!-- 配置瀑布流的各种参数，包括加载状态、完成状态等 -->
      <WaterfallSection :waterfall-goods-list="goodsList" :waterfall-loading="loading" :waterfall-finished="finished"
        :waterfall-button-can-show="false" :waterfall-render-complete="true" :skeleton-states="{ waterfall: false }"
        load-mode="scroll" @load-more="handleWaterfallLoadMore">
        <!-- 瀑布流商品项插槽 -->
        <!-- 为每个商品提供瀑布流样式的展示组件 -->
        <template #item="{ item }">
          <GoodsWaterfallItem :item="item" @item-click="handleItemClick" @add-cart="handleAddCart" />
        </template>

        <!-- 瀑布流空状态插槽 -->
        <!-- 在瀑布流模式下的空状态展示 -->
        <template #empty>
          <section v-if="goodsList.length <= 0 && !isLoading && isWaterfall" class="empty-state">
            <WoEmpty :description="emptyDescription" :image="emptyImage" :image-size="emptyImageSize">
              <!-- 自定义按钮插槽 -->
              <WoButton v-if="emptyButton" :type="emptyButton.type || 'primary'" :size="emptyButton.size || 'large'"
                :block="emptyButton.block" :round="emptyButton.round" :disabled="emptyButton.disabled"
                @click="handleEmptyButtonClick">
                {{ emptyButton.text || '重新加载' }}
              </WoButton>
            </WoEmpty>
          </section>
        </template>
      </WaterfallSection>
    </section>

    <!-- 通用空状态展示 -->
    <!-- 当没有商品数据且不在加载状态且非瀑布流模式时显示空状态提示 -->
    <section v-if="goodsList.length <= 0 && !isLoading && !isWaterfall" class="empty-state">
      <WoEmpty :description="emptyDescription" :image="emptyImage" :image-size="emptyImageSize">
        <!-- 自定义按钮插槽 -->
        <WoButton v-if="emptyButton" :type="emptyButton.type || 'primary'" :size="emptyButton.size || 'large'"
          :block="emptyButton.block" :round="emptyButton.round" :disabled="emptyButton.disabled"
          @click="handleEmptyButtonClick">
          {{ emptyButton.text || '重新加载' }}
        </WoButton>
      </WoEmpty>
    </section>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
import WoEmpty from '@/components/WoElementCom/WoEmpty.vue'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import GoodsSkeletonLoader from './GoodsSkeletonLoader.vue'
import ProductListItem from './ProductListItem.vue'
import GoodsWaterfallItem from './GoodsWaterfallItem.vue'
import WaterfallSection from './WaterfallSection.vue'
import { getDefaultBreakpoints } from '@/config/responsive.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 商品列表数据数组，包含所有需要展示的商品信息
  goodsList: {
    type: Array,
    default: () => []
  },
  // 初始加载状态，控制骨架屏的显示
  isLoading: {
    type: Boolean,
    default: false
  },
  // 滚动加载状态，控制加载更多的显示
  loading: {
    type: Boolean,
    default: false
  },
  // 数据加载完成状态，控制是否还有更多数据
  finished: {
    type: Boolean,
    default: false
  },
  // 是否使用瀑布流布局模式
  isWaterfall: {
    type: Boolean,
    default: false
  },
  // 响应式断点配置，用于不同屏幕尺寸的适配
  breakpoints: {
    type: Object,
    default: () => getDefaultBreakpoints()
  },
  // 空状态时的描述文本
  emptyDescription: {
    type: String,
    default: '暂无商品'
  },
  // 空状态图片类型或自定义图片URL
  emptyImage: {
    type: String,
    default: 'default'
  },
  // 空状态图片尺寸
  emptyImageSize: {
    type: [Number, String],
    default: 120
  },
  // 空状态按钮配置
  emptyButton: {
    type: Object,
    default: () => null
  }
})

// 使用toRefs解构props，保持响应性
// 确保在组件内部使用这些值时能够正确响应props的变化
const {
  goodsList,
  isLoading,
  loading,
  finished,
  isWaterfall,
  emptyDescription,
  emptyImage,
  emptyImageSize,
  emptyButton
} = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['load-more', 'item-click', 'add-cart', 'update:loading', 'empty-button-click'])

// ==================== 列表模式事件处理 ====================
// 处理列表模式的加载更多事件
// 当用户滚动到底部时触发，通知父组件加载更多数据
const handleLoadMore = () => {
  emit('load-more')
}

// 处理列表模式的加载状态更新事件
// 同步van-list组件的loading状态到父组件
const handleUpdateLoading = (value) => {
  emit('update:loading', value)
}

// ==================== 瀑布流模式事件处理 ====================
// 处理瀑布流模式的加载更多事件
// 瀑布流滚动触底时触发，模拟van-list的update:loading以兼容父组件
const handleWaterfallLoadMore = () => {
  // 先设置loading状态为true，然后触发加载更多事件
  emit('update:loading', true)
  emit('load-more')
}

// ==================== 商品交互事件处理 ====================
// 处理商品点击事件
// 当用户点击商品时，将商品信息传递给父组件
const handleItemClick = (item) => {
  emit('item-click', item)
}

// 处理商品加购事件
// 当用户点击加购按钮时，将商品信息传递给父组件
const handleAddCart = (item) => {
  emit('add-cart', item)
}

// ==================== 空状态事件处理 ====================
// 处理空状态按钮点击事件
// 当用户点击空状态按钮时，将按钮信息传递给父组件
const handleEmptyButtonClick = () => {
  emit('empty-button-click', emptyButton.value)
}
</script>

<style scoped lang="less">
.goods-list-layout {
  .list-layout {
    margin-top: 2px;

    .goods-list-container {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .waterfall-layout {
    :deep(.home-waterfall-container) {
      padding: 0;
    }
  }

  .empty-state {
    padding: 40px 0;
  }
}
</style>
