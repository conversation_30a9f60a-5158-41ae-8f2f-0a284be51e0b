<!--
/**
 * 地址表单组件
 *
 * 主要功能：
 * 1. 提供完整的收货地址录入功能，包括收货人姓名、手机号、地区选择和详细地址
 * 2. 集成级联地区选择器，支持省市区街道四级地址选择
 * 3. 实现表单字段实时验证，提供即时的错误提示和用户反馈
 * 4. 支持地址数据的初始化填充，适用于新增和编辑场景
 * 5. 提供表单验证和数据构建功能，确保数据完整性和格式正确性
 * 6. 支持禁用状态，适应不同业务场景的交互需求
 *
 * 技术特点：
 * - 使用van-cascader组件实现地区级联选择
 * - 采用响应式表单验证，实时反馈用户输入状态
 * - 集成防抖处理，优化地区数据加载性能
 * - 支持v-model双向数据绑定
 * - 提供完整的生命周期管理和错误处理
 *
 * 使用场景：
 * - 电商订单收货地址填写
 * - 用户个人信息地址管理
 * - 企业客户地址信息录入
 */
-->

<template>
  <!-- 地址表单主容器 -->
  <div class="address-form-component">
    <!-- 表单字段区域，包含收货人信息和地址信息 -->
    <div class="form-section">
      <!-- 收货人姓名输入框 -->
      <!-- 支持实时验证，失焦时触发验证，输入时清除错误状态 -->
      <WoFormItem
        label="收货人"
        v-model="formData.recName"
        placeholder="请填写收货人姓名"
        :error="nameError"
        :error-message="nameErrorMessage"
        clearable
        @blur="validateName"
        @input="clearNameError"
      />

      <!-- 收货人手机号输入框 -->
      <!-- 限制最大长度11位，使用电话号码输入类型，支持实时验证 -->
      <WoFormItem
        label="手机号"
        v-model="formData.recPhone"
        maxlength="11"
        placeholder="请填写11位手机号"
        type="tel"
        :error="phoneError"
        :error-message="phoneErrorMessage"
        clearable
        @blur="validatePhone"
        @input="clearPhoneError"
      />

      <!-- 地区选择字段 -->
      <!-- 点击触发级联选择器弹窗，显示已选择的地区路径 -->
      <WoFormItem label="所在地区" @click="onSelectRegion" clickable>
        <template #input>
          <!-- 地区显示文本，未选择时显示占位符样式 -->
          <div class="address-form__region-value"
            :class="{ 'address-form__region-value--placeholder': !formData.region }">
            {{ formData.region || '请选择省市区' }}
          </div>
        </template>
        <template #rightIcon>
          <!-- 右侧箭头图标，点击同样触发地区选择 -->
          <div class="address-form__arrow-icon" @click="onSelectRegion"></div>
        </template>
      </WoFormItem>

      <!-- 详细地址输入框 -->
      <!-- 使用多行文本域，支持自动调整高度，实时验证地址长度 -->
      <WoFormItem
        label="详细地址"
        rows="2"
        type="textarea"
        autosize
        v-model="formData.addrDetail"
        placeholder="请填写详细地址，如街道、门牌号等"
        :error="addressError"
        :error-message="addressErrorMessage"
        clearable
        @blur="validateAddress"
        @input="clearAddressError"
      />
    </div>

    <!-- 地区选择级联弹窗 -->
    <!-- 从底部弹出，圆角样式，禁用懒加载确保数据及时显示 -->
    <van-popup v-model:show="showCascader" round position="bottom" :lazy-render="false">
      <!-- 级联选择器组件 -->
      <!-- 支持省市区街道四级选择，动态加载下级数据 -->
      <van-cascader
        class="address-form__cascader"
        v-model="cascaderValue"
        title="所选地区"
        :options="cascaderOptions"
        :field-names="cascaderFieldNames"
        @close="showCascader = false"
        @change="onCascaderChange"
        @finish="onCascaderFinish"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { reactive, ref, computed, watch, onMounted, toRefs } from 'vue'
import { debounce } from 'es-toolkit'
import WoFormItem from '@components/WoElementCom/WoFormItem.vue'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { queryAddrArea } from '@api/interface/address.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 初始地址数据，用于编辑模式下的数据回填
  initialData: {
    type: Object,
    default: () => ({})
  },
  // 是否禁用表单，禁用时不允许用户操作
  disabled: {
    type: Boolean,
    default: false
  }
})

// 使用toRefs解构props，保持响应性
const { initialData, disabled } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update:modelValue', 'validate', 'region-change'])

// ==================== 表单数据状态管理 ====================
// 表单数据响应式对象，包含所有地址相关字段
const formData = reactive({
  recName: '',    // 收货人姓名
  recPhone: '',   // 收货人手机号
  region: '',     // 地区文本描述，如"北京市/朝阳区/三里屯街道"
  addrDetail: '', // 详细地址，如门牌号、楼层等
  areaId: '',     // 最后一级地区ID，用于后端接口调用
  areaType: ''    // 最后一级地区类型，1-省 2-市 3-区 4-街道
})

// 表单整体验证状态计算属性
// 检查所有必填字段是否满足要求
const phoneRegex = /^1\d{10}$/
const isFormValid = computed(() => {
  return formData.recName.trim() &&
         phoneRegex.test(formData.recPhone.trim()) &&
         formData.region &&
         formData.addrDetail.trim().length >= 5
})

// 监听表单数据变化，向父组件发送更新事件
// 实现v-model双向数据绑定
watch(formData, (newData) => {
  emit('update:modelValue', { ...newData })
}, { deep: true })

// 监听初始数据变化，用于编辑模式下的数据回填
watch(initialData, (newData) => {
  if (newData.value && Object.keys(newData.value).length > 0) {
    fillAddressForm(newData.value)
  }
}, { immediate: true, deep: true })

// ==================== 级联选择器状态管理 ====================
// 级联选择器显示状态控制
const showCascader = ref(false)

// 级联选择器当前选中值（最后一级的areaId）
const cascaderValue = ref('')

// 级联选择器完整选择路径，包含省市区街道的完整对象数组
const cascaderValueDetails = ref([])

// 级联选择器数据源，树形结构存储地区数据
const cascaderOptions = ref([])

// 级联选择器字段映射配置
// 定义数据对象中哪些字段对应显示文本、值和子选项
const cascaderFieldNames = {
  text: 'areaName',     // 显示文本对应的字段名
  value: 'areaId',      // 值对应的字段名
  children: 'children'  // 子选项对应的字段名
}

// 地区选择按钮点击处理函数
// 检查禁用状态后显示级联选择器
const onSelectRegion = () => {
  if (disabled.value) return
  showCascader.value = true
}

// 级联选择器选项变化处理函数
// 当用户选择某一级时，动态加载下一级数据
const onCascaderChange = debounce(async ({ selectedOptions, tabIndex }) => {
  const selected = selectedOptions[tabIndex]

  // 如果当前节点已经加载过子节点，则不需要再次请求
  if (selected.children && selected.children.length > 0) {
    return
  }

  // 构建查询参数，获取下一级地区数据
  const area = JSON.stringify({
    areaId: selected.areaId,
    areaType: selected.areaType
  })

  try {
    showLoadingToast()
    const [err, json] = await queryAddrArea(area)
    closeToast()

    if (err) {
      showToast(err.msg || '查询失败')
      return
    }

    // 如果没有下一级数据，说明当前就是最后一级，直接完成选择
    if (!json || json.length === 0) {
      // 标记为叶子节点
      selected.children = null
      // 更新级联选项（通过创建新引用触发响应式更新）
      cascaderOptions.value = [...cascaderOptions.value]
      // 直接完成选择
      onCascaderFinish(selectedOptions)
      return
    }

    // 更新节点的子选项数据
    const updateNodeChildren = (options, path, newChildren) => {
      let current = options
      for (let i = 0; i < path.length - 1; i++) {
        const node = current.find(item => item.areaId === path[i].areaId)
        if (node && node.children) {
          current = node.children
        } else {
          return false
        }
      }

      const targetNode = current.find(item => item.areaId === path[path.length - 1].areaId)
      if (targetNode) {
        targetNode.children = newChildren
        return true
      }
      return false
    }

    const childrenData = json.map(item => ({ ...item, children: [] }))
    const success = updateNodeChildren(cascaderOptions.value, selectedOptions.slice(0, tabIndex + 1), childrenData)

    if (success) {
      // 触发响应式更新
      cascaderOptions.value = [...cascaderOptions.value]
    }
  } catch (err) {
    console.error('查询下一级数据失败:', err)
    closeToast()
    showToast(err.message || err.msg || '查询失败')
  }
}, 300)

// 级联选择器完成选择处理函数
// 用户确认选择后，更新表单数据并关闭弹窗
const onCascaderFinish = (selectedOptions) => {
  showCascader.value = false
  cascaderValueDetails.value = selectedOptions

  // 拼接选中的地址文本，用斜杠分隔各级地区
  if (selectedOptions && selectedOptions.length > 0) {
    formData.region = selectedOptions.map(option => option.areaName).join('/')

    // 存储最后一级地区的ID和类型，用于后端接口调用
    const lastOption = selectedOptions[selectedOptions.length - 1]
    formData.areaId = lastOption.areaId
    formData.areaType = lastOption.areaType

    // 触发地区变化事件，通知父组件
    emit('region-change', {
      region: formData.region,
      areaId: formData.areaId,
      areaType: formData.areaType,
      cascaderValueDetails: cascaderValueDetails.value
    })
  }
}

// 初始化省级数据函数
// 组件挂载时调用，加载全国省份列表
const initProvinceData = async () => {
  const [err, data] = await queryAddrArea()
  if (err) {
    showToast(err.msg)
    return false
  }

  cascaderOptions.value = data.map(item => ({ ...item, children: [] }))
  return true
}

// 查询已选择地址的详细信息函数
// 用于编辑模式下加载完整的地址层级数据
const querySelectedAddrInfo = async () => {
  // 不存在已选中数据，流程结束
  if (cascaderValueDetails.value.length === 0) return

  // 加载指定层级的地址数据
  const loadAddressLevel = async (index, parentObj) => {
    const current = cascaderValueDetails.value[index]
    if (!current) return null

    // 构建查询参数
    const area = `{"areaId":"${current.areaId}","areaType":"${current.areaType}"}`
    const [err, json] = await queryAddrArea(area)
    if (err || json.length === 0) return null

    // 第一级（省）特殊处理
    if (index === 0) {
      const obj = cascaderOptions.value.find(item => item.areaId === current.areaId)
      if (obj) {
        obj.children = json
        return obj
      }
      return null
    }

    // 其他级别，从父对象的children中查找
    if (parentObj && parentObj.children) {
      const obj = parentObj.children.find(item => item.areaId === current.areaId)
      if (obj) {
        obj.children = json
        return obj
      }
    }
    return null
  }

  // 依次加载省市区街道数据
  const provinceObj = await loadAddressLevel(0)
  if (!provinceObj) return

  const cityObj = await loadAddressLevel(1, provinceObj)
  if (!cityObj) return

  // 加载区县数据
  await loadAddressLevel(2, cityObj)
  // 街道级别不需要继续加载子级
}

// ==================== 表单验证状态管理 ====================
// 收货人姓名验证状态
const nameError = ref(false)
const nameErrorMessage = ref('')

// 收货人姓名验证函数
// 检查姓名是否为空和长度是否符合要求
const validateName = () => {
  const name = formData.recName.trim()
  if (!name) {
    nameError.value = true
    nameErrorMessage.value = '请填写收货人姓名'
    return false
  }
  if (name.length < 2) {
    nameError.value = true
    nameErrorMessage.value = '姓名至少需要2个字符'
    return false
  }
  nameError.value = false
  nameErrorMessage.value = ''
  return true
}

// 清除收货人姓名错误状态
// 用户输入时实时清除错误提示
const clearNameError = () => {
  if (nameError.value) {
    nameError.value = false
    nameErrorMessage.value = ''
  }
}

// 手机号验证状态
const phoneError = ref(false)
const phoneErrorMessage = ref('')

// 手机号验证函数
// 检查手机号格式是否正确（11位数字且以1开头）
const validatePhone = () => {
  const phone = formData.recPhone.trim()
  if (!phone) {
    phoneError.value = true
    phoneErrorMessage.value = '请填写手机号'
    return false
  }
  if (!phoneRegex.test(phone)) {
    phoneError.value = true
    phoneErrorMessage.value = '请填写正确的11位手机号'
    return false
  }
  phoneError.value = false
  phoneErrorMessage.value = ''
  return true
}

// 清除手机号错误状态
// 用户输入时实时清除错误提示
const clearPhoneError = () => {
  if (phoneError.value) {
    phoneError.value = false
    phoneErrorMessage.value = ''
  }
}

// 详细地址验证状态
const addressError = ref(false)
const addressErrorMessage = ref('')

// 详细地址验证函数
// 检查详细地址是否为空和长度是否符合要求
const validateAddress = () => {
  const detail = formData.addrDetail.trim()
  if (!detail) {
    addressError.value = true
    addressErrorMessage.value = '请填写详细地址'
    return false
  }
  if (detail.length < 5) {
    addressError.value = true
    addressErrorMessage.value = '详细地址至少需要5个字符'
    return false
  }
  addressError.value = false
  addressErrorMessage.value = ''
  return true
}

// 清除详细地址错误状态
// 用户输入时实时清除错误提示
const clearAddressError = () => {
  if (addressError.value) {
    addressError.value = false
    addressErrorMessage.value = ''
  }
}

// 表单整体验证函数
// 验证所有字段并返回验证结果
const validateForm = () => {
  const isNameValid = validateName()
  const isPhoneValid = validatePhone()
  const isAddressValid = validateAddress()

  if (!formData.region) {
    showToast('请选择所在地区')
    return false
  }

  const isValid = isNameValid && isPhoneValid && isAddressValid
  emit('validate', { isValid, formData })
  return isValid
}

// ==================== 地址数据处理工具函数 ====================
// 构建地址数组函数
// 将后端返回的地址对象转换为级联选择器需要的数据格式
const buildAddressArray = (address) => {
  const levels = [
    { id: address.provinceId, name: address.provinceName, type: '1' },
    { id: address.cityId, name: address.cityName, type: '2' },
    { id: address.countyId, name: address.countyName, type: '3' },
    { id: address.townId, name: address.townName, type: '4' }
  ]

  return levels
    .filter(level => level.name)
    .map(level => ({
      areaId: level.id,
      areaName: level.name,
      areaType: level.type
    }))
}

// 填充地址表单数据函数
// 用于编辑模式下的数据回填
const fillAddressForm = (address) => {
  // 填充基础表单数据
  Object.assign(formData, {
    recName: address.recName || '',
    recPhone: address.recPhone || '',
    region: [address.provinceName, address.cityName, address.countyName, address.townName]
      .filter(Boolean).join('/') || '',
    addrDetail: address.addrDetail || '',
    areaId: address.areaId || '',
    areaType: address.areaType || ''
  })

  // 构建级联选择器需要的地址数组
  const addrArr = buildAddressArray(address)
  cascaderValueDetails.value = addrArr

  // 设置当前选中值为最后一级地区ID
  if (addrArr.length > 0) {
    cascaderValue.value = addrArr[addrArr.length - 1].areaId
  }
}

// 构建请求参数函数
// 将表单数据转换为后端接口需要的格式
const buildRequestParams = () => {
  const addr = cascaderValueDetails.value
  return {
    recName: formData.recName.trim(),
    recPhone: formData.recPhone.trim(),
    addrDetail: formData.addrDetail.trim(),
    provinceId: addr[0]?.areaId || '',
    provinceName: addr[0]?.areaName || '',
    cityId: addr[1]?.areaId || '',
    cityName: addr[1]?.areaName || '',
    countyId: addr[2]?.areaId || '',
    countyName: addr[2]?.areaName || '',
    townId: addr[3]?.areaId || '',
    townName: addr[3]?.areaName || ''
  }
}

// ==================== 组件生命周期和对外接口 ====================
// 组件挂载时的初始化操作
onMounted(async () => {
  try {
    // 初始化省级数据，加载全国省份列表
    await initProvinceData()

    // 如果有初始数据，填充表单并查询完整地址信息
    if (initialData.value && Object.keys(initialData.value).length > 0) {
      fillAddressForm(initialData.value)
      await querySelectedAddrInfo()
    }
  } catch (error) {
    console.error('地址表单初始化失败:', error)
    showToast('地址表单初始化失败')
  }
})

// 暴露给父组件的方法和属性
// 允许父组件直接调用验证、构建参数等功能
defineExpose({
  validateForm,
  buildRequestParams,
  fillAddressForm,
  isFormValid,
  formData,
  cascaderValueDetails
})
</script>

<style scoped lang="less">
.address-form-component {
  .form-section {
    // 表单项间距优化
    :deep(.wo-form-item) {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .van-field {
        padding: 16px 0;
        border-bottom: 1px solid #E2E8EE;
        transition: border-color 0.3s ease;

        &:focus-within {
          border-bottom-color: var(--wo-biz-theme-color);
        }

        &:last-child {
          border-bottom: none;
        }
      }

      // 标签样式优化
      .van-field__label {
        font-size: 15px;
        color: #171E24;
        font-weight: 500;
        min-width: 70px;
        position: relative;

        // 必填标识
        &::after {
          content: '*';
          color: #EF4444;
          margin-left: 2px;
          font-weight: 400;
        }
      }

      // 输入框样式优化
      .van-field__control {
        font-size: 15px;
        color: #171E24;

        &::placeholder {
          color: #CBD5E0;
          font-size: 14px;
        }

        &:focus {
          outline: none;
        }
      }
    }
  }

  .address-form__region-value {
    flex: 1;
    font-size: 15px;
    color: #171E24;
    line-height: 1.5;
    padding: 2px 0;

    &--placeholder {
      color: #CBD5E0;
      font-size: 14px;
    }
  }

  .address-form__arrow-icon {
    width: 8px;
    height: 12px;
    margin-left: 8px;
    background: url('@/static/images/arrow-right-black.png') no-repeat center;
    background-size: contain;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 0.10;
    }
  }

  .address-form__cascader {
    :deep(.van-tabs__line) {
      background: var(--wo-biz-theme-gradient-1);
      height: 3px;
      border-radius: 2px;
    }

    :deep(.van-cascader__option.van-cascader__option--selected) {
      color: var(--wo-biz-theme-color);
      font-weight: 500;
      background: #FF7A0A14;
    }

    :deep(.van-cascader__header) {
      background: #FFFFFF;
      //border-bottom: 1px solid #E2E8EE;
    }

    :deep(.van-cascader__close-icon) {
      color: #718096;
    }
  }
}
</style>
