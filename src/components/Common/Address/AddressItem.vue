<!--
/**
 * 地址项组件 - AddressItem
 *
 * 功能描述：
 * 用于展示单个地址信息的卡片组件，支持地址的查看、编辑和删除操作。
 * 组件会根据地址是否为默认地址显示不同的样式和操作按钮。
 *
 * 主要功能：
 * 1. 地址信息展示 - 显示收件人姓名、电话号码和完整地址
 * 2. 默认地址标识 - 为默认地址显示特殊标识和样式
 * 3. 交互操作 - 支持点击选择、编辑和删除地址
 * 4. 防抖处理 - 所有用户操作都进行防抖处理，避免重复触发
 *
 * 技术特点：
 * - 使用 Vue 3 Composition API 和 <script setup> 语法
 * - 响应式数据处理，支持地址信息的动态更新
 * - 事件防抖机制，提升用户体验
 * - 语义化 HTML 结构，良好的可访问性
 * - 响应式布局设计，适配不同屏幕尺寸
 */
-->

<template>
  <!-- 地址卡片容器 - 根据是否为默认地址应用不同样式，点击时触发选择事件 -->
  <article class="address-card" :class="{ 'address-card--default': isDefault }" @click="handleClick">
    <!-- 地址卡片头部 - 显示联系人信息区域 -->
    <header class="address-card__header">
      <!-- 联系人信息容器 - 包含收件人姓名和电话号码 -->
      <div class="contact-info">
        <!-- 收件人姓名 - 主要联系人标识 -->
        <h3 class="contact-info__name">{{ address.recName }}</h3>
        <!-- 联系电话 - 收件人联系方式 -->
        <span class="contact-info__phone">{{ address.recPhone }}</span>
      </div>
    </header>

    <!-- 地址卡片内容区 - 显示完整的收货地址信息 -->
    <div class="address-card__content">
      <!-- 详细地址文本 - 包含省市区县和详细地址的完整信息 -->
      <p class="address-detail">{{ fullAddress }}</p>
    </div>

    <!-- 地址卡片底部操作区 - 包含默认标识和操作按钮 -->
    <footer class="address-card__actions">
      <!-- 默认地址标识 - 仅在当前地址为默认地址时显示 -->
      <div v-show="isDefault" class="default-badge">
        <!-- 默认地址图标 - 视觉标识 -->
        <img src="../../../static/images/wo-select.png" class="default-badge__icon" alt="默认地址" loading="lazy">
        <!-- 默认地址文本标识 -->
        <span class="default-badge__text">已默认</span>
      </div>

      <!-- 操作按钮组 - 包含编辑和删除功能 -->
      <div class="action-buttons">
        <!-- 编辑按钮 - 仅在非默认地址时显示，点击时阻止事件冒泡并触发编辑事件 -->
        <button v-if="!isDefault" type="button" class="action-btn action-btn--edit" @click.stop="handleEdit"
          aria-label="编辑地址">
          编辑
        </button>
        <!-- 删除按钮 - 点击时阻止事件冒泡并触发删除事件 -->
        <button type="button" class="action-btn action-btn--delete" @click.stop="handleDelete" aria-label="删除地址">
          删除
        </button>
      </div>
    </footer>
  </article>
</template>

<script setup>
// ===================== 核心依赖导入 =======================
import { defineEmits, defineProps, computed, toRefs } from 'vue'
import { compact, throttle } from 'es-toolkit'
import { join } from 'es-toolkit/compat'
// ===================== 组件属性定义 =======================
// 定义组件接收的属性
const props = defineProps({
  // 地址对象 - 包含完整的地址信息数据
  address: {
    type: Object,
    required: true
  }
})

// 定义组件向外发射的事件
const emit = defineEmits(['click', 'edit', 'delete'])

// ===================== 响应式数据处理 =======================
// 使用 toRefs 解构 props，保持响应性
const { address } = toRefs(props)

// ===================== 地址状态计算 =======================
// 计算当前地址是否为默认地址
const isDefault = computed(() => {
  // 判断 isDefault 字段是否为 '1'，返回布尔值
  return address.value.isDefault === '1'
})

// 计算完整的地址字符串
const fullAddress = computed(() => {
  // 从地址对象中解构出各级地址信息
  const { provinceName, cityName, countyName, townName, addrDetail } = address.value
  // 使用 compact 过滤掉空值，组成地区数组
  const regions = compact([provinceName, cityName, countyName, townName])
  // 拼接地区信息和详细地址，返回完整地址字符串
  return `${join(regions, '')} ${addrDetail || ''}`
})

// ===================== 用户交互事件处理 =======================
// 处理地址卡片点击事件 - 使用防抖避免重复触发
const handleClick = throttle(() => {
  // 向父组件发射点击事件，传递当前地址数据
  emit('click', address.value)
}, 300) // 300ms 防抖间隔

// 处理编辑按钮点击事件 - 使用防抖避免重复触发
const handleEdit = throttle(() => {
  // 向父组件发射编辑事件，传递当前地址数据
  emit('edit', address.value)
}, 300) // 300ms 防抖间隔

// 处理删除按钮点击事件 - 使用防抖避免重复触发
const handleDelete = throttle(() => {
  // 向父组件发射删除事件，传递当前地址数据
  emit('delete', address.value)
}, 300) // 300ms 防抖间隔
</script>

<style scoped lang="less">
.address-card {
  padding: 14px;
  background-color: #FFFFFF;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &--default {
    border: 1px solid var(--wo-biz-theme-color);
  }
}

.address-card__header {
  margin-bottom: 12px;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 10px;

  &__name {
    font-size: 16px;
    font-weight: 600;
    color: #171E24;
    margin: 0;
  }

  &__phone {
    font-size: 16px;
    color: #171E24;
    font-weight: 600;
  }
}

.address-card__content {
  margin-bottom: 16px;
}

.address-detail {
  font-size: 13px;
  color: #171E24;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
}

.address-card__actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.address-card--default .address-card__actions {
  justify-content: space-between;
}

.default-badge {
  display: flex;
  align-items: center;
  color: var(--wo-biz-theme-color);
  font-size: 12px;
  font-weight: 500;

  &__icon {
    width: 15px;
    height: 15px;
    margin-right: 4px;
  }

  &__text {
    color: var(--wo-biz-theme-color);
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 25px;
  background: #FFFFFF;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  color: #171E24;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}
</style>
