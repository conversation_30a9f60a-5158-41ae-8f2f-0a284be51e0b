<!--
地址快速选择弹窗组件

功能描述：
- 提供底部弹出的地址选择界面，支持地址列表展示、选择、编辑和删除操作
- 支持设置默认地址功能，自动同步用户状态到全局 store
- 提供骨架屏加载状态和空状态展示
- 支持配送提示信息显示
- 提供新建地址入口
- 支持滚动定位到指定地址项

主要特性：
- 响应式地址数据管理（支持外部传入或内部获取）
- 防抖处理的地址选择和滚动定位
- 完整的错误处理和用户反馈
- 性能优化的列表渲染和滚动
-->
<template>
  <!-- 底部弹出的地址选择弹窗，占屏幕高度80% -->
  <van-popup v-model:show="visible" round position="bottom" :style="{ height: '80%' }" @close="handleClose">
    <div class="address-quick-selection-popup">
      <!-- 弹窗头部区域：标题和关闭按钮 -->
      <div class="popup-header">
        <h3 class="title">选择地址</h3>
        <!-- 关闭按钮，点击关闭弹窗 -->
        <div class="close-btn" @click="handleClose">
          <img src="@/static/images/close.png" alt="关闭" loading="lazy" />
        </div>
      </div>

      <!-- 配送提示信息区域，根据 showTips 和 tipsText 控制显示 -->
      <div class="address-tips" v-if="showTips && tipsText">
        <span class="tips-text" v-html="tipsText"></span>
      </div>

      <!-- 加载状态：显示骨架屏占位 -->
      <div class="address-list" v-if="loading">
        <div v-for="index in 2" :key="`skeleton-${index}`">
          <AddressItemSkeleton />
        </div>
      </div>

      <!-- 地址列表区域：展示所有可选地址 -->
      <div class="address-list" ref="addressListRef" v-else-if="hasAddressList">
        <div v-for="address in actualAddressList" :key="address.addressId"
          :ref="el => setAddressItemRef(el, address.addressId)" :data-address-id="address.addressId">
          <!-- 地址项组件，支持点击选择、编辑和删除操作 -->
          <AddressItem :address="address" @click="handleSelectAddress" @edit="handleEditAddress"
            @delete="handleDeleteAddress" />
        </div>
      </div>

      <!-- 空状态：无地址时显示 -->
      <div class="empty-state" v-else>
        <img src="./assets/no-address.png" alt="暂无地址" class="empty-image" loading="lazy" decoding="async" />
      </div>

      <!-- 底部操作栏：新建地址按钮 -->
      <WoActionBar class="action-bar">
        <WoButton type="primary" block size="xlarge" @click="handleCreateNewAddress">
          新建收货地址
        </WoButton>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
// ===================== 依赖导入 =======================
import { defineProps, defineEmits, toRefs, ref, watch, nextTick, computed, shallowRef, onUnmounted, readonly, markRaw, defineAsyncComponent } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { useUserStore } from '@store/modules/user.js'
import { updateUserDefaultAddr } from '@api/index.js'
import { debounce } from 'es-toolkit'

// 异步组件导入，提升首屏加载性能
const WoButton = defineAsyncComponent(() => import('@components/WoElementCom/WoButton/WoButton.vue'))
const WoActionBar = defineAsyncComponent(() => import('@components/WoElementCom/WoActionBar.vue'))
const AddressItem = defineAsyncComponent(() => import('@components/Common/Address/AddressItem.vue'))
const AddressItemSkeleton = defineAsyncComponent(() => import('@components/Common/Address/AddressItemSkeleton.vue'))

// ===================== 组件接口定义 =======================
// 组件属性定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  addressList: {
    type: Array,
    default: null // null 表示使用内部地址列表，非 null 表示使用外部传入的地址列表
  },
  selectedAddressId: {
    type: [String, Number],
    default: null // 指定要滚动到的地址 ID
  },
  showTips: {
    type: Boolean,
    default: false // 是否显示配送提示信息
  },
  tipsText: {
    type: String,
    default: '23:10前付款，预计明天（03月04日）到达' // 配送提示文本内容
  }
})

// 组件事件定义
const emit = defineEmits([
  'close', // 关闭弹窗事件
  'select', // 选择地址事件
  'create', // 创建新地址事件
  'edit', // 编辑地址事件
  'delete', // 删除地址事件
  'update:visible' // 更新弹窗显示状态事件
])

// 使用 toRefs 解构 props，保持响应性
const { visible, addressList, selectedAddressId, showTips, tipsText } = toRefs(props)

// ===================== 全局状态管理 =======================
// 用户状态管理 store
const userStore = useUserStore()

// ===================== 地址数据管理 =======================
// 内部地址列表状态（当外部未传入 addressList 时使用）
const internalAddressList = shallowRef([])
// 内部选中的地址 ID
const internalSelectedAddressId = ref(null)
// 地址列表加载状态
const loading = ref(false)

// 实际使用的地址列表：优先使用外部传入的，否则使用内部获取的
const actualAddressList = computed(() => {
  return addressList.value !== null ? addressList.value : internalAddressList.value
})

// 是否有地址列表数据
const hasAddressList = computed(() => {
  return actualAddressList.value.length > 0
})

// ===================== DOM 引用管理 =======================
// 地址列表容器的 DOM 引用
const addressListRef = ref(null)
// 地址项元素的 DOM 引用映射表，用于滚动定位
const addressItemRefs = shallowRef(new Map())

// ===================== 地址数据加载 =======================
// 从用户 store 加载地址列表数据
const loadAddressListFromStore = async () => {
  // 防止重复加载
  if (loading.value) return

  loading.value = true
  try {
    // 查询用户登录状态
    await userStore.queryLoginStatus()

    // 如果用户已登录，并行获取默认地址和地址列表
    if (userStore.isLogin) {
      await Promise.all([
        userStore.queryDefaultAddr({force: true}), // 强制刷新默认地址
        userStore.queryAddrList({force: true}) // 强制刷新地址列表
      ])
    }

    // 使用 markRaw 标记为非响应式数据，提升性能
    internalAddressList.value = markRaw(userStore.addressList || [])

    // 查找默认地址并设置为选中状态
    const defaultAddress = internalAddressList.value.find(item => item.isDefault === '1')
    if (defaultAddress) {
      internalSelectedAddressId.value = defaultAddress.addressId
    }
  } catch (error) {
    console.error('加载地址列表失败:', error)
    internalAddressList.value = [] // 加载失败时重置为空数组
  } finally {
    loading.value = false
  }
}

// ===================== DOM 操作工具函数 =======================
// 设置地址项的 DOM 引用，用于后续滚动定位
const setAddressItemRef = (el, addressId) => {
  if (el) {
    // 元素存在时添加到引用映射表
    addressItemRefs.value.set(addressId, el)
  } else {
    // 元素不存在时从引用映射表中删除
    addressItemRefs.value.delete(addressId)
  }
}

// 滚动到指定地址项，使用防抖优化性能
const scrollToAddress = debounce((addressId) => {
  // 确保容器和地址 ID 都存在
  if (addressListRef.value && addressId) {
    // 从引用映射表中获取目标元素
    const selectedElement = addressItemRefs.value.get(addressId);
    if (selectedElement) {
      // 平滑滚动到目标元素，居中显示
      selectedElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }
}, 100); // 100ms 防抖延迟

// ===================== 用户交互事件处理 =======================
// 关闭弹窗处理
const handleClose = () => {
  emit('update:visible', false) // 更新弹窗显示状态
  emit('close') // 触发关闭事件
}

// 选择地址处理，使用防抖避免重复点击
const handleSelectAddress = debounce(async (address) => {
  // 防止在加载状态下重复操作
  if (loading.value) return

  // 显示加载提示
  showLoadingToast()

  try {
    // 调用 API 设置默认地址
    const [err] = await updateUserDefaultAddr(address.addressId)

    if (!err) {
      // API 调用成功，更新本地数据
      const currentList = actualAddressList.value
      // 更新所有地址的默认状态：选中的设为默认，其他取消默认
      currentList.forEach(item => {
        item.isDefault = item.addressId === address.addressId ? '1' : '0'
      })

      // 如果使用内部地址列表，同步更新内部状态
      if (addressList.value === null) {
        internalAddressList.value = markRaw([...currentList])
      }

      // 同步更新用户 store 中的地址数据
      userStore.setAddrList(currentList)
      userStore.setDefaultAddr(address)

      // 显示成功提示
      showToast('设置默认地址成功')

      // 触发选择事件并关闭弹窗
      emit('select', address)
      handleClose()
    } else {
      // API 返回错误
      console.error('设置默认地址失败:', err)
      showToast(err.msg || '设置默认地址失败')
    }
  } catch (error) {
    // 异常处理
    console.error('设置默认地址异常:', error)
    showToast('设置默认地址失败')
  } finally {
    // 关闭加载提示
    closeToast()
  }
}, 300) // 300ms 防抖延迟

// 创建新地址处理
const handleCreateNewAddress = () => {
  emit('create') // 触发创建新地址事件
}

// 编辑地址处理
const handleEditAddress = (address) => {
  emit('edit', address) // 触发编辑地址事件
}

// 删除地址处理
const handleDeleteAddress = (address) => {
  emit('delete', address) // 触发删除地址事件
}

// ===================== 生命周期和副作用 =======================
// 监听弹窗显示状态变化
watch(visible, async (newVisible) => {
  if (newVisible) {
    // 弹窗打开时加载地址列表数据
    await loadAddressListFromStore()

    // 等待 DOM 更新完成后执行滚动定位
    nextTick(() => {
      let targetAddressId = null

      // 优先使用外部传入的选中地址 ID
      if (selectedAddressId.value !== null) {
        targetAddressId = selectedAddressId.value
      } else {
        // 否则查找默认地址进行定位
        const currentList = actualAddressList.value
        const defaultAddress = currentList.find(item => item.isDefault === '1')
        if (defaultAddress) {
          targetAddressId = defaultAddress.addressId
          // 如果使用内部地址列表，同步更新内部选中状态
          if (addressList.value === null) {
            internalSelectedAddressId.value = defaultAddress.addressId
          }
        }
      }

      // 如果找到目标地址，滚动到该位置
      if (targetAddressId) {
        scrollToAddress(targetAddressId)
      }
    })
  }
}, { immediate: false }) // 不立即执行，只在值变化时触发

// 组件卸载时清理防抖函数
onUnmounted(() => {
  scrollToAddress.cancel(); // 取消未执行的防抖函数
});

// ===================== 组件暴露接口 =======================
// 向父组件暴露的方法和数据
defineExpose({
  scrollToAddress, // 滚动到指定地址的方法
  loadAddressList: loadAddressListFromStore, // 重新加载地址列表的方法
  addressList: readonly(internalAddressList) // 只读的内部地址列表数据
})
</script>

<style scoped lang="less">
.address-quick-selection-popup {
  background: #F8F9FA;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 65px;
  box-sizing: border-box;
  contain: layout style paint;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 16px 20px;
    flex-shrink: 0;
    contain: layout style;

    .title {
      font-size: 17px;
      font-weight: 600;
      color: #171E24;
      margin: 0;
      text-align: center;
    }

    .close-btn {
      position: absolute;
      right: 20px;
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      will-change: opacity;

      img {
        width: 24px;
        height: 24px;
      }

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .address-tips {
    margin: 0 10px 10px 10px;
    contain: layout style;

    .tips-text {
      color: #FF4141;
      font-size: 15px;
      line-height: 1.5;
    }
  }

  .address-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 10px;
    will-change: scroll-position;
    contain: layout style paint;
    transform: translateZ(0);

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #CBD5E0;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #718096;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #4A5568;
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    contain: layout style;

    .empty-image {
      width: 200px;
      height: 200px;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }

    .empty-text {
      font-size: 16px;
      color: #4A5568;
      margin: 0;
    }
  }
}
</style>
