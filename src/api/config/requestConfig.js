import { assignIn } from "es-toolkit/compat";
import qs from "qs";

const JSON_HEADERS = Object.freeze({ 'Content-Type': 'application/json;charset=UTF-8' });
const FORM_HEADERS = Object.freeze({ 'Content-Type': 'application/x-www-form-urlencoded' });

/**
 * 生成JSON格式POST请求配置
 *
 * @param {Object} [data={}] - 作为请求体传递的对象，将直接透传到 body
 * @param {Record<string,string>} [headers={}] - 额外请求头，会与默认 JSON 头合并
 * @param {Object} [config={}] - 其他 alova Method 支持的配置（如 params、timeout 等）
 * @returns {{body:Object, headers:Record<string,string>}} 供 alovaInstance.Post 使用的配置
 */

export const getJsonPostConfig = (data = {}, headers = {}, config = {}) => ({
  body: { ...data },
  headers: assignIn({}, JSON_HEADERS, headers),
  ...config
});

/**
 * 生成表单GET请求配置
 *
 * @param {Object} [data={}] - GET 查询参数，最终以 params 方式传递
 * @param {Record<string,string>} [headers={}] - 额外请求头，将与默认头合并（默认无特殊头）
 * @param {Object} [config={}] - 其他 alova Method 支持的配置
 * @returns {{params:Object, headers:Record<string,string>}} 供 alovaInstance.Get 使用的配置
 */
/**
 * 生成表单GET请求配置
 */
export const getFormGetConfig = (data = {}, headers = {}, config = {}) => ({
  params: { ...data },
  headers: assignIn({}, headers),
  ...config
});

/**
 * 生成表单POST请求配置（Content-Type: application/x-www-form-urlencoded）
 *
 * @param {Object} [data={}] - 将被 qs.stringify 序列化为表单字符串
 * @param {Record<string,string>} [headers={}] - 额外请求头，会与默认表单头合并
 * @param {Object} [config={}] - 其他 alova Method 支持的配置
 * @returns {{body:string, headers:Record<string,string>}} 供 alovaInstance.Post 使用的配置
 */
export const getFormPostConfig = (data = {}, headers = {}, config = {}) => {
  const postData = qs.stringify({
    ...data,
    // _t: Date.now()
  }, { indices: false }); // 数组参数不使用下标形式（如 a[0]=1），而是 a=1&a=2

  return {
    body: postData,
    headers: assignIn({}, FORM_HEADERS, headers),
    ...config
  };
};
