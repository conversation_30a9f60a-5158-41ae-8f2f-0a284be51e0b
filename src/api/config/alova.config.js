import { createAlova } from 'alova';
import { axiosRequestAdapter } from '@alova/adapter-axios';
import VueHook from 'alova/vue';
import { onSuccess, onError } from './responseHandlers';

/**
 * alova 实例（全局复用）
 *
 * - statesHook: 指定状态管理为 VueHook
 * - timeout: 全局请求超时时间（毫秒）
 * - requestAdapter: 使用 axios 作为底层请求适配器
 * - responded: 响应拦截器，统一成功/失败处理
 *
 * 注意：重试不在此处设置，见 src/api/config/index.js 中的 sendWithRetry
 * @type {import('alova').Alova}
 */
/**
 * 创建 alova 实例
 * 说明：alova v3 的重试属于 Hook/中间件层（如 useRetriableRequest），
 * 直接在 createAlova 中配置 retry 不会生效。
 * 本项目改为在 src/api/config/index.js 的 sendWithRetry 里统一实现重试。
 */
const alovaInstance = createAlova({
  // 指定使用的状态管理钩子，这里使用 Vue 的响应式系统
  statesHook: VueHook,
  // 全局请求超时设置（单位：毫秒）
  timeout: 30000,
  // 使用的请求适配器，这里配置为 axios
  requestAdapter: axiosRequestAdapter(),
  // 响应拦截器配置
  responded: {
    onSuccess,
    onError
  },
});

export default alovaInstance;
