import { omit, pick, get } from 'es-toolkit/compat';

/**
 * 从配置中剔除重试相关字段，并返回：
 * - rest：可直接传入 alova Method 的纯配置对象
 * - retryOptions：供外层重试器使用的选项
 */
export const stripRetryKeys = (config = {}) => {
  const retryPicked = pick(config, [
    'retry',
    'maxRetry',
    'retryDelay',
    'multiplier',
    'maxDelay',
    'jitter',
    'shouldRetry',
  ]);
  const retryOptions = {
    ...retryPicked,
    // 兼容旧字段优先级
    retry: get(retryPicked, 'maxRetry', retryPicked.retry)
  };
  // rest = 原配置减去重试相关字段
  const rest = omit(config, Object.keys(retryPicked));
  return { rest, retryOptions };
};

