import { urlAppend, useLogin } from 'commonkit';
import { throttle } from 'es-toolkit';
import shared from '@/shared.js';
import { getBizCode } from '@utils/curEnv.js';
import { curChannelBiz, loginType } from '@utils/storage.js'

// 登录重定向：节流保护，避免短时间内重复触发
const triggerLoginThrottled = throttle((callbackUrl, loginTypeValue) => {
  const [, , toLogin] = useLogin();
  toLogin({ callbackUrl }, { useHistoryReplace: true, loginType: loginTypeValue || '0' });
}, 1000, { leading: true, trailing: false });
/**
 * 成功响应处理器
 *
 * 约定：接口成功时返回的 data 结构包含 code、returnCodeEnum 等字段
 * 当 code !== '0000' 且 returnCodeEnum 指示未登录时，触发统一登录流程
 *
 * @param {import('axios').AxiosResponse<any>} response - axios 响应对象（由适配器透传）
 * @returns {any} 返回业务层实际使用的数据对象
 */


export const onSuccess = (response) => {
  const json = response.data;
  // 在调用购物车接口下，不论接口是否成功，均关闭 isEntryQueryCart 状态
  if (response.config.url === '/ps-ccms-core-front/v2/cart/view') {
    shared.isEntryQueryCart = false;
  }

  // 处理未登录情况
  if (json.code !== '0000' && (json.returnCodeEnum === 'USER_NOT_LOGIN' || json.code === '0100')) {
    // 如果当前是入口查询购物车状态，并且接口是地址/购物车地址，返回未登录，不进行强行登录处理
    if (shared.isEntryQueryCart &&
      (response.config.url === '/ps-ccms-core-front/v1/user/Address/default' ||
        response.config.url === '/ps-ccms-core-front/v2/cart/view')) {
      return json;
    }

    const url = window.location.href;
    const callbackUrl = urlAppend(url, {
      distri_biz_code: getBizCode(),
      biz_channel_code: curChannelBiz.get()
    });
    triggerLoginThrottled(callbackUrl, loginType.get());
  }
  return json;
};

/**
 * 错误响应处理器
 *
 * - 对于可重试错误（网络/超时/5xx/408/429），抛出原始 error 交由外层重试策略处理
 * - 其他错误（4xx 等）包装为统一的错误对象抛出
 *
 * @param {any} error - 适配器抛出的错误对象
 * @throws {any} 抛出的错误将被 sendWithRetry 或调用方捕获
 */

export const onError = (error) => {
  console.error('请求失败:', error);

  // 对于可重试的错误，直接抛出让alova的重试机制处理
  // 网络错误、超时错误、5xx服务器错误等
  if (!error.response ||
      error.response.status >= 500 ||
      error.response.status === 408 ||
      error.response.status === 429) {
    // 直接抛出错误，让alova的重试机制处理
    throw error;
  }

  // 其他错误（4xx客户端错误等）不重试，直接返回格式化的错误信息
  throw {
    code: error.response?.status || -1,
    msg: error.message || '网络请求失败',
    response: error.response,
    config: error.config,
    originalError: error
  };
};
