/**
 * Hook 版重试封装（useRetriableRequest）
 *
 * @description
 * 这是基于 alova v3 提供的 useRetriableRequest 的二次封装。
 * 适合在 Vue 组件/组合式函数中按 Hook 方式使用，具备：
 * - 指数退避 + 抖动
 * - onRetry / onFail 事件钩子
 * - 手动停止 stop()
 *
 * 注意：
 * - 该 Hook 风格与项目现有 Promise 风格 API 并行存在；二者任选其一使用
 * - 对于非幂等 POST 接口，谨慎使用重试
 */
// ---------------------------------------------------------------------------------
// 以下为 Hook 风格的重试工具，面向组合式 API 使用场景
// 不影响基于 Promise 的封装（见 src/api/config/index.js），两者可并行存在
// ---------------------------------------------------------------------------------

import { useRetriableRequest } from 'alova/client';
import alovaInstance from './alova.config';
import { getJsonPostConfig, getFormGetConfig, getFormPostConfig } from './requestConfig';
import { stripRetryKeys } from './utils.js';

/**
 * 仅构建 Method（不发送）- JSON POST，用于 Hook 场景
 *
 * @param {string} url - 请求地址
 * @param {Object} [data={}] - JSON 请求体
 * @param {Record<string,string>} [headers={}] - 请求头
 * @param {Object} [config={}] - 其他 Method 配置项（重试相关字段会被剔除）
 * @returns {import('alova').Method<any>} 构建好的 Method 实例
 */
export const jsonPostMethod = (url, data = {}, headers = {}, config = {}) => {
  const requestConfig = getJsonPostConfig(data, headers, config);
  const { rest } = stripRetryKeys(config);
  return alovaInstance.Post(url, requestConfig.body, { headers: requestConfig.headers, ...rest });
};

/**
 * 仅构建 Method（不发送）- 表单 GET，用于 Hook 场景
 *
 * @param {string} url - 请求地址
 * @param {Object} [data={}] - 查询参数
 * @param {Record<string,string>} [headers={}] - 请求头
 * @param {Object} [config={}] - 其他 Method 配置项（重试相关字段会被剔除）
 * @returns {import('alova').Method<any>} 构建好的 Method 实例
 */
export const formGetMethod = (url, data = {}, headers = {}, config = {}) => {
  const requestConfig = getFormGetConfig(data, headers, config);
  const { rest } = stripRetryKeys(config);
  return alovaInstance.Get(url, { params: requestConfig.params, headers: requestConfig.headers, ...rest });
};

/**
 * 仅构建 Method（不发送）- 表单 POST，用于 Hook 场景
 *
 * @param {string} url - 请求地址
 * @param {Object} [data={}] - 表单字段对象
 * @param {Record<string,string>} [headers={}] - 请求头
 * @param {Object} [config={}] - 其他 Method 配置项（重试相关字段会被剔除）
 * @returns {import('alova').Method<any>} 构建好的 Method 实例
 */
export const formPostMethod = (url, data = {}, headers = {}, config = {}) => {
  const requestConfig = getFormPostConfig(data, headers, config);
  const { rest } = stripRetryKeys(config);
  return alovaInstance.Post(url, requestConfig.body, { headers: requestConfig.headers, ...rest });
};

/**
 * Hook 语法糖：直接返回 useRetriableRequest（JsonPost）
 *
 * @returns {ReturnType<typeof useRetriableRequest>} 包含 send/stop/loading/data/error 等响应式状态
 */
export const useRetriableJsonPost = (url, data = {}, headers = {}, config = {}) => {
  return useRetriable(() => jsonPostMethod(url, data, headers, config), config);
};

/**
 * Hook 语法糖：直接返回 useRetriableRequest（FormGet）
 *
 * @returns {ReturnType<typeof useRetriableRequest>} 同上
 */
export const useRetriableFormGet = (url, data = {}, headers = {}, config = {}) => {
  return useRetriable(() => formGetMethod(url, data, headers, config), config);
};

/**
 * Hook 语法糖：直接返回 useRetriableRequest（FormPost）
 *
 * @returns {ReturnType<typeof useRetriableRequest>} 同上
 */
export const useRetriableFormPost = (url, data = {}, headers = {}, config = {}) => {
  return useRetriable(() => formPostMethod(url, data, headers, config), config);
};


/**
 * 使用 Hook 方式创建带重试的请求
 * @template T
 * @param {() => import('alova').Method<T>} handler - 返回 alova Method 的函数
 * @param {Object} [config]
 * @param {number|((error:any,...args:any[])=>boolean)} [config.retry=3] - 最大重试次数或基于错误的动态判定函数
 * @param {{delay?:number,multiplier?:number,maxDelay?:number}} [config.backoff] - 退避策略，默认 { delay: 1000, multiplier: 1.5, maxDelay: 10000 }
 * @param {(ctx:any,next:()=>Promise<any>)=>Promise<any>|void} [config.middleware] - 中间件
 * @returns {ReturnType<typeof useRetriableRequest>} 包含 send/stop/loading/data/error 等响应式状态
 */
export const useRetriable = (handler, config = {}) => {
  const merged = {
  // 合并默认配置，保持向后兼容。仅调整配置对象，不改变 useRetriableRequest 的行为。
  // 注意：此处不引入项目外部的重试逻辑，完全沿用 alova 的 Hook 重试实现。
    retry: 3,
    backoff: { delay: 1000, multiplier: 1.5, maxDelay: 10000 },
    ...config
  };
  return useRetriableRequest(handler, merged);
};

/**
 * 语法糖：将已有的 useRequest 包一层重试能力
 * @template T
 * @param {() => import('alova').Method<T>} handler - 返回 alova Method 的函数
 * @param {Object} [config]
 * @returns {ReturnType<typeof useRetriableRequest>} 同上
 */
export const useRequestWithRetry = (handler, config = {}) => {
  // 纯语法糖：直接复用 useRetriable，避免重复逻辑
  return useRetriable(handler, config);
};

