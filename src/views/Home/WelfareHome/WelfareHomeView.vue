<!--
/**
 * 员工福利首页组件
 *
 * 主要功能：
 * 1. 展示员工福利服务首页的完整布局，包括头部横幅、网格菜单和商品展示区域
 * 2. 提供商品分类头部导航，支持用户在不同福利商品类别间切换浏览
 * 3. 实现瀑布流商品展示，支持分页加载和无限滚动浏览
 * 4. 集成搜索功能，支持用户搜索员工福利相关商品和服务
 * 5. 实现骨架屏加载效果和平滑过渡动画，提升用户体验
 * 6. 支持标签页切换时的平滑过渡，避免页面回弹现象
 *
 * 技术特点：
 * - 使用BaseHomeLayout作为基础布局组件，统一首页结构
 * - 集成GoodsHeader商品头部导航组件，提供分类切换功能
 * - 采用WaterfallSection瀑布流组件，支持无限滚动加载
 * - 使用transition组件实现骨架屏与内容的平滑切换
 * - 实现响应式设计，适配不同屏幕尺寸
 * - 优化标签切换体验，避免数据清空导致的页面跳动
 *
 * 使用场景：
 * - 员工福利服务平台的主要入口页面
 * - 用户浏览和搜索员工福利商品的核心界面
 * - 展示福利商品分类和浏览的导航页面
 */
-->

<template>
  <!-- 员工福利首页主容器 -->
  <!-- 使用BaseHomeLayout提供统一的首页布局结构 -->
  <!-- 配置搜索占位符、头部横幅、网格菜单等基础元素 -->
  <!-- 设置网格列数为5列，适配员工福利服务的菜单展示需求 -->
  <BaseHomeLayout
    home-class="welfare-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <!-- 附加内容区域插槽，用于展示商品分类导航 -->
    <template #additional-content>
      <!-- 商品分类头部导航区域 -->
      <!-- 当骨架屏状态为true或分类列表有数据时显示 -->
      <!-- 提供商品分类切换功能，支持用户在不同类别间导航 -->
      <div
        v-if="skeletonStates.goodsHeader || typeList.length > 0"
        class="home-goods-header-container"
      >
        <transition name="skeleton-fade" mode="out-in">
          <!-- 商品头部骨架屏组件，在数据加载时显示 -->
          <GoodsHeaderSkeleton v-if="skeletonStates.goodsHeader" key="goods-header-skeleton" />
          <!-- 商品头部导航组件，提供分类切换功能 -->
          <GoodsHeader
            v-else
            :typeList="typeList"
            key="goods-header-content"
            @switchTabs="switchTabs"
          />
        </transition>
      </div>
    </template>

    <!-- 主要内容区域插槽，展示瀑布流商品列表 -->
    <template #main-content>
      <!-- 瀑布流商品展示组件 -->
      <!-- 支持分页加载、无限滚动和渲染完成回调 -->
      <WaterfallSection
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import GoodsHeader from '@views/Home/components/GoodsHeader.vue'
import GoodsHeaderSkeleton from '@views/Home/components/Skeleton/GoodsHeaderSkeleton.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// ==================== 首页基础数据和瀑布流功能 ====================
// 使用首页数据组合式函数，获取基础数据、瀑布流状态和相关工具函数
const {
  headerBannerList,        // 头部横幅列表数据
  gridMenuItems,           // 网格菜单项数据
  skeletonStates,          // 骨架屏显示状态控制
  moduleDataReady,         // 模块数据就绪状态，用于控制骨架屏隐藏时机
  waterfallGoodsList,      // 瀑布流商品列表数据
  waterfallLoading,        // 瀑布流加载状态
  waterfallFinished,       // 瀑布流加载完成状态
  waterfallButtonCanShow,  // 瀑布流加载更多按钮显示状态
  waterfallRenderComplete, // 瀑布流渲染完成状态
  waterfallCurrentPage,    // 瀑布流当前页码
  getHeaderBannerList,     // 获取头部横幅数据的函数
  getIconList,             // 获取图标列表数据的函数
  getWaterfallList,        // 获取瀑布流商品列表的函数
  resetWaterfallState,     // 重置瀑布流状态的函数
  getPartionListData,      // 获取商品分区列表数据的函数
  hideSkeletonInOrder      // 按顺序隐藏骨架屏的函数，提供平滑的加载体验
} = useHomeData()

// ==================== 首页导航和交互功能 ====================
// 使用首页导航组合式函数，获取各种用户交互事件的处理函数
const {
  handleGoodsClick,        // 商品点击事件处理函数，跳转到商品详情页
  handleBannerClick,       // 横幅点击事件处理函数，处理横幅跳转逻辑
  handleGridItemClick,     // 网格菜单项点击事件处理函数，处理菜单导航
  handleMoreClick,         // 更多按钮点击事件处理函数，展开更多菜单
  handleSearch             // 搜索事件处理函数，处理搜索跳转和参数传递
} = useHomeNavigation()

// ==================== 员工福利页面特有数据管理 ====================
// 商品分类列表数据，用于商品头部导航切换
const typeList = ref([])
// 当前选中的商品池ID，用于瀑布流商品数据获取
const goodsPoolIdSelected = ref('')

// ==================== 骨架屏状态扩展配置 ====================
// 扩展骨架屏状态配置，为员工福利页面特有的商品头部区域添加加载状态控制
skeletonStates.value = {
  ...skeletonStates.value,  // 保留原有的骨架屏状态配置
  goodsHeader: true         // 商品头部导航区域骨架屏状态
}

// ==================== 模块数据就绪状态配置 ====================
// 扩展模块数据就绪状态配置，用于控制商品头部区域骨架屏的隐藏时机
moduleDataReady.value = {
  ...moduleDataReady.value, // 保留原有的模块数据就绪状态
  goodsHeader: false        // 商品头部数据就绪状态
}

// ==================== 瀑布流商品交互处理 ====================
// 瀑布流渲染完成后的回调处理函数
// 当瀑布流组件完成渲染后调用，用于更新渲染完成状态
const handleWaterfallAfterRender = () => {
  // 标记瀑布流渲染已完成，可用于后续的性能优化或状态控制
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多商品的事件处理函数
// 当用户触发加载更多操作时调用，获取下一页商品数据
const handleWaterfallLoadMore = () => {
  // 调用瀑布流数据获取函数，传入当前选中的商品池ID
  // 第二个参数为排序类型（空字符串表示默认排序）
  // 第三个参数为true表示这是加载更多操作
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// ==================== 商品分类切换处理 ====================
// 商品分类标签页切换事件处理函数
// 当用户点击商品头部导航的不同分类时触发
const switchTabs = async (id) => {
  // 更新当前选中的商品池ID，但不立即清空数据避免页面回弹
  goodsPoolIdSelected.value = id

  // 重置瀑布流分页状态，但保留当前显示的数据
  // 这样可以避免切换时的页面闪烁，提供更好的用户体验
  waterfallCurrentPage.value = 1          // 重置页码为第一页
  waterfallFinished.value = false         // 重置加载完成状态
  waterfallLoading.value = false          // 重置加载状态
  waterfallButtonCanShow.value = false    // 隐藏加载更多按钮
  waterfallRenderComplete.value = false   // 重置渲染完成状态

  // 等待DOM更新完成后再加载新数据
  // 确保状态重置已生效，不设置骨架屏状态直接加载新数据
  await nextTick()
  // 获取新分类的商品数据，第三个参数false表示首次加载
  getWaterfallList(id, '', false)
}

// ==================== 商品池切换处理 ====================
// 商品池切换处理函数
// 用于完全重置瀑布流状态并加载新的商品池数据
const changeGoodsPool = (id, sortType = '') => {
  // 更新当前选中的商品池ID
  goodsPoolIdSelected.value = id
  // 完全重置瀑布流状态，包括清空数据和重置所有状态
  resetWaterfallState()
  // 获取新商品池的数据，第三个参数false表示首次加载
  getWaterfallList(id, sortType, false)
}

// ==================== 页面初始化数据加载 ====================
// 页面初始化数据加载的异步函数
// 负责获取商品分类数据和初始化默认商品池
const initPage = async () => {
  // 获取商品分区列表数据（类型2表示主要商品分类）
  // 用于商品头部导航的分类切换功能
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  // 标记商品头部数据已就绪
  moduleDataReady.value.goodsHeader = true
  // 按指定顺序隐藏骨架屏，依次隐藏：主横幅 -> 网格菜单 -> 商品头部
  await hideSkeletonInOrder(['banner', 'gridMenu', 'goodsHeader'])

  // 如果有商品分类数据，默认选择第一个分类并加载其商品数据
  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]  // 获取推荐分类（第一个分类）
    goodsPoolIdSelected.value = recommond.id  // 设置为当前选中的商品池ID
    changeGoodsPool(recommond.id)  // 切换到该商品池并加载数据
  }
}

// ==================== 组件生命周期管理 ====================
// 组件挂载完成后的初始化操作
// 按照页面展示顺序依次加载各个区域的数据
onMounted(() => {
  // 获取头部横幅数据，展示首页顶部轮播图
  getHeaderBannerList()

  // 获取网格菜单图标数据，员工福利使用showPage为5的图标配置
  getIconList(5)

  // 初始化页面数据，包括商品分类和默认商品池
  initPage()
})

// 组件卸载时的清理操作
// 预留清理函数，可用于取消未完成的请求或清理定时器
onUnmounted(() => {
  // 清理工作预留位置
  // 可在此处添加取消请求、清理定时器等操作
})
</script>

<style scoped lang="less">
.welfare-home {
  .home-goods-header-container {
    margin: 10px 0;
    box-sizing: border-box;
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
