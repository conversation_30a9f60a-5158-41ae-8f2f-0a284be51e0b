<!--
/**
 * 商品分类头部组件
 *
 * 主要功能：
 * 1. 展示商品分类标签列表，支持水平滚动浏览
 * 2. 提供分类切换功能，支持点击选择不同分类
 * 3. 实现自定义滚动条，提升滚动体验
 * 4. 支持激活状态显示，高亮当前选中的分类
 * 5. 自动检测内容溢出，按需显示滚动条
 * 6. 响应式设计，适配不同屏幕尺寸
 *
 * 技术特点：
 * - 使用水平滚动容器实现分类标签浏览
 * - 集成自定义滚动条和滚动位置计算
 * - 支持事件发射进行父子组件通信
 * - 使用ref获取DOM元素进行滚动控制
 * - 实现滚动条位置的实时更新
 *
 * 使用场景：
 * - 商品列表页的分类筛选头部
 * - 首页商品分类导航
 * - 任何需要水平滚动分类选择的场景
 */
-->

<template>
  <!-- 商品分类头部主容器 -->
  <!-- 根据滚动条显示状态动态调整底部内边距 -->
  <div class="goods-header" :style="{ paddingBottom: showScrollbar ? '10px' : '0' }">
    <!-- 分类标签滚动容器 -->
    <!-- 监听滚动事件以更新自定义滚动条位置 -->
    <div ref="containerRef" class="goods-header__container" @scroll="handleScroll">
      <!-- 分类标签项循环渲染 -->
      <!-- 根据激活状态应用不同样式，监听点击事件进行分类切换 -->
      <div
        ref="typeRefs"
        v-for="(item, index) in typeList"
        :key="index"
        class="goods-header__item"
        :class="{ 'goods-header__item--active': activeIndex === index }"
        @click="chooseOne(index)"
      >
        {{ item.name }}
      </div>
    </div>
    <!-- 自定义滚动条 -->
    <!-- 仅在需要滚动时显示，滚动条滑块位置根据滚动位置动态计算 -->
    <div v-show="showScrollbar" class="goods-header__scrollbar">
      <div class="goods-header__scrollbar-thumb" :style="{ transform: `translateX(${scrollbarPosition}px)` }"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 分类类型列表数据，包含分类ID和名称
  typeList: {
    type: Array,
    default: () => []
  }
})

// 定义组件向父组件发射的事件
const emit = defineEmits(['switchTabs'])

// ==================== 组件状态管理 ====================
// 分类标签DOM元素引用数组
const typeRefs = ref([])
// 当前激活的分类索引
const activeIndex = ref(0)
// 滚动容器DOM元素引用
const containerRef = ref(null)
// 自定义滚动条滑块位置
const scrollbarPosition = ref(0)
// 是否显示自定义滚动条
const showScrollbar = ref(false)

// ==================== 分类切换相关方法 ====================
// 分类选择处理函数
// 更新激活索引并向父组件发射切换事件
const chooseOne = (index) => {
  // 更新当前激活的分类索引
  activeIndex.value = index
  // 向父组件发射分类切换事件，传递选中分类的ID
  emit('switchTabs', props.typeList[index].id)
}

// ==================== 滚动功能相关方法 ====================
// 滚动事件处理函数
// 计算自定义滚动条的位置和显示状态
const handleScroll = () => {
  // 如果容器不存在，直接返回
  if (!containerRef.value) return

  const container = containerRef.value
  const scrollLeft = container.scrollLeft        // 当前滚动位置
  const scrollWidth = container.scrollWidth      // 总滚动宽度
  const clientWidth = container.clientWidth      // 可视区域宽度

  // 如果内容宽度小于等于容器宽度，不需要滚动条
  if (scrollWidth <= clientWidth) {
    showScrollbar.value = false
    scrollbarPosition.value = 0
    return
  }

  // 显示滚动条
  showScrollbar.value = true

  // 滚动条轨道宽度（60px）
  const trackWidth = 60
  // 滚动条滑块宽度（20px）
  const thumbWidth = 20
  // 滚动条滑块可移动的距离
  const scrollableThumbDistance = trackWidth - thumbWidth

  // 计算滚动比例（0-1之间）
  const scrollRatio = scrollLeft / (scrollWidth - clientWidth)

  // 根据滚动比例计算滚动条滑块位置
  scrollbarPosition.value = scrollRatio * scrollableThumbDistance
}

// ==================== 生命周期钩子 ====================
// 组件挂载时的初始化操作
onMounted(() => {
  // 等待DOM更新完成后执行初始化
  nextTick(() => {
    // 如果有分类数据，默认选中第一个分类
    if (props.typeList.length > 0) {
      activeIndex.value = 0
    }
    // 初始化时检查是否需要显示滚动条
    handleScroll()
  })
})
</script>

<style lang="less" scoped>
.goods-header {
  width: 100vw;
  position: relative;
  overflow-x: hidden;
  z-index: 98;

  &__container {
    font-size: 14px;
    height: 25px;
    margin-top: 10px;
    display: flex;
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };
  }

  &__item {
    white-space: nowrap;
    height: 14px;
    line-height: 14px;
    padding: 0 17px;
    transition: 0.3s;
    color: #4A5568;
    cursor: pointer;

    &:not(:last-child) {
      border-right: 1px solid #E2E8EE;
    }

    &--active {
      position: relative;
      z-index: 2;
      color: #171E24;

      &::after {
        content: "";
        display: block;
        position: absolute;
        left: 50%;
        bottom: -1px;
        width: 56px;
        height: 6px;
        border-radius: 4px;
        z-index: -1;
        transform: translateX(-50%);
        background: var(--wo-biz-theme-gradient-1);
        transition: 0.3s;
      }
    }
  }

  // 自定义滚动条样式
  &__scrollbar {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: rgba(255, 122, 10, 0.2);
    border-radius: 2px;
    overflow: hidden;
  }

  &__scrollbar-thumb {
    width: 20px;
    height: 3px;
    background-color: var(--wo-biz-theme-color);
    border-radius: 2px;
    transition: transform 0.1s ease;
  }
}
</style>
