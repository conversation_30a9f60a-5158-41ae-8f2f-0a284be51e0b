<!--
/**
 * 图标网格组件
 *
 * 主要功能：
 * 1. 提供灵活的图标网格布局，支持2列、4列、5列等多种列数配置
 * 2. 支持网格模式和滚动模式两种显示方式，适应不同的界面需求
 * 3. 实现智能的"更多"按钮功能，当项目超过限制时自动显示
 * 4. 提供占位符机制，确保网格布局的整齐性
 * 5. 支持自定义滚动条样式，提升滚动模式下的用户体验
 * 6. 集成角标、副标题等扩展信息显示功能
 *
 * 技术特点：
 * - 支持响应式布局和动态列数计算
 * - 实现自定义滚动条和滚动事件处理
 * - 使用计算属性优化性能和数据处理
 * - 支持占位符和特殊项目类型处理
 * - 集成SVG图标和图片图标支持
 *
 * 使用场景：
 * - 首页功能菜单网格
 * - 分类导航图标列表
 * - 工具栏和快捷操作入口
 */
-->

<template>
  <!-- 图标网格主容器 -->
  <!-- 根据列数和显示模式应用不同的CSS类 -->
  <div class="grid-menu" :class="[`columns-${dynamicColumns}`, { 'scroll-mode': displayMode === 'scroll' }]">
    <!-- 网格容器，支持滚动和事件监听 -->
    <div class="grid-container" ref="gridContainer" @scroll="handleScroll">
      <!-- 网格项循环渲染 -->
      <!-- 根据项目类型应用不同的CSS类 -->
      <!-- 监听点击事件进行交互处理 -->
      <div
        v-for="(item, index) in displayMenuItems"
        :key="index"
        class="grid-item"
        :class="{
          'grid-item-more': item.isMore,
          'grid-item-placeholder': item.isPlaceholder
        }"
        @click="handleItemClick(item, index)"
      >
        <!-- 项目内容区域，占位符不显示内容 -->
        <div class="item-content" v-if="!item.isPlaceholder">
          <!-- 图标区域 -->
          <div class="item-icon">
            <!-- 有图标时显示图片 -->
            <img v-if="item.icon" :src="item.icon" :alt="item.title" />
            <!-- 无图标时显示占位符SVG -->
            <div v-else class="icon-placeholder">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" />
              </svg>
            </div>
          </div>

          <!-- 主标题 -->
          <div class="item-title">{{ item.title }}</div>

          <!-- 副标题/描述信息 -->
          <!-- 当有副标题时显示 -->
          <div v-if="item.subtitle" class="item-subtitle">{{ item.subtitle }}</div>

          <!-- 角标信息 -->
          <!-- 当有角标时显示 -->
          <div v-if="item.badge" class="item-badge">{{ item.badge }}</div>
        </div>
      </div>
    </div>

    <!-- 自定义滚动条 -->
    <!-- 仅在滚动模式下且需要滚动时显示 -->
    <div v-if="displayMode === 'scroll' && showScrollbar" class="custom-scrollbar">
      <!-- 滚动条滑块，动态计算位置和宽度 -->
      <div class="scrollbar-thumb" :style="{ left: scrollbarLeft + 'px', width: scrollbarWidth + 'px' }"></div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs, ref, nextTick, onMounted, onUnmounted, watch } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 菜单项数据数组，包含图标、标题等信息
  items: {
    type: Array,
    default: () => []
  },
  // 网格列数配置，支持2列、4列、5列布局
  columns: {
    type: Number,
    default: 5,
    validator: (value) => value === 2 || value === 4 || value === 5
  },
  // 是否显示"更多"按钮，当项目超过限制时显示
  showMore: {
    type: Boolean,
    default: true
  },
  // 最大显示项目数量，超过此数量时显示"更多"按钮
  maxItems: {
    type: Number,
    default: 10
  },
  // 显示模式：'grid'网格模式，'scroll'滚动模式
  displayMode: {
    type: String,
    default: 'grid',
    validator: (value) => ['grid', 'scroll'].includes(value)
  }
})

// 使用toRefs解构props，保持响应性
const { items, columns, showMore, maxItems, displayMode } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['itemClick', 'moreClick'])

// ==================== 滚动功能相关状态 ====================
// 网格容器DOM引用，用于滚动事件监听和计算
const gridContainer = ref(null)
// 自定义滚动条滑块的左侧位置
const scrollbarLeft = ref(0)
// 自定义滚动条滑块的宽度
const scrollbarWidth = ref(0)
// 是否显示自定义滚动条
const showScrollbar = ref(false)

// ==================== 菜单项数据处理 ====================
// 计算属性：处理菜单项数据，添加"更多"按钮逻辑
const menuItems = computed(() => {
  // 复制原始数据，避免直接修改props
  let itemList = [...items.value]

  // 滚动模式下不需要限制数量和更多按钮
  if (displayMode.value === 'scroll') {
    return itemList
  }

  // 网格模式下的数量限制和更多按钮逻辑
  if (showMore.value && itemList.length > maxItems.value - 1) {
    // 当启用更多按钮且项目数量超过限制时
    // 截取前N-1个项目，最后一个位置留给"更多"按钮
    itemList = itemList.slice(0, maxItems.value - 1)
    itemList.push({
      title: '更多',
      icon: null,
      isMore: true
    })
  } else if (itemList.length > maxItems.value) {
    // 当不显示更多按钮但项目数量超过限制时，直接截取
    itemList = itemList.slice(0, maxItems.value)
  }

  return itemList
})

// 计算属性：处理显示的菜单项，包含占位符逻辑
const displayMenuItems = computed(() => {
  const items = menuItems.value

  // 滚动模式下不需要占位符
  if (displayMode.value === 'scroll') {
    return items
  }

  // 网格模式下，如果当前行不满，需要添加占位符保持布局整齐
  const itemCount = items.length
  const cols = columns.value

  // 如果项目数量为0或正好是列数的倍数，不需要占位符
  if (itemCount === 0 || itemCount % cols === 0) {
    return items
  }

  // 计算需要添加的占位符数量
  const placeholdersNeeded = cols - (itemCount % cols)
  const result = [...items]

  // 添加占位符项目
  for (let i = 0; i < placeholdersNeeded; i++) {
    result.push({
      title: '',
      icon: null,
      isPlaceholder: true
    })
  }

  return result
})

// ==================== 布局计算相关 ====================
// 计算属性：动态计算网格列数
const dynamicColumns = computed(() => {
  // 滚动模式下列数等于项目数量，实现单行滚动
  if (displayMode.value === 'scroll') {
    return menuItems.value.length
  }

  // 网格模式下始终使用设定的列数，确保宽度均匀分布
  return columns.value
})

// 计算属性：计算每个项目的宽度
const itemWidth = computed(() => {
  if (displayMode.value === 'scroll') {
    // 滚动模式下每个项目固定宽度70px
    return '70px'
  }
  // 网格模式下根据列数平均分配宽度
  return `${100 / dynamicColumns.value}%`
})

// ==================== 滚动功能相关方法 ====================
// 滚动事件处理函数
// 计算自定义滚动条的位置和宽度，实现滚动条跟随效果
const handleScroll = () => {
  // 仅在滚动模式下且容器存在时处理
  if (displayMode.value !== 'scroll' || !gridContainer.value) return

  const container = gridContainer.value
  const scrollLeft = container.scrollLeft        // 当前滚动位置
  const scrollWidth = container.scrollWidth      // 总滚动宽度
  const clientWidth = container.clientWidth      // 可视区域宽度

  // 计算自定义滚动条的位置和宽度
  const scrollbarTrackWidth = 60                 // 滚动条轨道宽度
  const scrollRatio = scrollLeft / (scrollWidth - clientWidth)  // 滚动比例
  const thumbWidth = Math.max(12, (clientWidth / scrollWidth) * scrollbarTrackWidth)  // 滑块宽度
  const thumbLeft = scrollRatio * (scrollbarTrackWidth - thumbWidth)  // 滑块位置

  // 更新滚动条状态
  scrollbarLeft.value = thumbLeft
  scrollbarWidth.value = thumbWidth
}

// 更新滚动条显示状态函数
// 判断是否需要显示自定义滚动条
const updateScrollbarVisibility = () => {
  // 非滚动模式或容器不存在时隐藏滚动条
  if (displayMode.value !== 'scroll' || !gridContainer.value) {
    showScrollbar.value = false
    return
  }

  const container = gridContainer.value
  // 当内容宽度超过容器宽度时显示滚动条
  showScrollbar.value = container.scrollWidth > container.clientWidth
}

// ==================== 用户交互处理 ====================
// 项目点击事件处理函数
// 根据项目类型执行不同的操作
const handleItemClick = (item, index) => {
  // 占位符项目不响应点击事件
  if (item.isPlaceholder) {
    return
  }

  if (item.isMore) {
    // 点击"更多"按钮，发射moreClick事件
    emit('moreClick')
  } else {
    // 点击普通项目，发射itemClick事件并传递项目数据和索引
    emit('itemClick', { item, index })
  }
}

// ==================== 数据监听和生命周期 ====================
// 监听显示项目和显示模式的变化
// 当数据变化时重新计算滚动条状态
watch([displayMenuItems, displayMode], () => {
  nextTick(() => {
    updateScrollbarVisibility()
    handleScroll()
  })
}, { immediate: true })

// 组件挂载时的初始化操作
onMounted(() => {
  // 等待DOM更新完成后初始化滚动条状态
  nextTick(() => {
    updateScrollbarVisibility()
    handleScroll()
  })

  // 为容器添加滚动事件监听器
  if (gridContainer.value) {
    gridContainer.value.addEventListener('scroll', handleScroll)
  }
})

// 组件卸载时的清理操作
onUnmounted(() => {
  // 移除滚动事件监听器，避免内存泄漏
  if (gridContainer.value) {
    gridContainer.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped lang="less">
.grid-menu {
  //padding: @padding-page;
  //background: #FFFFFF;
  position: relative;

  .grid-container {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  // 滚动模式样式
  &.scroll-mode {
    padding-bottom: 20px; // 为滚动条留出空间

    .grid-container {
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      gap: 5px;
      //padding-bottom: 5px;

      // 隐藏原生滚动条
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    .grid-item {
      flex-shrink: 0;
      width: 70px;
    }
  }

  // 自定义滚动条
  .custom-scrollbar {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: rgba(255, 122, 10, 0.2);
    border-radius: 2px;

    .scrollbar-thumb {
      position: absolute;
      top: 0;
      height: 100%;
      background-color: var(--wo-biz-theme-color);
      border-radius: 2px;
      transition: all 0.2s ease;
      min-width: 12px;
    }
  }

  .grid-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: v-bind(itemWidth);
    box-sizing: border-box;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 5px 0;

    //&:hover {
    //  background-color: rgba(0, 0, 0, 0.02);
    //  border-radius: 8px;
    //  transform: translateY(-1px);
    //
    //  .item-icon {
    //    transform: scale(1.05);
    //    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    //  }
    //}

    //&:active {
    //  transform: translateY(0);
    //  background-color: rgba(0, 0, 0, 0.04);
    //  border-radius: 8px;
    //
    //  .item-icon {
    //    transform: scale(0.95);
    //  }
    //}

    .item-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
    }

    .item-icon {
      width: 45px;
      height: 45px;
      margin-bottom: 3px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      img {
        width: 34px;
        height: 34px;
        object-fit: contain;
      }

      .icon-placeholder {
        color: #718096;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .item-title {
      font-size: 12px;
      font-weight: 500;
      color: #171E24;
      text-align: center;
      line-height: 1.2;
      max-width: 100%;
      overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
    }

    .item-subtitle {
      font-size: 11px;
      color: #718096;
      text-align: center;
      margin-top: 2px;
      line-height: 1.2;
      max-width: 100%;
      overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
    }

    .item-badge {
      position: absolute;
      top: -2px;
      right: -2px;
      background: var(--wo-biz-theme-gradient-3);
      color: #FFFFFF;
      font-size: 11px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 8px;
      min-width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 1px 3px rgba(255, 107, 107, 0.3);
      transform: scale(0.9);
    }

    &.grid-item-more {
      .item-icon {
        background: linear-gradient(135deg, #F8F9FA 0%, darken(#F8F9FA, 5%) 100%);
        border: 2px dashed #E2E8EE;
        box-shadow: none;

        &::after {
          content: '';
          width: 20px;
          height: 20px;
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2'%3E%3Cpath d='M12 5v14M5 12h14'/%3E%3C/svg%3E") no-repeat center;
          background-size: contain;
        }
      }

      .item-title {
        color: #4A5568;
      }

      &:hover .item-icon {
        background: linear-gradient(135deg, darken(#F8F9FA, 5%) 0%, darken(#F8F9FA, 10%) 100%);
        border-color: darken(#E2E8EE, 10%);
      }
    }
  }
}

.grid-menu {
  &.columns-2 {
    .grid-item {
      flex-direction: row;
      text-align: left;
      padding: 10px 0;

      .item-content {
        flex-direction: row;
        align-items: center;
        width: 100%;
        padding: 0 10px;
      }

      .item-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 0;
        margin-right: 12px;
        flex-shrink: 0;

        img {
          width: 36px;
          height: 36px;
        }
      }

      .item-title {
        font-size: 14px;
        text-align: left;
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
      }

      .item-subtitle {
        text-align: left;
        margin-top: 4px;
      }
    }
  }

  // 占位符样式
  .grid-item-placeholder {
    pointer-events: none;
    visibility: hidden;
  }
}
</style>
