<!--
/**
 * 迷你商品卡片组件
 *
 * 主要功能：
 * 1. 展示商品的基本信息，采用紧凑的迷你布局设计
 * 2. 支持不同业务场景的价格显示，包括单价和价格区间
 * 3. 提供图片懒加载优化性能
 * 4. 实现响应式设计，适配小尺寸容器
 * 5. 支持悬停效果和交互反馈
 * 6. 根据业务类型动态调整显示内容
 *
 * 技术特点：
 * - 使用PriceDisplay组件统一价格显示格式
 * - 支持图片懒加载和异步解码优化性能
 * - 实现文本溢出处理和多行文本截断
 * - 使用CSS变量支持主题色定制
 * - 采用Flexbox布局实现紧凑设计
 *
 * 使用场景：
 * - 推荐商品区域的小尺寸商品展示
 * - 侧边栏或弹窗中的商品列表
 * - 需要紧凑布局的商品卡片场景
 */
-->

<template>
  <!-- 迷你商品卡片主容器 -->
  <div class="goods-card-mini">
    <!-- 商品图片区域 -->
    <div class="goods-image">
      <!-- 商品图片，使用懒加载和异步解码优化性能 -->
      <img :src="goodsInfo.image" :alt="goodsInfo.name" loading="lazy" decoding="async" />
    </div>

    <!-- 商品信息区域 -->
    <div class="goods-info">
      <!-- 商品名称，支持多行显示和文本截断 -->
      <div class="goods-name">{{ goodsInfo.name }}</div>
      <!-- 商品详情区域：价格和销量 -->
      <div class="goods-details">
        <!-- 价格显示区域 -->
        <!-- 根据业务类型和价格数据选择显示方式 -->
        <template v-if="isZQBiz && (goodsInfo.highPrice || goodsInfo.lowPrice)">
          <!-- ZQ业务场景：显示价格区间 -->
          <PriceDisplay
            :high-price="goodsInfo.highPrice"
            :low-price="goodsInfo.lowPrice"
            range-label=""
            size="small"
            color="orange"
          />
        </template>
        <template v-else>
          <!-- 其他业务场景：显示单一价格 -->
          <PriceDisplay :price="goodsInfo.price" size="small" color="orange" />
        </template>
        <!-- 销量信息，当有销量数据且非ZQ业务时显示 -->
        <span class="goods-sales" v-if="goodsInfo.sales > 0 && !isZQBiz">销量: {{ goodsInfo.sales }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import { getBizCode } from '@/utils/curEnv'
import PriceDisplay from '@components/Common/PriceDisplay.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 商品信息对象，包含商品的所有展示数据
  goodsInfo: {
    type: Object,
    required: true,
    default: () => ({
      image: '',        // 商品图片URL
      name: '',         // 商品名称
      price: 0,         // 商品价格
      sales: 0,         // 商品销量
      spec: '',         // 商品规格信息
      lowPrice: '',     // 价格区间最低价
      highPrice: ''     // 价格区间最高价
    })
  }
})

// ==================== Props响应式解构 ====================
// 使用toRefs解构props，保持响应性
const { goodsInfo } = toRefs(props)

// ==================== 业务逻辑相关计算属性 ====================
// 计算属性：判断是否为ZQ业务场景
// 根据业务代码判断当前业务类型，用于控制价格显示方式
const isZQBiz = computed(() => getBizCode() === 'zq')
</script>

<style scoped lang="less">
.goods-card-mini {
  background: #FFFFFF;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 160px;
  min-width: 100px;
  display: flex;
  flex-direction: column;
}

.goods-image {
  position: relative;
  width: 100%;
  //height: 120px;
  overflow: hidden;
  background: #F8F9FA;
  flex: 1;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    background-color: #F8F9FA;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.goods-info {
  padding: 8px;
  background: #FFFFFF;

  .goods-name {
    font-size: 12px;
    font-weight: 400;
    color: #171E24;
    margin: 0 0 6px 0;
    line-height: 1.3;
    display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
    text-decoration: none;
  }

  .goods-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;

    .goods-price {
      color: var(--wo-biz-theme-color);
      font-size: 14px;
      font-weight: 700;
      line-height: 1;

      &::before {
        content: '¥';
        font-size: 11px;
        font-weight: 400;
        margin-right: 1px;
      }
    }

    .goods-price-range {
      display: flex;
      align-items: baseline;
      color: var(--wo-biz-theme-color);
      font-weight: 700;
      flex: 1 1 auto;
      min-width: 0;

      .goods-price-number {
        font-size: 12px;
        position: relative;
        padding-left: 8px;

        &::before {
          content: '¥';
          position: absolute;
          left: 0;
          top: 0;
          font-size: 11px;
          font-weight: 400;
        }
      }

      .goods-price-separator {
        margin: 0 2px;
        color: #718096;
        font-weight: 400;
      }
    }

    .goods-sales {
      color: #718096;
      font-size: 11px;
      line-height: 1;
      white-space: nowrap;
    }
  }

  .goods-spec {
    color: #4A5568;
    font-size: 11px;
    line-height: 1.2;
    background: #F8F9FA;
    padding: 2px 6px;
    border-radius: 2px;
    margin-top: 2px;
    display: inline-block;
    max-width: 100%;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }
}
</style>
