<!--
/**
 * 推荐区块组件
 *
 * 主要功能：
 * 1. 展示单个推荐区块的内容，包含标题图片和商品图片
 * 2. 支持多个推荐项目的并排显示
 * 3. 处理商品图片点击事件，支持跳转功能
 * 4. 提供悬停效果增强用户交互体验
 * 5. 采用响应式布局适配不同屏幕尺寸
 *
 * 技术特点：
 * - 使用Flexbox布局实现灵活的图片排列
 * - 支持事件发射进行父子组件通信
 * - 实现图片悬停效果和过渡动画
 * - 采用语义化的alt属性提升无障碍访问
 * - 支持动态内容渲染和条件显示
 *
 * 使用场景：
 * - 首页推荐商品区块展示
 * - 活动推广内容的图片展示
 * - 任何需要图片组合展示的推荐场景
 */
-->

<template>
  <!-- 推荐区块容器，仅在有推荐项目时显示 -->
  <div class="recommend-section" v-if="items.length > 0">
    <!-- 标题图片区域 -->
    <div class="title">
      <!-- 标题图片循环渲染 -->
      <!-- 每个推荐项目的标题图片，使用语义化alt属性 -->
      <img
        v-for="(item, index) in items"
        :key="`title-${index}`"
        :src="item.fontImgUrl"
        :alt="`推荐标题${index + 1}`"
      />
    </div>
    <!-- 商品图片区域 -->
    <div class="commodity">
      <!-- 商品图片循环渲染 -->
      <!-- 每个推荐项目的商品图片，支持点击跳转 -->
      <img
        v-for="(item, index) in items"
        :key="`commodity-${index}`"
        :src="item.actImgUrl"
        :alt="`推荐商品${index + 1}`"
        @click="$emit('imgClick', item.skipUrl)"
      />
    </div>
  </div>
</template>

<script setup>
// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
defineProps({
  // 推荐项目数据数组，包含标题图片、商品图片、跳转链接等信息
  items: {
    type: Array,
    default: () => []
  }
})

// ==================== 事件定义 ====================
// 定义组件向父组件发射的事件
defineEmits(['imgClick'])
</script>

<style lang="less" scoped>
.recommend-section {
  width: calc(50% - 5px);
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 4px;
  margin-bottom: 10px;

  .title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 10px;
    // padding: 0 10px;

    img:first-child {
      width: 80px;
      height: auto;
    }

    img:last-child {
      width: 65px;
      height: auto;
    }
  }

  .commodity {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 10px;
    margin-bottom: 10px;

    img {
      width: calc(50% - 5px);
      height: auto;
      cursor: pointer;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.7;
      }
    }
  }
}
</style>
