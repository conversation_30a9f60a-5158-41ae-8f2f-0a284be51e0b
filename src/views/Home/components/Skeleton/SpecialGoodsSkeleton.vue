<!--
/**
 * 特殊商品骨架屏组件
 *
 * 主要功能：
 * 1. 在特殊商品区域数据加载期间显示占位效果
 * 2. 模拟特殊商品区域的结构，包括热区和商品滚动区域
 * 3. 支持自定义骨架屏数量，适应不同的加载需求
 * 4. 提供流畅的加载动画效果，提升用户体验
 * 5. 采用组合布局，包含热区展示和商品列表
 *
 * 技术特点：
 * - 结合热区和滚动区域的复合布局
 * - 支持props配置骨架屏数量
 * - 使用CSS渐变和动画实现加载效果
 * - 模拟完整的特殊商品展示结构
 * - 支持响应式设计和媒体查询
 *
 * 使用场景：
 * - 首页特殊商品区域的加载状态
 * - 活动商品展示区域的占位显示
 * - 任何需要热区+商品列表骨架屏的场景
 */
-->

<template>
  <!-- 特殊商品骨架屏主容器 -->
  <div class="special-goods-skeleton">
    <!-- 热区骨架屏 -->
    <div class="skeleton-hot-zone">
      <!-- 热区内容占位 -->
      <div class="skeleton-hot-zone-content"></div>
    </div>

    <!-- 商品滚动区域骨架屏 -->
    <div class="skeleton-scroll-wrapper">
      <!-- 循环生成指定数量的骨架屏商品卡片 -->
      <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
        <!-- 骨架屏商品图片占位 -->
        <div class="skeleton-image"></div>
        <!-- 骨架屏商品内容区域 -->
        <div class="skeleton-content">
          <!-- 骨架屏商品标题占位（完整长度） -->
          <div class="skeleton-title"></div>
          <!-- 骨架屏商品标题占位（短长度） -->
          <div class="skeleton-title short"></div>
          <!-- 骨架屏商品详情区域 -->
          <div class="skeleton-details">
            <!-- 骨架屏价格占位 -->
            <div class="skeleton-price"></div>
            <!-- 骨架屏销量占位 -->
            <div class="skeleton-sales"></div>
          </div>
          <!-- 骨架屏商品规格占位 -->
          <div class="skeleton-spec"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 骨架屏显示数量，默认显示5个
  skeletonCount: {
    type: Number,
    default: 5
  }
})
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.special-goods-skeleton {

  position: relative;
  border-radius: 12px;
  margin: 8px 12px;
  overflow: hidden;


  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;


  min-height: 230px;
  display: flex;
  flex-direction: column;

  .skeleton-hot-zone {
    flex: 1;
    min-height: 40px;
    padding: 12px 12px 0 12px;

    .skeleton-hot-zone-content {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 100%;
      height: 100%;
      min-height: 40px;
      border-radius: 8px;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0.3) 75%);
    }
  }

  .skeleton-scroll-wrapper {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 12px;
    scroll-behavior: smooth;
    width: 100%;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };

    .skeleton-item {
      flex: 0 0 130px;
      background: #FFFFFF;
      border-radius: 6px;
      overflow: hidden;
      cursor: pointer;

      height: 200px;


      &:last-child {
        margin-right: 4px12;
      }

      .skeleton-image {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 100%;
        height: 120px;
        border-radius: 6px 6px 0 0;
        background-color: #F8F9FA;
      }

      .skeleton-content {

        padding: 8px;

        .skeleton-title {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

          height: 12px;
          margin-bottom: 6px;
          width: 100%;

          &.short {
            width: 70%;
            margin-bottom: 4px;
          }
        }

        .skeleton-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          gap: 8px;

          .skeleton-price {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

            height: 14px;
            width: 50px;
            flex-shrink: 0;
          }

          .skeleton-sales {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

            height: 11px;
            width: 40px;
            border-radius: 2px;
          }
        }

        .skeleton-spec {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

          height: 11px;
          width: 75%;
          border-radius: 2px;
        }
      }
    }
  }
}


@media (max-width: 375px) {
  .special-goods-skeleton {
    .skeleton-hot-zone {
      padding: 8px 8px 0 8px;

      .skeleton-hot-zone-content {
        min-height: 30px;
      }
    }

    .skeleton-scroll-wrapper {
      gap: 8px;
      padding: 8px;

      .skeleton-item {
        flex: 0 0 120px;
        height: 190px;

        &:last-child {
          margin-right: 8px;
        }

        .skeleton-image {
          height: 110px;
        }

        .skeleton-content {
          padding: 6px;

          .skeleton-title {
            height: 11px;
            margin-bottom: 4px;

            &.short {
              margin-bottom: 2px;
            }
          }

          .skeleton-details {
            margin-bottom: 2px;
            gap: 6px;

            .skeleton-price {
              height: 13px;
              width: 45px;
            }

            .skeleton-sales {
              height: 10px;
              width: 35px;
            }
          }

          .skeleton-spec {
            height: 10px;
          }
        }
      }
    }
  }
}
</style>
