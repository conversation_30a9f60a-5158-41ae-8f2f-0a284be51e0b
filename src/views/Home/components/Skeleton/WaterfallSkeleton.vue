<!--
/**
 * 瀑布流骨架屏组件
 *
 * 主要功能：
 * 1. 在瀑布流商品数据加载期间显示占位效果，保持页面布局稳定
 * 2. 模拟瀑布流商品卡片的结构，包括图片、标题、价格、销量等元素
 * 3. 提供随机高度的骨架屏项目，模拟真实瀑布流的不规则布局
 * 4. 支持自定义骨架屏数量，适应不同的加载需求
 * 5. 提供流畅的加载动画效果，提升用户体验
 *
 * 技术特点：
 * - 使用CSS Grid布局实现瀑布流效果
 * - 通过随机高度函数模拟真实商品卡片的高度差异
 * - 使用CSS渐变和动画实现加载效果
 * - 支持props配置骨架屏数量
 *
 * 使用场景：
 * - 商品列表页瀑布流的加载状态
 * - 首页商品推荐区域的占位显示
 * - 任何需要瀑布流骨架屏的场景
 */
-->

<template>
  <!-- 瀑布流骨架屏主容器 -->
  <div class="waterfall-skeleton">
    <!-- 骨架屏网格容器 -->
    <div class="skeleton-grid">
      <!-- 循环生成指定数量的骨架屏商品卡片 -->
      <!-- 每个卡片使用随机高度模拟真实瀑布流效果 -->
      <div
        v-for="i in skeletonCount"
        :key="i"
        class="skeleton-item"
        :style="{ height: getRandomHeight() }"
      >
        <!-- 骨架屏商品图片占位 -->
        <div class="skeleton-image"></div>
        <!-- 骨架屏商品内容区域 -->
        <div class="skeleton-content">
          <!-- 骨架屏商品标题占位（完整长度） -->
          <div class="skeleton-title"></div>
          <!-- 骨架屏商品标题占位（短长度） -->
          <div class="skeleton-title short"></div>
          <!-- 骨架屏商品详情区域 -->
          <div class="skeleton-details">
            <!-- 骨架屏价格占位 -->
            <div class="skeleton-price"></div>
            <!-- 骨架屏销量占位 -->
            <div class="skeleton-sales"></div>
          </div>
          <!-- 骨架屏商品规格占位 -->
          <div class="skeleton-spec"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 骨架屏显示数量，默认显示6个
  skeletonCount: {
    type: Number,
    default: 6
  }
})

// ==================== 随机高度生成 ====================
// 随机高度生成函数
// 从预定义的高度数组中随机选择一个高度值
// 用于模拟真实瀑布流中商品卡片的高度差异
const getRandomHeight = () => {
  // 预定义的高度数组，包含多种高度值
  const heights = ['280px', '290px', '300px', '285px', '295px', '275px']
  // 随机选择一个索引
  const index = Math.floor(Math.random() * heights.length)
  // 返回对应的高度值
  return heights[index]
}
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.waterfall-skeleton {
  // padding: 0 12px;

  .skeleton-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    min-height: 600px;

    .skeleton-item {
      background: #fff;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

      .skeleton-image {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 100%;
        height: 180px;
        border-radius: 8px 8px 0 0;
      }

      .skeleton-content {
        padding: 16px 14px 14px;

        .skeleton-title {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
          height: 15px;
          margin-bottom: 8px;
          width: 100%;

          &.short {
            width: 70%;
            margin-bottom: 12px;
          }
        }

        .skeleton-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          gap: 8px;

          .skeleton-price {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
            height: 20px;
            width: 60px;
            flex-shrink: 0;
          }

          .skeleton-sales {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
            height: 16px;
            width: 50px;
            border-radius: 10px;
          }
        }

        .skeleton-spec {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
          height: 14px;
          width: 80%;
          border-radius: 8px;
        }
      }
    }
  }
}


@media (max-width: 375px) {
  .waterfall-skeleton {
    .skeleton-grid {
      gap: 10px;

      .skeleton-item {
        .skeleton-image {
          height: 140px;
        }

        .skeleton-content {
          padding: 14px 12px 12px;

          .skeleton-title {
            height: 14px;
            margin-bottom: 6px;

            &.short {
              margin-bottom: 10px;
            }
          }

          .skeleton-details {
            .skeleton-price {
              height: 18px;
              width: 55px;
            }

            .skeleton-sales {
              height: 14px;
              width: 45px;
            }
          }

          .skeleton-spec {
            height: 12px;
          }
        }
      }
    }
  }
}
</style>
