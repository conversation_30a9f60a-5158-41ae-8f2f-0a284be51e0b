<!--
/**
 * 网格菜单骨架屏组件
 *
 * 主要功能：
 * 1. 在网格菜单数据加载期间显示占位效果，保持页面布局稳定
 * 2. 模拟网格菜单的结构，包括图标、标题、副标题等元素
 * 3. 提供流畅的加载动画效果，提升用户体验
 * 4. 支持响应式设计，适配不同屏幕尺寸
 * 5. 默认显示5个菜单项的骨架屏效果
 *
 * 技术特点：
 * - 使用CSS渐变和动画实现加载效果
 * - 采用Flexbox布局保持结构一致性
 * - 支持媒体查询实现响应式适配
 * - 无JavaScript逻辑，纯CSS实现
 *
 * 使用场景：
 * - 首页网格菜单加载时的占位显示
 * - 分类页面菜单项的加载状态
 * - 任何需要网格菜单骨架屏的场景
 */
-->

<template>
  <!-- 网格菜单骨架屏主容器 -->
  <div class="grid-menu-skeleton">
    <!-- 骨架屏网格容器 -->
    <div class="skeleton-grid-container">
      <!-- 循环生成5个骨架屏菜单项 -->
      <div v-for="i in 5" :key="i" class="skeleton-grid-item">
        <!-- 骨架屏图标占位 -->
        <div class="skeleton-icon"></div>
        <!-- 骨架屏标题占位 -->
        <div class="skeleton-title"></div>
        <!-- 骨架屏副标题占位 -->
        <div class="skeleton-subtitle"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件无需JavaScript逻辑，纯CSS实现加载效果
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.grid-menu-skeleton {

  background: #ffffff;
  border-radius: 12px;
  margin: 8px 0;

  padding: 5px;

  .skeleton-grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .skeleton-grid-item {
      width: calc(20% - 6.4px);
      display: flex;
      flex-direction: column;
      align-items: center;

      padding: 6px;
      text-align: center;
      box-sizing: border-box;

      .skeleton-icon {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

        width: 34px;
        height: 34px;
        border-radius: 4px;

        margin-bottom: 8px;
      }

      .skeleton-title {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 80%;

        height: 12px;
        margin-bottom: 2px;
      }

      .skeleton-subtitle {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 60%;

        height: 10px;
      }
    }
  }
}


@media (max-width: 375px) {
  .grid-menu-skeleton {
    .skeleton-grid-container {
      .skeleton-grid-item {

        padding: 6px;

        .skeleton-icon {

          width: 34px;
          height: 34px;
          margin-bottom: 8px;
        }

        .skeleton-title {
          height: 12px;
          margin-bottom: 2px;
        }

        .skeleton-subtitle {
          height: 10px;
        }
      }
    }
  }
}
</style>
