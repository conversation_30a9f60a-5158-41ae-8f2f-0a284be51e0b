<!--
/**
 * 分类骨架屏组件
 *
 * 主要功能：
 * 1. 在分类数据加载期间显示占位效果，保持页面布局稳定
 * 2. 模拟分类页面的结构，包括分类标题和子分类网格
 * 3. 提供流畅的加载动画效果，提升用户体验
 * 4. 支持响应式设计，适配不同屏幕尺寸
 * 5. 默认显示3个分类区块，每个区块包含6个子分类项
 *
 * 技术特点：
 * - 使用CSS Grid布局实现子分类网格效果
 * - 采用CSS渐变和动画实现加载效果
 * - 支持媒体查询实现响应式适配
 * - 使用阴影和圆角提升视觉效果
 * - 无JavaScript逻辑，纯CSS实现
 *
 * 使用场景：
 * - 分类页面数据加载时的占位显示
 * - 商品分类列表的加载状态
 * - 任何需要分类结构骨架屏的场景
 */
-->

<template>
  <!-- 分类骨架屏主容器 -->
  <div class="category-skeleton">
    <!-- 循环生成3个分类区块骨架屏 -->
    <div v-for="i in 3" :key="i" class="skeleton-category-item">
      <!-- 分类标题区域骨架屏 -->
      <div class="skeleton-category-header">
        <!-- 分类标题占位 -->
        <div class="skeleton-title"></div>
      </div>

      <!-- 分类内容区域骨架屏 -->
      <div class="skeleton-category-content">
        <!-- 子分类网格容器 -->
        <div class="skeleton-subcategory-grid">
          <!-- 循环生成6个子分类项骨架屏 -->
          <div v-for="j in 6" :key="j" class="skeleton-subcategory-item">
            <!-- 子分类图片占位 -->
            <div class="skeleton-image"></div>
            <!-- 子分类名称占位 -->
            <div class="skeleton-name"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件无需JavaScript逻辑，纯CSS实现加载效果
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.category-skeleton {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 5px;
}

.skeleton-category-item {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.skeleton-category-header {
  padding: 15px 20px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);

  .skeleton-title {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
    width: 120px;
    height: 18px;
    border-radius: 4px;
  }
}

.skeleton-category-content {
  padding: 0 20px 20px;
}

.skeleton-subcategory-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 20px;

  @media (max-width: 375px) {
    gap: 12px 16px;
    padding: 0 8px;
  }
}

.skeleton-subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 8px;

  .skeleton-image {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
    width: 68px;
    height: 68px;
    border-radius: 8px;
    margin-bottom: 8px;
  }

  .skeleton-name {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
    width: 60px;
    height: 14px;
    border-radius: 2px;
  }
}


@media (max-width: 375px) {
  .skeleton-category-header {
    padding: 12px 16px;

    .skeleton-title {
      width: 100px;
      height: 16px;
    }
  }

  .skeleton-category-content {
    padding: 0 16px 16px;
  }

  .skeleton-subcategory-item {
    padding: 6px;

    .skeleton-image {
      width: 56px;
      height: 56px;
      margin-bottom: 6px;
    }

    .skeleton-name {
      width: 50px;
      height: 12px;
    }
  }
}
</style>
