<!--
/**
 * 子轮播图骨架屏组件
 *
 * 主要功能：
 * 1. 在子轮播图数据加载期间显示占位效果，保持页面布局稳定
 * 2. 模拟子轮播图的基本结构和尺寸
 * 3. 提供流畅的加载动画效果，减少用户等待焦虑
 * 4. 支持响应式设计，适配不同屏幕尺寸
 * 5. 与实际子轮播图组件保持一致的视觉风格
 *
 * 技术特点：
 * - 使用CSS动画实现流畅的加载效果
 * - 采用渐变背景模拟内容加载状态
 * - 支持圆角和视觉效果
 * - 无JavaScript逻辑，纯CSS实现
 *
 * 使用场景：
 * - 页面子轮播图加载时的占位显示
 * - 次要轮播图区域的加载状态
 * - 任何需要子轮播图骨架屏的场景
 */
-->

<template>
  <!-- 子轮播图骨架屏主容器 -->
  <div class="sub-banner-skeleton">
    <!-- 骨架屏子轮播图容器 -->
    <div class="skeleton-sub-banner">
      <!-- 骨架屏图片占位区域 -->
      <div class="skeleton-image"></div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件无需JavaScript逻辑，纯CSS实现加载效果
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.sub-banner-skeleton {
  position: relative;
  width: 100%;
  margin: 8px 0;

  .skeleton-sub-banner {
    position: relative;
    width: 100%;
    height: 120px;
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;

    .skeleton-image {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 100%;
      height: 100%;
      border-radius: 12px;
      background-color: #f8f9fa;
    }
  }
}


@media (max-width: 375px) {
  .sub-banner-skeleton {
    margin: 8px 0;

    .skeleton-sub-banner {
      height: 100px;
    }
  }
}
</style>
