<!--
/**
 * 推荐区域骨架屏组件
 *
 * 主要功能：
 * 1. 在推荐商品数据加载期间显示占位效果，保持页面布局稳定
 * 2. 模拟推荐区域的结构，包括标题和商品信息等元素
 * 3. 提供流畅的加载动画效果，减少用户等待焦虑
 * 4. 支持多个推荐区块的并排显示，默认显示4个区块
 * 5. 采用响应式设计，适配不同屏幕尺寸
 *
 * 技术特点：
 * - 使用CSS渐变和动画实现加载效果
 * - 采用Flexbox布局实现响应式网格
 * - 支持圆角和背景色等视觉效果
 * - 无JavaScript逻辑，纯CSS实现
 *
 * 使用场景：
 * - 首页推荐商品区域的加载状态
 * - 商品列表页推荐区块的占位显示
 * - 任何需要推荐内容骨架屏的场景
 */
-->

<template>
  <!-- 推荐区域骨架屏主容器 -->
  <div class="recommend-skeleton">
    <!-- 循环生成4个推荐区块骨架屏 -->
    <div
      v-for="i in 4"
      :key="i"
      class="recommend-section-skeleton"
    >
      <!-- 标题区域骨架屏 -->
      <div class="title-skeleton">
        <!-- 标题左侧占位 -->
        <div class="skeleton-title-left"></div>
        <!-- 标题右侧占位 -->
        <div class="skeleton-title-right"></div>
      </div>
      <!-- 商品信息区域骨架屏 -->
      <div class="commodity-skeleton">
        <!-- 商品左侧信息占位 -->
        <div class="skeleton-commodity-left"></div>
        <!-- 商品右侧信息占位 -->
        <div class="skeleton-commodity-right"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件无需JavaScript逻辑，纯CSS实现加载效果
</script>

<style lang="less" scoped>
.recommend-skeleton {
  margin-top: 10px;
  padding: 0 5px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.recommend-section-skeleton {
  width: calc(50% - 5px);
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 10px;
  margin-bottom: 10px;
}

.title-skeleton {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 10px;
}

.skeleton-title-left {
  width: 80px;
  height: 20px;
  background: #f0f0f0;
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-title-right {
  width: 65px;
  height: 20px;
  background: #f0f0f0;
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  animation-delay: 0.1s;
}

.commodity-skeleton {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 10px;
  margin-bottom: 27px;
}

.skeleton-commodity-left,
.skeleton-commodity-right {
  width: calc(50% - 5px);
  height: 80px;
  background: #f0f0f0;
  border-radius: 6px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-commodity-right {
  animation-delay: 0.2s;
}

@keyframes skeleton-loading {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
</style>
