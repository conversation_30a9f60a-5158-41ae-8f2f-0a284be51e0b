<!--
/**
 * 第三轮播图骨架屏组件
 *
 * 主要功能：
 * 1. 在第三轮播图数据加载期间显示占位效果，保持页面布局稳定
 * 2. 模拟第三轮播图的左右分栏结构
 * 3. 提供流畅的加载动画效果，减少用户等待焦虑
 * 4. 支持响应式设计，适配不同屏幕尺寸
 * 5. 采用左右分栏布局，模拟真实的轮播图结构
 *
 * 技术特点：
 * - 使用Flexbox布局实现左右分栏效果
 * - 采用CSS渐变和动画实现加载效果
 * - 支持圆角和视觉效果
 * - 无JavaScript逻辑，纯CSS实现
 * - 模拟双图片并排的轮播图结构
 *
 * 使用场景：
 * - 页面第三轮播图加载时的占位显示
 * - 双图片轮播图区域的加载状态
 * - 任何需要分栏轮播图骨架屏的场景
 */
-->

<template>
  <!-- 第三轮播图骨架屏主容器 -->
  <div class="third-banner-skeleton">
    <!-- 骨架屏第三轮播图容器 -->
    <div class="skeleton-third-banner">
      <!-- 左侧图片区域 -->
      <div class="skeleton-left">
        <!-- 左侧骨架屏图片占位 -->
        <div class="skeleton-image"></div>
      </div>
      <!-- 右侧图片区域 -->
      <div class="skeleton-right">
        <!-- 右侧骨架屏图片占位 -->
        <div class="skeleton-image"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件无需JavaScript逻辑，纯CSS实现加载效果
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.third-banner-skeleton {
  margin: 8px 0;
  .skeleton-third-banner {
    display: flex;
    height: 210px;
    gap: 8px;

    .skeleton-left,
    .skeleton-right {
      display: flex;
      width: 50%;
      background: #ffffff;
      border-radius: 12px;
      overflow: hidden;

      .skeleton-image {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 100%;
        height: 100%;
        border-radius: 12px;
        background-color: #f8f9fa;
      }
    }
  }
}


@media (max-width: 375px) {
  .third-banner-skeleton {
    margin: 8px 0;
    .skeleton-third-banner {
      height: 180px;
      gap: 6px;
    }
  }
}
</style>
