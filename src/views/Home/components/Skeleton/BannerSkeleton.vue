<!--
/**
 * 轮播图骨架屏组件
 *
 * 主要功能：
 * 1. 在轮播图数据加载期间显示占位效果，提升用户体验
 * 2. 模拟轮播图的基本结构和尺寸，保持页面布局稳定
 * 3. 提供流畅的加载动画效果，减少用户等待焦虑
 * 4. 支持响应式设计，适配不同屏幕尺寸
 * 5. 与实际轮播图组件保持一致的视觉风格
 *
 * 技术特点：
 * - 使用CSS动画实现流畅的加载效果
 * - 采用渐变背景模拟内容加载状态
 * - 支持圆角和阴影等视觉效果
 * - 无JavaScript逻辑，纯CSS实现
 *
 * 使用场景：
 * - 首页轮播图加载时的占位显示
 * - 商品详情页轮播图的加载状态
 * - 任何需要轮播图骨架屏的场景
 */
-->

<template>
  <!-- 轮播图骨架屏主容器 -->
  <div class="banner-skeleton">
    <!-- 骨架屏轮播图容器 -->
    <div class="skeleton-banner">
      <!-- 骨架屏图片占位区域 -->
      <!-- 使用CSS动画模拟加载效果 -->
      <div class="skeleton-image"></div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件无需JavaScript逻辑，纯CSS实现加载效果
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.banner-skeleton {
  border-radius: 12px;
  overflow: hidden;

  .skeleton-banner {
    position: relative;
    width: 100%;
    height: 120px;
    background: #ffffff;
    border-radius: 12px;

    .skeleton-image {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 100%;
      height: 100%;
      border-radius: 12px;
      background-color: #f8f9fa;
    }


    .skeleton-pagination {
      position: absolute;
      bottom: 12px;
      right: 12px;
      background: rgba(0, 0, 0, 0.6);
      padding: 6px 12px;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.1);

      .skeleton-fraction {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 24px;
        height: 13px;
        border-radius: 2px;
        background: rgba(255, 255, 255, 0.8);
        animation: none;
      }
    }
  }
}
</style>
