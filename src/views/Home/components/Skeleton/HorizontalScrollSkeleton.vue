<!--
/**
 * 水平滚动骨架屏组件
 *
 * 主要功能：
 * 1. 在水平滚动商品列表数据加载期间显示占位效果
 * 2. 模拟水平滚动商品卡片的结构，包括图片、标题、价格等元素
 * 3. 支持自定义骨架屏数量，适应不同的加载需求
 * 4. 提供流畅的加载动画效果，提升用户体验
 * 5. 采用水平滚动布局，模拟真实的商品展示效果
 *
 * 技术特点：
 * - 使用Flexbox布局实现水平滚动效果
 * - 支持props配置骨架屏数量
 * - 使用CSS渐变和动画实现加载效果
 * - 模拟商品卡片的完整结构
 * - 支持响应式设计和媒体查询
 *
 * 使用场景：
 * - 首页水平滚动商品列表的加载状态
 * - 推荐商品区域的占位显示
 * - 任何需要水平滚动骨架屏的场景
 */
-->

<template>
  <!-- 水平滚动骨架屏主容器 -->
  <div class="horizontal-scroll-skeleton">
    <!-- 骨架屏滚动包装器 -->
    <div class="skeleton-scroll-wrapper">
      <!-- 循环生成指定数量的骨架屏商品卡片 -->
      <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
        <!-- 骨架屏商品图片占位 -->
        <div class="skeleton-image"></div>
        <!-- 骨架屏商品内容区域 -->
        <div class="skeleton-content">
          <!-- 骨架屏商品标题占位（完整长度） -->
          <div class="skeleton-title"></div>
          <!-- 骨架屏商品标题占位（短长度） -->
          <div class="skeleton-title short"></div>
          <!-- 骨架屏商品详情区域 -->
          <div class="skeleton-details">
            <!-- 骨架屏价格占位 -->
            <div class="skeleton-price"></div>
            <!-- 骨架屏销量占位 -->
            <div class="skeleton-sales"></div>
          </div>
          <!-- 骨架屏商品规格占位 -->
          <div class="skeleton-spec"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 骨架屏显示数量，默认显示5个
  skeletonCount: {
    type: Number,
    default: 5
  }
})
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.horizontal-scroll-skeleton {

  position: relative;
  min-height: 180px;
  display: flex;
  align-items: center;

  .skeleton-scroll-wrapper {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 8px;
    scroll-behavior: smooth;
    width: 100%;


    &::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none;
    scrollbar-width: none;

    .skeleton-item {
      flex: 0 0 160px;
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #f0f0f0;
      cursor: pointer;

      height: 280px;


      &:last-child {
        margin-right: 12px;
      }

      .skeleton-image {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 100%;
        height: 180px;
        border-radius: 8px 8px 0 0;
        background-color: #fafafa;
      }

      .skeleton-content {

        padding: 12px;

        .skeleton-title {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

          height: 14px;
          margin-bottom: 8px;
          width: 100%;

          &.short {
            width: 70%;
            margin-bottom: 8px;
          }
        }

        .skeleton-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;
          gap: 8px;

          .skeleton-price {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

            height: 16px;
            width: 60px;
            flex-shrink: 0;
          }

          .skeleton-sales {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

            height: 12px;
            width: 50px;
            border-radius: 6px;
          }
        }

        .skeleton-spec {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

          height: 12px;
          width: 80%;
          border-radius: 4px;
        }
      }
    }
  }
}


@media (max-width: 375px) {
  .horizontal-scroll-skeleton {
    .skeleton-scroll-wrapper {
      gap: 12px;

      .skeleton-item {
        flex: 0 0 160px;
        height: 280px;

        .skeleton-image {
          height: 180px;
        }

        .skeleton-content {
          padding: 12px;

          .skeleton-title {
            height: 14px;
            margin-bottom: 8px;

            &.short {
              margin-bottom: 8px;
            }
          }

          .skeleton-details {
            .skeleton-price {
              height: 16px;
              width: 60px;
            }

            .skeleton-sales {
              height: 12px;
              width: 50px;
            }
          }

          .skeleton-spec {
            height: 12px;
          }
        }
      }
    }
  }
}
</style>
