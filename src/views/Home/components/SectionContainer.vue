<!--
/**
 * 区块容器组件
 *
 * 主要功能：
 * 1. 提供统一的区块容器布局，包含标题头部和内容区域
 * 2. 支持可选的"更多"按钮，提供扩展操作入口
 * 3. 使用插槽机制支持自定义内容区域
 * 4. 提供统一的视觉风格和间距规范
 * 5. 支持点击事件处理和父子组件通信
 * 6. 实现响应式设计和过渡动画效果
 *
 * 技术特点：
 * - 使用语义化的HTML标签提升可访问性
 * - 采用Flexbox布局实现头部对齐
 * - 支持插槽内容的灵活定制
 * - 使用toRefs保持props响应性
 * - 集成过渡动画和悬停效果
 *
 * 使用场景：
 * - 首页各个功能区块的容器
 * - 商品列表区域的标题容器
 * - 任何需要标题+内容结构的区块
 */
-->

<template>
    <!-- 区块容器主体 -->
    <section class="home-block">
        <!-- 区块头部，包含标题和更多按钮 -->
        <div class="header">
            <!-- 区块标题，使用h2语义化标签 -->
            <h2 class="title">{{ title }}</h2>
            <!-- 更多按钮，仅在有more文本时显示 -->
            <!-- 包含文本和箭头图标，支持点击事件 -->
            <p class="more" v-if="more" @click="onClick">
                <span>{{ more }}</span>
                <span class="icon-arrow" />
            </p>
        </div>
        <!-- 区块内容区域 -->
        <!-- 使用插槽允许父组件自定义内容 -->
        <div class="content">
            <slot />
        </div>
    </section>
</template>

<script setup>
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
    // 区块标题，必填属性
    title: {
        type: String,
        required: true
    },
    // 更多按钮文本，可选属性
    more: {
        type: String,
        default: ''
    }
})

// 定义组件向父组件发射的事件
const emit = defineEmits(['click'])

// ==================== Props响应式解构 ====================
// 使用toRefs解构props，保持响应性
const { title, more } = toRefs(props)

// ==================== 事件处理方法 ====================
// 更多按钮点击事件处理函数
// 向父组件发射click事件
const onClick = () => {
    emit('click')
}
</script>

<style lang="less" scoped>
.home-block {
    margin: 10px 0;
    //background: #FFFFFF;
    overflow: hidden;
    transition: box-shadow 0.3s ease;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px 10px;

        .title {
            font-size: 18px;
            font-weight: 600;
            color: #171E24;
            line-height: 1.4;
            margin: 0;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: -12px;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 16px;
                background: linear-gradient(135deg, #EF4444, #F97316);
                border-radius: 2px;
            }
        }

        .more {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            color: #4A5568;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 20px;
            background: #F8F9FA;
            border: 1px solid transparent;
            transition: all 0.3s ease;

            &:hover {
                color: #EF4444;
                background: #FFFFFF;
                border-color: #EF4444;
                transform: translateY(-1px);
            }

            .icon-arrow {
                margin-left: 6px;
                width: 12px;
                height: 12px;
                background-image: url('assets/arrow.png');
                background-repeat: no-repeat;
                background-size: contain;
                background-position: center;
                transition: transform 0.3s ease;
                opacity: 0.7;
            }

            &:hover .icon-arrow {
                transform: translateX(3px);
                opacity: 1;
            }
        }
    }

    .content {
        //padding: 10px;
        //background: #FFFFFF;
    }
}
</style>
