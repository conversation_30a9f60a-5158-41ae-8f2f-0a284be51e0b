<!--
/**
 * 推荐商品视图组件
 *
 * 主要功能：
 * 1. 展示热门推荐商品列表，采用分组布局显示
 * 2. 将商品数据按每组2个进行分组，创建推荐区块
 * 3. 处理商品图片点击事件，支持跳转到商品详情
 * 4. 提供响应式布局，适配不同屏幕尺寸
 * 5. 集成RecommendSection组件，实现模块化展示
 *
 * 技术特点：
 * - 使用计算属性实现数据分组逻辑
 * - 支持事件代理处理子组件事件
 * - 采用Flexbox布局实现响应式设计
 * - 使用toRefs保持props响应性
 * - 实现页面跳转和导航功能
 *
 * 使用场景：
 * - 首页热门商品推荐区域
 * - 商品列表页的推荐商品展示
 * - 任何需要分组展示推荐内容的场景
 */
-->

<template>
  <!-- 推荐商品主容器 -->
  <div class="recommend">
    <!-- 推荐区块循环渲染 -->
    <!-- 每个区块包含2个商品，监听图片点击事件 -->
    <RecommendSection
      v-for="(section, index) in sections"
      :key="index"
      :items="section"
      @img-click="handleImgClick"
    />
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import RecommendSection from '@views/Home/components/RecommendSection.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 热门商品数据数组
  hotGoods: {
    type: Array,
    default: () => []
  }
})

// ==================== Props响应式解构 ====================
// 使用toRefs解构props，保持响应性
const { hotGoods } = toRefs(props)

// ==================== 数据分组处理 ====================
// 计算属性：将热门商品数据分组为推荐区块
// 每个区块包含2个商品，用于创建网格布局
const sections = computed(() => {
  const result = []
  // 按每组2个商品进行分组
  for (let i = 0; i < hotGoods.value.length; i += 2) {
    const section = hotGoods.value.slice(i, i + 2)
    // 只有当区块有商品时才添加到结果中
    if (section.length > 0) {
      result.push(section)
    }
  }
  return result
})

// ==================== 事件处理方法 ====================
// 商品图片点击处理函数
// 接收商品URL并进行页面跳转
const handleImgClick = (url) => {
  if (url) {
    // 跳转到商品详情页面
    window.location.href = url
  }
}
</script>

<style lang="less" scoped>
.recommend {
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}
</style>
