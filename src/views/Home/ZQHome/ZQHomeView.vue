<!--
/**
 * 政企服务首页组件
 *
 * 主要功能：
 * 1. 展示政企服务首页的完整布局，包括头部横幅、网格菜单和商品展示区域
 * 2. 集成省分服务商筛选功能，支持企业管理员按省分和服务商筛选商品
 * 3. 提供瀑布流商品展示，支持滚动加载和分页浏览功能
 * 4. 根据用户角色类型提供不同的数据加载策略，支持状态管理集成
 * 5. 集成搜索功能，支持用户搜索政企服务商品
 * 6. 实现骨架屏加载效果，提升用户体验
 *
 * 技术特点：
 * - 使用BaseHomeLayout作为基础布局组件，统一首页结构
 * - 集成ProvinceFilter省分筛选组件，支持异步加载优化性能
 * - 采用WaterfallSection瀑布流组件，支持滚动模式的无限加载
 * - 集成Pinia状态管理，支持省分服务商选择状态持久化
 * - 根据用户角色动态选择数据加载方式，提供个性化服务
 * - 实现响应式设计，适配不同屏幕尺寸
 *
 * 使用场景：
 * - 政企服务平台的主要入口页面
 * - 企业管理员浏览和筛选政企服务商品的核心界面
 * - 支持多省分多服务商的企业级服务展示平台
 */
-->

<template>
  <!-- 省分服务商筛选组件 -->
  <!-- 异步加载的筛选组件，支持企业管理员按省分和服务商筛选商品 -->
  <!-- 当用户确认选择时触发handleSelectionConfirm事件处理 -->
  <ProvinceFilter @confirm="handleSelectionConfirm" />

  <!-- 政企服务首页主容器 -->
  <!-- 使用BaseHomeLayout提供统一的首页布局结构 -->
  <!-- 配置搜索占位符、头部横幅、网格菜单等基础元素 -->
  <!-- 设置网格列数为5列，适配政企服务的菜单展示需求 -->
  <BaseHomeLayout
    home-class="zq-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <!-- 主要内容区域插槽 -->
    <template #main-content>
      <!-- 瀑布流商品展示组件 -->
      <!-- 采用滚动加载模式，支持无限滚动和分页加载 -->
      <!-- 集成骨架屏效果，提供流畅的加载体验 -->
      <WaterfallSection
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        loadMode="scroll"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted, computed, defineAsyncComponent } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getEnterpriseManagerInfo } from '@/utils/zqInfo'
import { useProvinceServiceStore } from '@/store/modules/provinceService.js'

// 异步加载省分筛选组件，优化首屏加载性能
// 只有在需要时才加载该组件，减少初始包体积
const ProvinceFilter = defineAsyncComponent(() => import('@/components/ZQCommon/ProvinceFilter.vue'))

// ==================== 首页基础数据和瀑布流功能 ====================
// 使用首页数据组合式函数，获取基础数据、瀑布流状态和相关工具函数
const {
  headerBannerList,        // 头部横幅列表数据
  gridMenuItems,           // 网格菜单项数据
  skeletonStates,          // 骨架屏显示状态控制
  waterfallGoodsList,      // 瀑布流商品列表数据
  waterfallLoading,        // 瀑布流加载状态
  waterfallFinished,       // 瀑布流加载完成状态
  waterfallCurrentPage,    // 瀑布流当前页码
  waterfallPageSize,       // 瀑布流每页数量
  waterfallButtonCanShow,  // 瀑布流加载更多按钮显示状态
  waterfallRenderComplete, // 瀑布流渲染完成状态
  getHeaderBannerList,     // 获取头部横幅数据的函数
  getIconList,             // 获取图标列表数据的函数
  getWaterfallList,        // 获取瀑布流商品列表的标准函数
  getWaterfallListWithStore, // 获取瀑布流商品列表的状态管理版本函数
  resetWaterfallState,     // 重置瀑布流状态的函数
  getPartionListData       // 获取商品分区列表数据的函数
} = useHomeData()

// ==================== 首页导航和交互功能 ====================
// 使用首页导航组合式函数，获取各种用户交互事件的处理函数
const {
  handleGoodsClick,        // 商品点击事件处理函数，跳转到商品详情页
  handleBannerClick,       // 横幅点击事件处理函数，处理横幅跳转逻辑
  handleGridItemClick,     // 网格菜单项点击事件处理函数，处理菜单导航
  handleMoreClick,         // 更多按钮点击事件处理函数，展开更多菜单
  handleSearch             // 搜索事件处理函数，处理搜索跳转和参数传递
} = useHomeNavigation()

// ==================== 状态管理集成 ====================
// 使用省分服务状态管理store，支持省分和服务商选择状态的持久化
const provinceServiceStore = useProvinceServiceStore()

// ==================== 政企服务页面特有数据管理 ====================
// 商品分类列表数据，用于商品分类管理
const typeList = ref([])
// 当前选中的商品池ID，用于瀑布流商品数据获取
const goodsPoolIdSelected = ref('')

// ==================== 用户角色类型计算 ====================
// 计算属性：获取当前用户的角色类型
// 根据角色类型决定使用不同的数据加载策略
const roleType = computed(() => {
  // 从企业管理员信息中获取角色类型
  const { roleType: rt = '' } = getEnterpriseManagerInfo() || {}
  return rt
})

// ==================== 瀑布流商品交互处理 ====================
// 瀑布流渲染完成后的回调处理函数
// 当瀑布流组件完成渲染后调用，用于更新渲染完成状态
const handleWaterfallAfterRender = () => {
  // 标记瀑布流渲染已完成，可用于后续的性能优化或状态控制
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多商品的事件处理函数
// 根据用户角色类型选择不同的数据加载策略
const handleWaterfallLoadMore = () => {
  // 角色类型为'4'的企业管理员使用支持状态管理的加载方法
  // 可以根据省分和服务商筛选条件获取商品数据
  if (roleType.value === '4') {
    getWaterfallListWithStore(goodsPoolIdSelected.value, '', true, provinceServiceStore)
  } else {
    // 其他角色使用标准的加载方法，获取全部商品数据
    getWaterfallList(goodsPoolIdSelected.value, '', true)
  }
}

// ==================== 商品池切换处理 ====================
// 商品池切换处理函数
// 根据用户角色类型选择不同的数据加载策略
const changeGoodsPool = (id, sortType = '') => {
  // 更新当前选中的商品池ID
  goodsPoolIdSelected.value = id
  // 重置瀑布流状态，清空现有数据和重置分页状态
  resetWaterfallState()

  // 根据用户角色类型选择合适的数据加载方法
  if (roleType.value === '4') {
    // 企业管理员（角色类型4）使用支持状态管理的加载方法
    // 会根据当前选择的省分和服务商进行数据筛选
    getWaterfallListWithStore(id, sortType, false, provinceServiceStore)
  } else {
    // 其他角色使用标准的加载方法，获取全部商品数据
    getWaterfallList(id, sortType, false)
  }
}

// ==================== 省分服务商筛选处理 ====================
// 省分服务商选择确认事件处理函数
// 当用户在省分筛选组件中确认选择后触发
const handleSelectionConfirm = (selection) => {
  // 输出选择结果到控制台，便于调试和日志记录
  console.log('确认选择，当前选中的省份ID:', selection.areaId)
  console.log('确认选择，当前选中的服务商ID:', selection.isvId)
  console.log('确认选择，当前选中的省份名称:', selection.provinceName)
  console.log('确认选择，当前选中的服务商名称:', selection.serviceName)

  // 将用户选择的省分信息保存到状态管理store中
  // 确保选择状态在页面刷新后仍然保持
  if (selection.areaId) {
    provinceServiceStore.selectProvince(selection.areaId)
  }

  // 将用户选择的服务商信息保存到状态管理store中
  if (selection.isvId) {
    provinceServiceStore.selectService(selection.isvId)
  }

  // 重置瀑布流状态，清空现有商品数据和分页状态
  // 为重新加载筛选后的商品数据做准备
  resetWaterfallState()

  // 使用支持状态管理的加载方法重新获取商品数据
  // 会根据新选择的省分和服务商条件进行数据筛选
  getWaterfallListWithStore(goodsPoolIdSelected.value, '', false, provinceServiceStore)
}

// ==================== 页面初始化数据加载 ====================
// 页面初始化数据加载的异步函数
// 负责获取商品分类数据和初始化默认商品池
const initPage = async () => {
  // 获取商品分区列表数据（类型2表示主要商品分类）
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  // 如果有商品分类数据，默认选择第一个分类并加载其商品数据
  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]  // 获取推荐分类（第一个分类）
    goodsPoolIdSelected.value = recommond.id  // 设置为当前选中的商品池ID
    changeGoodsPool(recommond.id)  // 切换到该商品池并加载数据
  }
}

// ==================== 组件生命周期管理 ====================
// 组件挂载完成后的初始化操作
// 按照页面展示顺序依次加载各个区域的数据
onMounted(() => {
  // 获取头部横幅数据，展示首页顶部轮播图
  getHeaderBannerList()

  // 获取网格菜单图标数据，政企服务使用showPage为8的图标配置
  getIconList(8)

  // 初始化页面数据，包括商品分类和默认商品池
  initPage()
})

// 组件卸载时的清理操作
// 预留清理函数，可用于取消未完成的请求或清理定时器
onUnmounted(() => {
  // 清理工作预留位置
  // 可在此处添加取消请求、清理定时器等操作
})
</script>

<style scoped lang="less">
.zq-home {
  padding-bottom: 48px;
  box-sizing: border-box;
}
</style>
