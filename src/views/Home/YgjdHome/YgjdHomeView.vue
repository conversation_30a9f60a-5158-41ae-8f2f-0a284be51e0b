<!--
/**
 * 员工家庭首页组件
 *
 * 主要功能：
 * 1. 展示员工家庭服务首页的完整布局，包括头部横幅、网格菜单和商品展示区域
 * 2. 提供热门商品推荐区域，展示精选的员工家庭服务商品
 * 3. 实现瀑布流商品展示，支持分页加载和无限滚动浏览
 * 4. 集成搜索功能，支持用户搜索员工家庭相关商品和服务
 * 5. 实现骨架屏加载效果和平滑过渡动画，提升用户体验
 * 6. 支持联通和非联通环境的差异化处理，适配不同运营商需求
 *
 * 技术特点：
 * - 使用BaseHomeLayout作为基础布局组件，统一首页结构
 * - 集成RecommendView推荐组件，展示热门商品推荐
 * - 采用WaterfallSection瀑布流组件，支持无限滚动加载
 * - 使用transition组件实现骨架屏与内容的平滑切换
 * - 实现响应式设计，适配不同屏幕尺寸
 * - 集成环境检测，根据运营商环境提供差异化体验
 *
 * 使用场景：
 * - 员工家庭服务平台的主要入口页面
 * - 用户浏览和搜索员工家庭服务商品的核心界面
 * - 展示热门商品推荐和分类浏览的营销页面
 */
-->

<template>
  <!-- 员工家庭首页主容器 -->
  <!-- 使用BaseHomeLayout提供统一的首页布局结构 -->
  <!-- 配置搜索占位符、头部横幅、网格菜单等基础元素 -->
  <!-- 设置网格列数为5列，适配员工家庭服务的菜单展示需求 -->
  <BaseHomeLayout
    home-class="ygjd-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <!-- 附加内容区域插槽，用于展示热门商品推荐 -->
    <template #additional-content>
      <!-- 热门商品推荐区域 -->
      <!-- 当骨架屏状态为true或热门商品数据有内容时显示 -->
      <!-- 使用transition组件实现骨架屏与内容的平滑切换 -->
      <div
        v-if="skeletonStates.recommend || hotGoods.length > 0"
        class="home-recommend-container"
      >
        <transition name="skeleton-fade" mode="out-in">
          <!-- 推荐区域骨架屏组件，在数据加载时显示 -->
          <RecommendSkeleton v-if="skeletonStates.recommend" key="recommend-skeleton" />
          <!-- 推荐商品内容组件，展示热门商品列表 -->
          <RecommendView
            v-else-if="hotGoods.length > 0"
            key="recommend-content"
            :hotGoods="hotGoods"
          />
        </transition>
      </div>
    </template>

    <!-- 主要内容区域插槽，展示瀑布流商品列表 -->
    <template #main-content>
      <!-- 瀑布流商品展示组件 -->
      <!-- 支持分页加载、无限滚动和渲染完成回调 -->
      <!-- 应用自定义样式类，调整容器内边距 -->
      <WaterfallSection
        class="ygjd-waterfall-section"
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import RecommendView from "@views/Home/components/RecommendView.vue"
import RecommendSkeleton from '@views/Home/components/Skeleton/RecommendSkeleton.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getHotGoods } from '@/api/interface/digitalVillage'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom } from 'commonkit'
import { closeToast, showLoadingToast } from 'vant'

// ==================== 首页基础数据和瀑布流功能 ====================
// 使用首页数据组合式函数，获取基础数据、瀑布流状态和相关工具函数
const {
  headerBannerList,        // 头部横幅列表数据
  gridMenuItems,           // 网格菜单项数据
  skeletonStates,          // 骨架屏显示状态控制
  moduleDataReady,         // 模块数据就绪状态，用于控制骨架屏隐藏时机
  waterfallGoodsList,      // 瀑布流商品列表数据
  waterfallLoading,        // 瀑布流加载状态
  waterfallFinished,       // 瀑布流加载完成状态
  waterfallButtonCanShow,  // 瀑布流加载更多按钮显示状态
  waterfallRenderComplete, // 瀑布流渲染完成状态
  getHeaderBannerList,     // 获取头部横幅数据的函数
  getIconList,             // 获取图标列表数据的函数
  getWaterfallList,        // 获取瀑布流商品列表的函数
  resetWaterfallState,     // 重置瀑布流状态的函数
  getPartionListData,      // 获取商品分区列表数据的函数
  hideSkeletonInOrder      // 按顺序隐藏骨架屏的函数，提供平滑的加载体验
} = useHomeData()

// ==================== 首页导航和交互功能 ====================
// 使用首页导航组合式函数，获取各种用户交互事件的处理函数
const {
  handleGoodsClick,        // 商品点击事件处理函数，跳转到商品详情页
  handleBannerClick,       // 横幅点击事件处理函数，处理横幅跳转逻辑
  handleGridItemClick,     // 网格菜单项点击事件处理函数，处理菜单导航
  handleMoreClick,         // 更多按钮点击事件处理函数，展开更多菜单
  handleSearch             // 搜索事件处理函数，处理搜索跳转和参数传递
} = useHomeNavigation()

// ==================== 员工家庭页面特有数据管理 ====================
// 商品分类列表数据，用于商品分类管理
const typeList = ref([])
// 当前选中的商品池ID，用于瀑布流商品数据获取
const goodsPoolIdSelected = ref('')
// 热门商品列表数据，用于推荐区域展示
const hotGoods = ref([])

// ==================== 骨架屏状态扩展配置 ====================
// 扩展骨架屏状态配置，为员工家庭页面特有的推荐区域添加加载状态控制
skeletonStates.value = {
  ...skeletonStates.value,  // 保留原有的骨架屏状态配置
  recommend: true           // 推荐区域骨架屏状态
}

// ==================== 模块数据就绪状态配置 ====================
// 扩展模块数据就绪状态配置，用于控制推荐区域骨架屏的隐藏时机
moduleDataReady.value = {
  ...moduleDataReady.value, // 保留原有的模块数据就绪状态
  recommend: false          // 推荐区域数据就绪状态
}

// ==================== 瀑布流商品交互处理 ====================
// 瀑布流渲染完成后的回调处理函数
// 当瀑布流组件完成渲染后调用，用于更新渲染完成状态
const handleWaterfallAfterRender = () => {
  // 标记瀑布流渲染已完成，可用于后续的性能优化或状态控制
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多商品的事件处理函数
// 当用户触发加载更多操作时调用，获取下一页商品数据
const handleWaterfallLoadMore = () => {
  // 调用瀑布流数据获取函数，传入当前选中的商品池ID
  // 第二个参数为排序类型（空字符串表示默认排序）
  // 第三个参数为true表示这是加载更多操作
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// ==================== 商品池切换处理 ====================
// 商品池切换处理函数
// 用于完全重置瀑布流状态并加载新的商品池数据
const changeGoodsPool = (id, sortType = '') => {
  // 更新当前选中的商品池ID
  goodsPoolIdSelected.value = id
  // 完全重置瀑布流状态，包括清空数据和重置所有状态
  resetWaterfallState()
  // 获取新商品池的数据，第三个参数false表示首次加载
  getWaterfallList(id, sortType, false)
}

// ==================== 热门商品推荐数据获取 ====================
// 初始化热门商品推荐数据的异步函数
// 用于推荐区域的热门商品展示
const initHotGoods = async () => {
  // 构建热门商品请求参数
  const params = {
    showPage: '1',                    // 显示页面标识，固定为1表示首页推荐
    bizCode: getBizCode('GOODS'),     // 商品业务代码
    channel: curChannelBiz.get()      // 当前渠道业务标识
  }

  // 非联通环境下显示全局加载提示
  // 联通环境使用自己的加载机制，不显示vant的加载提示
  if (!isUnicom) {
    showLoadingToast()
  }

  // 调用热门商品API获取推荐数据
  const [err, json] = await getHotGoods(params)

  // 非联通环境下关闭全局加载提示
  if (!isUnicom) {
    closeToast()
  }

  // 处理API请求错误情况
  if (err) {
    // 输出错误信息到控制台，便于调试
    console.error('获取热门商品失败:', err.msg)
    // 标记推荐数据已就绪（即使失败也要隐藏骨架屏）
    moduleDataReady.value.recommend = true
    // 按指定顺序隐藏骨架屏，避免页面一直显示加载状态
    await hideSkeletonInOrder(['banner', 'gridMenu', 'recommend'])
    return
  }

  // 处理成功响应数据
  const arr = json || []  // 确保数据为数组格式，避免空值导致的错误
  hotGoods.value = arr    // 设置热门商品数据

  // 标记推荐数据已就绪
  moduleDataReady.value.recommend = true
  // 按指定顺序隐藏骨架屏，依次隐藏：主横幅 -> 网格菜单 -> 推荐区域
  await hideSkeletonInOrder(['banner', 'gridMenu', 'recommend'])
}

// ==================== 页面初始化数据加载 ====================
// 页面初始化数据加载的异步函数
// 负责获取商品分类数据和初始化默认商品池
const initPage = async () => {
  // 获取商品分区列表数据（类型2表示主要商品分类）
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  // 如果有商品分类数据，默认选择第一个分类并加载其商品数据
  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]  // 获取推荐分类（第一个分类）
    goodsPoolIdSelected.value = recommond.id  // 设置为当前选中的商品池ID
    changeGoodsPool(recommond.id)  // 切换到该商品池并加载数据
  }
}

// ==================== 组件生命周期管理 ====================
// 组件挂载完成后的初始化操作
// 按照页面展示顺序依次加载各个区域的数据
onMounted(() => {
  // 获取头部横幅数据，展示首页顶部轮播图
  getHeaderBannerList()

  // 获取网格菜单图标数据，员工家庭使用showPage为7的图标配置
  getIconList(7)

  // 初始化热门商品推荐数据
  initHotGoods()

  // 初始化页面数据，包括商品分类和默认商品池
  initPage()
})

// 组件卸载时的清理操作
// 预留清理函数，可用于取消未完成的请求或清理定时器
onUnmounted(() => {
  // 清理工作预留位置
  // 可在此处添加取消请求、清理定时器等操作
})
</script>

<style scoped lang="less">
.ygjd-home {
  //padding: 10px;
  //box-sizing: border-box;

  .home-recommend-container {
    padding: 0 10px;
    position: relative;
    box-sizing: border-box;
  }

  .ygjd-waterfall-section {
    :deep(.home-waterfall-container) {
      padding: 0 10px;
    }
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
