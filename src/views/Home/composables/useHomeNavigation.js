/**
 * @fileoverview 首页导航管理组合式函数
 * @description 提供首页各种导航和点击事件的处理逻辑，包括商品点击、Banner点击、网格菜单点击等
 * @example
 * import { useHomeNavigation } from './useHomeNavigation'
 *
 * export default {
 *   setup() {
 *     const {
 *       handleGoodsClick,
 *       handleBannerClick,
 *       handleGridItemClick,
 *       handleMoreClick,
 *       handleActivityClick
 *     } = useHomeNavigation()
 *
 *     return {
 *       handleGoodsClick,
 *       handleBannerClick,
 *       handleGridItemClick,
 *       handleMoreClick,
 *       handleActivityClick
 *     }
 *   }
 * }
 */

import { useRouter } from 'vue-router'
import {computed} from "vue";
import {getCustomerManagerInfo, getEnterpriseManagerInfo} from "@utils/zqInfo.js";
import {getBizCode} from "@utils/curEnv.js";

/**
 * 首页导航管理组合式函数
 * @description 提供首页各种导航和点击事件的处理逻辑
 * @returns {Object} 导航处理函数集合
 * @returns {Function} returns.handleGoodsClick - 处理商品点击事件
 * @returns {Function} returns.handleBannerClick - 处理Banner点击事件
 * @returns {Function} returns.handleGridItemClick - 处理网格菜单点击事件
 * @returns {Function} returns.handleMoreClick - 处理更多按钮点击事件
 * @returns {Function} returns.handleActivityClick - 处理活动点击事件
 * @example
 * const {
 *   handleGoodsClick,
 *   handleBannerClick,
 *   handleGridItemClick,
 *   handleMoreClick,
 *   handleActivityClick
 * } = useHomeNavigation()
 */
export function useHomeNavigation() {
  const router = useRouter()

  const bizCode = getBizCode()

  /**
   * 角色类型计算属性
   * @description 根据企业管理员信息或客户管理员信息获取角色类型
   * @type {import('vue').ComputedRef<string>}
   */
  const roleType = computed(() => {
    const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
    return roleType
  })

  /**
   * 处理商品点击事件
   * @description 根据业务代码和角色类型跳转到相应的商品详情页面
   * @param {Object} goodsInfo - 商品信息对象
   * @param {string} goodsInfo.goodsId - 商品ID
   * @returns {void}
   * @example
   * handleGoodsClick({ goodsId: '12345' })
   */
  const handleGoodsClick = (goodsInfo) => {
    if(bizCode === 'zq') {
      if(roleType.value === '1' || roleType.value === '3') {
        router.push(`/zq/goodsdetail/${goodsInfo.goodsId}`)
        return
      } else if(roleType.value === '2') {
        router.push(`/zq/cm/goodsdetail/${goodsInfo.goodsId}`)
        return
      }
    }

    if (goodsInfo.goodsId) {
      router.push(`/goodsdetail/${goodsInfo.goodsId}`)
    }
  }

  /**
   * 处理Banner点击事件
   * @description 点击Banner时跳转到指定的链接地址
   * @param {Object} params - 参数对象
   * @param {Object} params.item - Banner项目信息
   * @param {string} params.item.linkUrl - Banner链接地址
   * @returns {void}
   * @example
   * handleBannerClick({ item: { linkUrl: 'https://example.com' } })
   */
  const handleBannerClick = ({ item }) => {
    if (item.linkUrl) {
      window.location.href = item.linkUrl
    }
  }

  /**
   * 处理网格菜单点击事件
   * @description 点击网格菜单项时跳转到指定的URL地址
   * @param {Object} params - 参数对象
   * @param {Object} params.item - 网格菜单项信息
   * @param {string} params.item.url - 菜单项链接地址
   * @returns {void}
   * @example
   * handleGridItemClick({ item: { url: 'https://example.com' } })
   */
  const handleGridItemClick = ({ item }) => {
    if (item.url) {
      window.location.href = item.url
    }
  }

  /**
   * 处理更多按钮点击事件
   * @description 处理更多按钮的点击逻辑，可以根据需要实现具体功能
   * @returns {void}
   * @example
   * handleMoreClick()
   */
  const handleMoreClick = () => {
    // 可以根据需要实现具体逻辑
    console.log('更多按钮被点击')
  }

  /**
   * 处理活动点击事件
   * @description 点击活动项时跳转到指定的URL地址
   * @param {Object} item - 活动项信息
   * @param {string} item.url - 活动链接地址
   * @param {string} [position=''] - 活动位置标识
   * @returns {void}
   * @example
   * handleActivityClick({ url: 'https://example.com' }, 'banner')
   */
  const handleActivityClick = (item, position = '') => {
    if (item.url) {
      window.location.href = item.url
    }
  }

  return {
    handleGoodsClick,
    handleBannerClick,
    handleGridItemClick,
    handleMoreClick,
    handleActivityClick
  }
}
