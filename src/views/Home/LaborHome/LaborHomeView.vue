<!--
/**
 * 劳动者服务首页组件
 *
 * 主要功能：
 * 1. 展示劳动者服务首页的完整布局，包括头部横幅、网格菜单和商品展示区域
 * 2. 提供瀑布流商品展示，支持分页加载和无限滚动浏览
 * 3. 集成搜索功能，支持用户搜索劳动者相关商品和服务
 * 4. 实现骨架屏加载效果，提升用户体验
 * 5. 支持商品池切换和数据管理，提供灵活的商品展示策略
 *
 * 技术特点：
 * - 使用BaseHomeLayout作为基础布局组件，统一首页结构
 * - 采用WaterfallSection瀑布流组件，支持无限滚动加载
 * - 实现响应式设计，适配不同屏幕尺寸
 * - 集成组合式函数，提供模块化的功能管理
 * - 支持商品分类和数据筛选功能
 *
 * 使用场景：
 * - 劳动者服务平台的主要入口页面
 * - 用户浏览和搜索劳动者服务商品的核心界面
 * - 展示劳动者相关商品和服务的营销页面
 */
-->

<template>
  <!-- 劳动者服务首页主容器 -->
  <!-- 使用BaseHomeLayout提供统一的首页布局结构 -->
  <!-- 配置搜索占位符、头部横幅、网格菜单等基础元素 -->
  <!-- 设置网格列数为5列，适配劳动者服务的菜单展示需求 -->
  <BaseHomeLayout
    home-class="labor-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <!-- 主要内容区域插槽，展示瀑布流商品列表 -->
    <template #main-content>
      <!-- 瀑布流商品展示组件 -->
      <!-- 支持分页加载、无限滚动和渲染完成回调 -->
      <WaterfallSection
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// ==================== 首页基础数据和瀑布流功能 ====================
// 使用首页数据组合式函数，获取基础数据、瀑布流状态和相关工具函数
const {
  headerBannerList,        // 头部横幅列表数据
  gridMenuItems,           // 网格菜单项数据
  skeletonStates,          // 骨架屏显示状态控制
  waterfallGoodsList,      // 瀑布流商品列表数据
  waterfallLoading,        // 瀑布流加载状态
  waterfallFinished,       // 瀑布流加载完成状态
  waterfallButtonCanShow,  // 瀑布流加载更多按钮显示状态
  waterfallRenderComplete, // 瀑布流渲染完成状态
  getHeaderBannerList,     // 获取头部横幅数据的函数
  getIconList,             // 获取图标列表数据的函数
  getWaterfallList,        // 获取瀑布流商品列表的函数
  resetWaterfallState,     // 重置瀑布流状态的函数
  getPartionListData       // 获取商品分区列表数据的函数
} = useHomeData()

// ==================== 首页导航和交互功能 ====================
// 使用首页导航组合式函数，获取各种用户交互事件的处理函数
const {
  handleGoodsClick,        // 商品点击事件处理函数，跳转到商品详情页
  handleBannerClick,       // 横幅点击事件处理函数，处理横幅跳转逻辑
  handleGridItemClick,     // 网格菜单项点击事件处理函数，处理菜单导航
  handleMoreClick,         // 更多按钮点击事件处理函数，展开更多菜单
  handleSearch             // 搜索事件处理函数，处理搜索跳转和参数传递
} = useHomeNavigation()

// ==================== 劳动者服务页面特有数据管理 ====================
// 商品分类列表数据，用于商品分类管理
const typeList = ref([])
// 当前选中的商品池ID，用于瀑布流商品数据获取
const goodsPoolIdSelected = ref('')

// ==================== 瀑布流商品交互处理 ====================
// 瀑布流渲染完成后的回调处理函数
// 当瀑布流组件完成渲染后调用，用于更新渲染完成状态
const handleWaterfallAfterRender = () => {
  // 标记瀑布流渲染已完成，可用于后续的性能优化或状态控制
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多商品的事件处理函数
// 当用户触发加载更多操作时调用，获取下一页商品数据
const handleWaterfallLoadMore = () => {
  // 调用瀑布流数据获取函数，传入当前选中的商品池ID
  // 第二个参数为排序类型（空字符串表示默认排序）
  // 第三个参数为true表示这是加载更多操作
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// ==================== 商品池切换处理 ====================
// 商品池切换处理函数
// 用于完全重置瀑布流状态并加载新的商品池数据
const changeGoodsPool = (id, sortType = '') => {
  // 更新当前选中的商品池ID
  goodsPoolIdSelected.value = id
  // 完全重置瀑布流状态，包括清空数据和重置所有状态
  resetWaterfallState()
  // 获取新商品池的数据，第三个参数false表示首次加载
  getWaterfallList(id, sortType, false)
}

// ==================== 页面初始化数据加载 ====================
// 页面初始化数据加载的异步函数
// 负责获取商品分类数据和初始化默认商品池
const initPage = async () => {
  // 获取商品分区列表数据（类型2表示主要商品分类）
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  // 如果有商品分类数据，默认选择第一个分类并加载其商品数据
  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]  // 获取推荐分类（第一个分类）
    goodsPoolIdSelected.value = recommond.id  // 设置为当前选中的商品池ID
    changeGoodsPool(recommond.id)  // 切换到该商品池并加载数据
  }
}

// ==================== 组件生命周期管理 ====================
// 组件挂载完成后的初始化操作
// 按照页面展示顺序依次加载各个区域的数据
onMounted(() => {
  // 获取头部横幅数据，展示首页顶部轮播图
  getHeaderBannerList()

  // 获取网格菜单图标数据，劳动者服务使用showPage为6的图标配置
  getIconList(6)

  // 初始化页面数据，包括商品分类和默认商品池
  initPage()
})

// 组件卸载时的清理操作
// 预留清理函数，可用于取消未完成的请求或清理定时器
onUnmounted(() => {
  // 清理工作预留位置
  // 可在此处添加取消请求、清理定时器等操作
})
</script>

<style scoped lang="less">
.labor-home {
}
</style>
