<!--
/**
 * 数字乡村首页组件
 *
 * 主要功能：
 * 1. 展示数字乡村服务首页的完整布局，包括轮播横幅和分类导航
 * 2. 提供轮播横幅展示，支持自动播放、循环播放和分页指示器
 * 3. 实现多级分类展示，支持二级分类和三级分类的层级导航
 * 4. 提供分类商品浏览功能，支持用户点击分类跳转到商品列表
 * 5. 实现骨架屏加载效果，提升用户体验
 * 6. 支持批量数据加载优化，避免接口并发过多导致的性能问题
 *
 * 技术特点：
 * - 使用GoodsSwiper轮播组件，支持多种轮播配置
 * - 采用分批加载策略，优化大量分类数据的加载性能
 * - 集成骨架屏组件，提供细粒度的加载状态控制
 * - 使用防抖处理用户点击事件，避免重复操作
 * - 实现响应式设计，适配不同屏幕尺寸
 * - 集成错误处理机制，提供友好的错误提示
 *
 * 使用场景：
 * - 数字乡村服务平台的主要入口页面
 * - 用户浏览和选择乡村服务分类的核心界面
 * - 展示乡村服务推荐和分类导航的营销页面
 */
-->

<template>
  <!-- 数字乡村首页主容器 -->
  <div class="sfzn-home">
    <!-- 轮播横幅区域 -->
    <section class="banner-section">
      <!-- 横幅骨架屏，在数据加载时显示 -->
      <BannerSkeleton v-if="skeletonStates.banner" />

      <!-- 轮播横幅内容，当有图片数据时显示 -->
      <!-- 支持自动播放、循环播放和分页指示器功能 -->
      <div v-else-if="imageList && imageList.length > 0" class="banner-wrapper">
        <GoodsSwiper
          :image-list="imageList"
          :autoplay="true"
          :autoplay-delay="3000"
          :loop="true"
          :show-pagination="true"
          mode="landscape"
          @image-click="onImageClick"
        />
      </div>
    </section>

    <!-- 分类导航区域 -->
    <section class="category-section">
      <!-- 分类骨架屏，在数据加载时显示 -->
      <CategorySkeleton v-if="skeletonStates.category" />

      <!-- 空状态提示，当没有分类数据时显示 -->
      <div v-else-if="isEmpty" class="empty-state">
        <div class="empty-text">暂无分类数据</div>
      </div>

      <!-- 分类列表内容，展示多级分类结构 -->
      <div v-else class="category-list">
        <!-- 二级分类项，每个分类作为一个独立的卡片展示 -->
        <article
          v-for="item in secondList"
          :key="item.id"
          class="category-item"
          :style="getCategoryItemStyle(item.img)"
        >
          <!-- 分类头部，显示分类名称 -->
          <header class="category-header">
            <h2 class="category-title">{{ item.name }}</h2>
          </header>

          <!-- 分类内容，展示三级分类网格 -->
          <div class="category-content">
            <div class="subcategory-grid">
              <!-- 三级分类项，支持点击跳转到商品列表 -->
              <div
                v-for="subcategory in item.list"
                :key="subcategory.id"
                class="subcategory-item"
                @click="onSubcategoryClick(subcategory)"
              >
                <!-- 三级分类图片容器 -->
                <div class="subcategory-image-wrapper">
                  <img
                    class="subcategory-image"
                    :src="subcategory.img"
                    alt=""
                    loading="lazy"
                    @error="handleImageError"
                  />
                </div>
                <!-- 三级分类名称 -->
                <span class="subcategory-name">{{ subcategory.name }}</span>
              </div>
            </div>
          </div>
        </article>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce } from 'es-toolkit'
import GoodsSwiper from '@/components/Common/GoodsSwiper.vue'
import BannerSkeleton from '@/views/Home/components/Skeleton/BannerSkeleton.vue'
import CategorySkeleton from '../components/Skeleton/CategorySkeleton.vue'
import { getBannerInfo } from '@/api/interface/bannerIcon'
import { getClassification } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { showToast } from 'vant'
import { categoryPid } from '@utils/storage.js'

// ==================== 骨架屏状态管理 ====================
// 骨架屏显示状态控制，用于在数据加载时显示加载效果
const skeletonStates = ref({
  banner: true,      // 横幅区域骨架屏状态
  category: true     // 分类区域骨架屏状态
})

// ==================== 轮播横幅数据管理 ====================
// 轮播图片列表数据，存储从服务器获取的横幅信息
const imageList = ref([])

// ==================== 分类数据管理 ====================
// 二级分类列表数据，存储分类层级结构信息
const secondList = ref([])
// 数据加载状态，控制加载过程中的状态显示
const loading = ref(false)

// ==================== 计算属性 ====================
// 计算属性：判断是否为空状态
// 当不在加载中且分类列表为空时，显示空状态提示
const isEmpty = computed(() => !loading.value && secondList.value.length === 0)

// ==================== 路由管理 ====================
// 获取当前路由信息，用于参数传递和页面跳转
const route = useRoute()
// 获取路由器实例，用于编程式导航
const router = useRouter()

// ==================== 样式和图片处理工具函数 ====================
// 获取分类项背景样式的函数
// 根据分类图片URL生成对应的CSS背景样式
const getCategoryItemStyle = (imgUrl) => {
  // 如果没有图片URL，返回空样式对象
  if (!imgUrl) return {}

  // 返回背景图片相关的CSS样式配置
  return {
    backgroundImage: `url(${imgUrl})`,     // 设置背景图片
    backgroundRepeat: 'no-repeat',         // 不重复背景图片
    backgroundSize: '100% 100%',           // 背景图片完全覆盖容器
    backgroundPosition: 'center'           // 背景图片居中显示
  }
}

// 图片加载错误处理函数
// 当图片加载失败时隐藏图片元素，避免显示破损图片图标
const handleImageError = (event) => {
  // 输出警告信息到控制台，便于调试
  console.warn('图片加载失败:', event.target.src)
  // 隐藏加载失败的图片元素
  event.target.style.display = 'none'
}

// ==================== 轮播横幅初始化和交互 ====================
// 初始化轮播横幅数据的异步函数
// 从服务器获取横幅配置信息并设置轮播组件
const initSwiper = async () => {
  try {
    // 构建横幅信息请求参数
    const params = {
      showPage: '1',                    // 显示页面标识，固定为1表示首页横幅
      bizCode: getBizCode('GOODS')      // 商品业务代码
    }

    // 调用横幅信息API获取数据
    const [err, json] = await getBannerInfo(params)

    // 处理API请求错误情况
    if (err) {
      console.error('获取轮播数据失败:', err.msg)
      showToast(err.msg)
      return
    }

    // 处理成功响应数据
    if (json && json.length > 0) {
      // 将原始横幅数据转换为轮播组件所需的格式
      imageList.value = json.map((item, index) => ({
        id: item.id || index,                                    // 横幅ID，如果没有则使用索引
        url: item.imgUrl,                                        // 横幅图片URL
        alt: item.bannerChName || `轮播图 ${index + 1}`,         // 图片描述文本
        title: item.bannerChName,                                // 横幅标题
        linkUrl: item.url                                        // 横幅点击跳转链接
      }))
    }
  } catch (error) {
    // 捕获并处理异常情况
    console.error('轮播初始化失败:', error)
    showToast('轮播初始化失败')
  } finally {
    // 无论成功或失败，都要隐藏骨架屏
    await nextTick()
    // 延迟300ms隐藏骨架屏，提供平滑的过渡效果
    setTimeout(() => {
      skeletonStates.value.banner = false
    }, 300)
  }
}

// 轮播图片点击事件处理函数
// 使用防抖处理，避免用户快速点击导致的重复跳转
const onImageClick = debounce(({ item }) => {
  // 如果横幅配置了跳转链接，则进行页面跳转
  if (item.linkUrl) {
    window.location.href = item.linkUrl
  }
}, 300)

// ==================== 分类数据获取和处理 ====================
// 获取指定分类下的子分类数据的异步函数
// 用于获取分类层级结构中的下级分类信息
const fetchClassification = async (id) => {
  try {
    // 调用分类信息API获取子分类数据
    const [err, json] = await getClassification({
      bizCode: getBizCode('GOODS'),     // 商品业务代码
      category_pid: id,                 // 父分类ID
      page_no: 1,                       // 页码，固定为第一页
      page_size: 500                    // 每页数量，设置较大值获取所有子分类
    })

    // 处理API请求错误情况
    if (err) {
      console.error('获取分类数据失败:', err.msg)
      return []
    }

    // 返回分类数据，如果为空则返回空数组
    return json || []
  } catch (error) {
    // 捕获并处理异常情况
    console.error('获取分类数据失败:', error)
    return []
  }
}

// 分批处理分类数据的异步函数
// 避免同时发起过多API请求导致的性能问题和服务器压力
const processCategoriesInBatches = async (categories, batchSize = 3) => {
  const results = []

  // 按批次大小分割分类数组，逐批处理
  for (let i = 0; i < categories.length; i += batchSize) {
    const batch = categories.slice(i, i + batchSize)

    // 为当前批次的每个分类创建异步任务
    const batchPromises = batch.map(async (item, batchIndex) => {
      const actualIndex = i + batchIndex  // 计算在原数组中的实际索引
      try {
        // 获取当前分类的三级分类数据
        const thirdList = await fetchClassification(item.id)
        // 按位置字段降序排序，确保显示顺序正确
        const sortedThirdList = thirdList.sort((a, b) => b.pos - a.pos)
        return { index: actualIndex, list: sortedThirdList }
      } catch (error) {
        // 处理单个分类获取失败的情况
        console.error(`获取三级分类失败 (ID: ${item.id}):`, error)
        return { index: actualIndex, list: [] }
      }
    })

    // 等待当前批次的所有请求完成，使用allSettled确保不会因单个失败而中断
    const batchResults = await Promise.allSettled(batchPromises)
    results.push(...batchResults)
  }

  return results
}

// ==================== 分类数据初始化 ====================
// 初始化分类数据的异步函数
// 获取二级分类和对应的三级分类数据，构建完整的分类层级结构
const initCategoryData = async () => {
  // 防止重复加载，如果正在加载中则直接返回
  if (loading.value) return

  try {
    // 设置加载状态为true
    loading.value = true

    // 获取二级分类数据，使用存储的分类父ID
    const secondCategories = await fetchClassification(categoryPid.get())

    // 如果没有二级分类数据，设置空列表并返回
    if (!secondCategories || secondCategories.length === 0) {
      secondList.value = []
      return
    }

    // 按位置字段降序排序二级分类，确保显示顺序正确
    const sortedSecondCategories = secondCategories.sort((a, b) => b.pos - a.pos)

    // 将二级分类数据转换为组件所需的格式
    // 初始时三级分类列表为空，后续异步填充
    const transList = sortedSecondCategories.map(item => ({
      id: item.id,        // 分类ID
      name: item.name,    // 分类名称
      img: item.img,      // 分类图片
      list: []            // 三级分类列表，初始为空
    }))

    // 先设置二级分类数据，让页面能够立即显示
    secondList.value = transList

    // 分批获取所有二级分类对应的三级分类数据
    const batchResults = await processCategoriesInBatches(sortedSecondCategories)

    // 处理批量获取的结果，将三级分类数据填充到对应的二级分类中
    batchResults.forEach(result => {
      // 检查请求是否成功完成
      if (result.status === 'fulfilled' && result.value) {
        const { index, list } = result.value
        // 确保对应的二级分类存在，然后设置其三级分类列表
        if (secondList.value[index]) {
          secondList.value[index].list = list
        }
      }
    })

  } catch (error) {
    // 捕获并处理异常情况
    console.error('初始化分类数据失败:', error)
    showToast('初始化分类数据失败')
    secondList.value = []
  } finally {
    // 无论成功或失败，都要重置加载状态和隐藏骨架屏
    loading.value = false
    await nextTick()
    // 延迟300ms隐藏骨架屏，提供平滑的过渡效果
    setTimeout(() => {
      skeletonStates.value.category = false
    }, 300)
  }
}

// ==================== 用户交互事件处理 ====================
// 三级分类点击事件处理函数
// 当用户点击三级分类时跳转到对应的商品列表页面
const onSubcategoryClick = debounce((item) => {
  // 生成时间戳，用于避免缓存问题
  const timestamp = Date.now()

  // 跳转到商品列表页面，传递分类ID和查询参数
  router.push({
    path: `/goodslist/${item.id}`,    // 商品列表路径，包含分类ID
    query: {
      ...route.query,                 // 保留当前页面的查询参数
      timestamp                       // 添加时间戳参数
    }
  })
}, 300)  // 300ms防抖，避免用户快速点击导致的重复跳转

// ==================== 应用初始化 ====================
// 应用初始化函数
// 负责设置分类父ID和启动数据加载流程
const initializeApp = async () => {
  try {
    // 如果URL查询参数中包含分类父ID，则保存到本地存储
    if (route.query.category_pid) {
      categoryPid.set(route.query.category_pid)
    }

    // 并行初始化轮播和分类数据，提高加载效率
    // 使用allSettled确保即使某个初始化失败也不会影响其他部分
    await Promise.allSettled([
      initSwiper(),        // 初始化轮播横幅
      initCategoryData()   // 初始化分类数据
    ])
  } catch (error) {
    // 捕获并处理应用初始化异常
    console.error('应用初始化失败:', error)
    showToast('页面初始化失败')
  }
}

// ==================== 组件生命周期管理 ====================
// 组件挂载完成后的初始化操作
onMounted(async () => {
  // 启动应用初始化流程
  await initializeApp()
})
</script>

<style lang='less' scoped>
.sfzn-home {
  background-color: #F8F9FA;
  height: 100%;
  overflow: auto;
}

.banner-section {
  padding: 4px 5px 0;

  .banner-wrapper {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.category-section {
  padding: 8px 5px;

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-top: 8px;

    .empty-text {
      font-size: 16px;
      color: #718096;
      font-weight: 400;
    }
  }
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }
}

.category-header {
  padding: 10px;
  //background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  //backdrop-filter: blur(10px);

  .category-title {
    font-size: 18px;
    color: #171E24;
    font-weight: 500;
    line-height: 1.2;
    margin: 0;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }
}

.category-content {
  padding: 10px 5px;
}

.subcategory-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  width: calc(33.333% - 14px);
  min-width: 80px;

  &:hover {
    background-color: #fff7f0;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
    background-color: darken(#fff7f0, 5%);
  }
}

.subcategory-image-wrapper {
  width: 68px;
  height: 68px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 5px;
  background-color: #F8F9FA;
  display: flex;
  align-items: center;
  justify-content: center;

  .subcategory-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.subcategory-name {
  font-size: 14px;
  color: #171E24;
  font-weight: 400;
  line-height: 1.2;
  display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
  max-width: 100%;
  word-break: break-all;
}
</style>
