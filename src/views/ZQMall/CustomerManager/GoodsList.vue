<!--
/**
 * 本省商品列表页面组件
 *
 * 主要功能：
 * 1. 展示客户经理管辖区域内的本省商品信息
 * 2. 提供供应商切换功能，支持多供应商商品浏览
 * 3. 集成商品搜索功能，支持关键词搜索和搜索页面跳转
 * 4. 实现商品列表的分页加载和无限滚动
 * 5. 提供商品详情查看和购物车添加功能
 * 6. 处理空状态显示，区分无供应商和无商品两种情况
 *
 * 技术特点：
 * - 使用Vue 3 Composition API和组合函数实现代码复用
 * - 集成Vant UI组件库提供标签切换和列表展示
 * - 采用分页加载机制优化大数据量展示性能
 * - 使用计算属性实现响应式数据处理
 * - 集成浮动气泡组件提供快捷操作入口
 *
 * 使用场景：
 * - 客户经理查看本省商品信息
 * - 多供应商商品对比和选择
 * - 商品搜索和详情查看
 * - 商品添加到购物车操作
 */
-->

<template>
  <!-- 商品列表页面容器 -->
  <div class="goods-list-page">
    <!-- 页面头部区域，包含供应商标签和搜索功能 -->
    <header class="page-header" v-if="supplierList.length > 0">
      <!-- 供应商切换标签，支持粘性定位和切换事件 -->
      <Tabs v-model:active="curSupplierIndex" sticky @change="curSupplierChange">
        <!-- 遍历供应商列表，为每个供应商创建标签 -->
        <Tab v-for="item in supplierList" :key="item.isvId" :title="item.isvName" />
      </Tabs>
      <!-- 搜索头部组件，支持商品搜索和搜索页面跳转 -->
      <SearchHeader
        v-model="searchKeyword"
        placeholder="搜索商品"
        :redirectToSearch="false"
        @search="handleSearch"
        @clickable="handleSearchClick"
      />
    </header>

    <!-- 主要内容区域 -->
    <main class="goods-content">
      <!-- 无供应商时的空状态显示 -->
      <div v-if="supplierList.length === 0" class="empty">
        <img class="empty-img" src="./assets/empty1.png" alt="暂无供应商">
        <p class="empty-tips">暂无供应商</p>
      </div>

      <!-- 商品列表布局组件，处理商品展示和交互 -->
      <GoodsListLayout
        v-else
        :goods-list="goodsList"
        :is-loading="isLoading"
        :loading="loading"
        :finished="finished"
        empty-description="本地区无货"
        @load-more="onLoad"
        @item-click="goToDetail"
        @add-cart="addOneCart"
        @update:loading="(val) => loading = val"
      />
    </main>

    <!-- 浮动气泡组件，提供购物车等快捷操作 -->
    <FloatingBubble
      :offset="floatingBubbleOffset"
      :is-show-cart="false"
      @go-to-cart="goToCart"
    />

    <!-- 底部导航标签组件，当前激活商品标签 -->
    <TabComponent currentTab="goods" />
  </div>
</template>

<script setup>
// ==================== 依赖导入 ====================
import { ref, onMounted, computed } from 'vue'
import { showToast, Tab, Tabs, closeToast, showLoadingToast } from 'vant'
import SearchHeader from '@components/Common/SearchHeader.vue'
import FloatingBubble from '@components/Common/FloatingBubble.vue'
import GoodsListLayout from '@components/GoodsListCommon/GoodsListLayout.vue'
import TabComponent from '@views/ZQMall/CustomerManager/components/Tab.vue'
import { useGoodsList } from '@/composables/useGoodsList.js'
import { getBizCode } from '@utils/curEnv.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo, queryZqInfo } from '@utils/zqInfo.js'
import { zqQuerySimplified } from '@/api/index.js'
import { useRoute, useRouter } from 'vue-router'
import { get } from 'es-toolkit/compat'

// ==================== 组合函数和路由管理 ====================
// 使用商品列表组合函数，获取商品相关的状态和方法
const {
  goodsList,          // 商品列表数据
  loading,            // 加载更多状态
  finished,           // 是否加载完成
  isLoading,          // 初始加载状态
  pageNo,             // 当前页码
  pageSize,           // 每页数量
  resetList,          // 重置列表方法
  processGoodsData,   // 商品数据处理方法
  applyStockFilter,   // 库存过滤方法
  goToDetail,         // 跳转商品详情方法
  goToCart,           // 跳转购物车方法
  addOneCart          // 添加商品到购物车方法
} = useGoodsList()

// 获取路由实例，用于页面跳转和参数获取
const route = useRoute()
const router = useRouter()

// ==================== 页面状态管理 ====================
// 浮动气泡组件的位置偏移配置
const floatingBubbleOffset = ref({ bottom: 150 })

// 搜索关键词输入框的值
const searchKeyword = ref('')

// 商品分类ID，用于分类筛选
const categoryId = ref('')

// ==================== 供应商管理 ====================
// 供应商列表数据
const supplierList = ref([])

// 当前选中的供应商索引
const curSupplierIndex = ref(0)

// ==================== 计算属性 ====================
// 当前选中的供应商信息
const curSupplier = computed(() => {
  return supplierList.value[curSupplierIndex.value] || {}
})

// 用户角色类型，用于API请求参数
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// ==================== 工具函数 ====================
// 获取zqInfo信息的工具函数
// 根据当前选中的供应商和省份信息构建查询参数
const zqInfo = () => {
  const zqInfoData = queryZqInfo()
  return {
    // 当前供应商代码，如果没有选中供应商则为空字符串
    supplierCode: curSupplier.value.isvId ? curSupplier.value.isvId : '',
    // 省份代码字符串，多个省份用逗号分隔
    proStr: zqInfoData.provinceCode ? zqInfoData.provinceCode.join(',') : ''
  }
}


// ==================== 搜索功能 ====================
// 处理搜索操作的函数
// 验证搜索关键词并跳转到搜索结果页面
const handleSearch = (keyWord) => {
  // 验证搜索关键词是否为空
  if (!keyWord) {
    showToast('请输入搜索内容')
    return
  }

  // 生成时间戳，用于缓存控制
  const timestamp = Date.parse(new Date())

  // 获取测试参数
  const testDMX = get(route.query, 'testDMX', false)

  // 获取当前的zqInfo信息
  const zqInfoData = zqInfo()

  // 构建搜索页面URL并跳转
  router.push('/search/list?timestamp=' + timestamp + '&keyword=' + keyWord + '&testDMX=' + testDMX + '&supplierCode=' + zqInfoData.supplierCode + '&proStr=' + zqInfoData.proStr)

  // 清空搜索输入框
  searchKeyword.value = ''
}

// 搜索框点击处理函数
// 当用户点击搜索框时的回调
const handleSearchClick = () => {
  console.log('搜索框被点击，即将跳转到搜索页面')
}

// ==================== 供应商切换 ====================
// 供应商切换处理函数
// 当用户切换供应商标签时重新加载商品列表
const curSupplierChange = () => {
  // 重置商品列表状态
  resetList()

  // 重新获取商品列表
  fetchGoodsList()
}

// ==================== 数据获取 ====================
// 获取供应商列表的函数
// 从zqInfo中提取供应商列表信息
const getSupplierList = () => {
  const zqInfoData = queryZqInfo()
  supplierList.value = zqInfoData.isvList || []
}

// 获取商品列表的异步函数
// 根据当前供应商和分页参数获取商品数据
const fetchGoodsList = async () => {
  // 如果是第一页，显示初始加载状态
  if (pageNo.value === 1) {
    isLoading.value = true
  }

  // 获取当前的zqInfo信息
  const zqInfoData = zqInfo()

  // 显示加载提示
  showLoadingToast()

  // 调用API获取商品列表
  const [err, json] = await zqQuerySimplified({
    roleType: roleType.value,              // 用户角色类型
    supplierCode: zqInfoData.supplierCode, // 供应商代码
    proStr: zqInfoData.proStr,             // 省份代码字符串
    type: 3,                               // 查询类型：商品
    bizCode: getBizCode('GOODS'),          // 业务代码
    categoryId: categoryId.value,          // 分类ID
    pageNo: pageNo.value,                  // 页码
    pageSize: pageSize.value               // 每页数量
  })

  // 关闭加载提示
  closeToast()

  // 如果是第一页，清空现有商品列表
  if (pageNo.value === 1) {
    goodsList.value = []
  }

  // 更新加载状态
  loading.value = false
  isLoading.value = false

  // 处理API响应结果
  if (!err) {
    // 如果有商品数据
    if (json && json.goodsList && json.goodsList.length > 0) {
      // 处理商品数据格式
      const processedList = processGoodsData(json.goodsList)

      // 应用库存过滤
      const filteredList = applyStockFilter(processedList)

      // 如果过滤后没有商品且还有更多数据，继续加载下一页
      if (filteredList.length <= 0 && json.cacheType === '1') {
        pageNo.value++
        fetchGoodsList()
        return
      }

      // 将新商品添加到列表中
      goodsList.value = goodsList.value.concat(filteredList)

      // 根据缓存类型决定是否继续加载
      if (json.cacheType === '1') {
        pageNo.value++  // 还有更多数据，页码递增
      } else {
        finished.value = true  // 没有更多数据，标记加载完成
      }
    } else {
      // 没有商品数据的处理
      if (!json || (json && json.cacheType === '0')) {
        finished.value = true  // 标记加载完成
        return
      }
      // 继续尝试加载下一页
      pageNo.value++
      fetchGoodsList()
    }
  } else {
    // API请求失败的错误处理
    console.error('获取商品列表失败:', err.msg)
  }
}

// ==================== 事件处理 ====================
// 加载更多商品的处理函数
// 当用户滚动到列表底部时触发
const onLoad = () => {
  fetchGoodsList()
}

// ==================== 生命周期管理 ====================
// 组件挂载时的初始化操作
onMounted(async () => {
  // 初始化供应商列表
  getSupplierList()

  // 获取初始商品列表
  fetchGoodsList()
})
</script>

<style scoped lang="less">
.goods-list-page {
  padding-top: 88px;

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    :deep(.van-tabs) {
      .van-tab {
        color: #171E24;

        &.van-tab--active {
          color: var(--wo-biz-theme-color);
        }
      }

      .van-tabs__line {
        background-color: var(--wo-biz-theme-color);
      }
    }
  }

  .empty {
    font-size: 28px;
    color: #718096;
    text-align: center;
    margin: 0 auto;
    padding: 40px 0;

    .empty-img {
      width: 35%;
    }

    .empty-tips {
      font-size: 16px;
      margin-top: 16px;
    }
  }

  .goods-content {
    padding: 0 10px 50px 10px;
    box-sizing: border-box;
  }
}
</style>
