<!--
/**
 * 订单地址修改页面组件
 *
 * 主要功能：
 * 1. 支持订单发货前的收货地址修改，提供原地址编辑和新地址选择两种方式
 * 2. 展示当前订单的原收货地址信息，包括收货人姓名、电话和详细地址
 * 3. 提供原地址快速编辑功能，通过弹窗形式修改当前地址信息
 * 4. 展示用户的所有收货地址列表，支持选择新的收货地址替换原地址
 * 5. 集成地址新增功能，用户可以快速添加新的收货地址并选择使用
 * 6. 提供地址修改确认机制，确保用户明确修改操作的影响
 * 7. 支持修改结果的实时反馈，显示修改状态和结果提示
 *
 * 技术特点：
 * - 使用Vue 3 Composition API实现响应式状态管理
 * - 集成Pinia状态管理，获取用户地址列表数据
 * - 实现订单地址修改的业务逻辑，包括参数验证和错误处理
 * - 支持地址数据的格式转换和验证
 * - 集成防重复提交机制，避免用户重复操作
 * - 提供友好的加载状态和错误提示
 *
 * 使用场景：
 * - 用户在订单发货前需要修改收货地址时
 * - 订单管理页面跳转到地址修改功能时
 * - 需要快速编辑或选择新收货地址的场景
 */
-->

<template>
  <!-- 订单地址修改页面主容器 -->
  <div class="address-form order-address-edit">
    <!-- 修改提示信息区域，告知用户修改限制和注意事项 -->
    <div class="tip-box">
      <div class="tip-content">
        提示：发货前订单仅支持修改一次，修改后可能会影响物流时效。若商品因换地址、已发货、运费变更等原因导致修改失败，请您谅解。
      </div>
    </div>

    <!-- 原地址信息展示区域 -->
    <div class="address-info-section">
      <!-- 区域标题 -->
      <div class="section-title">原地址</div>
      <!-- 地址详细信息容器 -->
      <div class="address-info">
        <!-- 左侧信息：联系人和编辑操作 -->
        <div class="address-info-left">
          <!-- 收货人姓名和电话 -->
          <div class="contact">{{ originalAddressInfo.recName }} {{ originalAddressInfo.recPhone }}</div>
          <!-- 编辑原地址的操作按钮 -->
          <div class="edit-op" @click="editOriginalAddress">
            <div class="edit-btn">修改原地址</div>
            <img class="edit-icon" :src="(bizCode === 'ygjd') ? jdWoModAddressIcon : woModAddressIcon" alt=""/>
          </div>
        </div>
        <!-- 右侧信息：详细地址 -->
        <div class="address-info-right">
          <!-- 完整的收货地址信息 -->
          <div class="address">{{ originalAddressInfo.recAddress }}</div>
        </div>
      </div>
    </div>

    <!-- 新地址选择区域 -->
    <div class="select-address-section">
      <div class="title-row">
        <!-- 区域标题 -->
        <div class="title">选择新收货地址</div>
        <!-- 新增地址快捷入口 -->
        <div class="add-address" @click="addNewAddress">+ 新增地址</div>
      </div>
    </div>

    <!-- 地址列表容器，支持滚动浏览 -->
    <div class="address-list-container" ref="addressListRef">
      <!-- 有地址数据时显示地址列表 -->
      <template v-if="addressList.length > 0">
        <!-- 循环渲染地址列表项 -->
        <div
          v-for="(item, index) in addressList"
          :key="item.addressId || index"
          class="address-item"
          :class="{ 'cur': selectedIndex === index }"
          @click="selectAddress(index)"
        >
          <!-- 地址内容区域 -->
          <div class="address-content">
            <!-- 收货人姓名和电话 -->
            <div class="contact">{{ item.recName }} {{ item.recPhone }}</div>
            <!-- 完整地址信息，包含省市区和详细地址 -->
            <div class="address">
              <span>{{ item.provinceName }} </span>
              <span>{{ item.cityName }} </span>
              <span>{{ item.countyName }} </span>
              <span>{{ item.townName }} </span>
              <span>{{ item.addrDetail }}</span>
            </div>
          </div>
          <!-- 选择状态指示器 -->
          <div class="radio-btn">
            <!-- 根据选择状态显示不同的图标 -->
            <img :src="selectedIndex === index ? checkImgSrc : uncheckImgSrc" alt="选择状态" />
          </div>
        </div>
      </template>
      <!-- 无地址数据时显示空状态提示 -->
      <div v-else class="empty-address">暂无收货地址，请添加</div>
    </div>

    <!-- 底部操作栏：确认修改按钮 -->
    <AddressActionBar>
      <!-- 确认修改按钮，根据加载状态和选择状态控制可用性 -->
      <WoButton
        type="primary"
        block
        size="xlarge"
        :disabled="isLoading || selectedIndex === '' || selectedIndex < 0 || selectedIndex >= addressList.length"
        @click="confirmOrderAddressChange"
      >
        {{ isLoading ? '提交中...' : '确认修改' }}
      </WoButton>
    </AddressActionBar>
  </div>

  <!-- 原地址编辑弹窗组件 -->
  <!-- 提供快速编辑原地址的功能，支持表单验证和数据保存 -->
  <AddressQuickEditPopup
    v-model:visible="showLocationSelector"
    :initial-data="originalAddressFormData"
    @save="saveOriginalAddress"
    @validate="onFormValidate"
    @region-change="onRegionChange"
    ref="addressEditPopupRef"
  />
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import AddressActionBar from '@components/WoElementCom/WoActionBar.vue'
import AddressQuickEditPopup from '@components/Common/Address/AddressQuickEditPopup.vue'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { modifyOrderAddress } from '@api/interface/order.js'
import { useUserStore } from '@store/modules/user.js'
import checkImg from '@/static/images/wo-select.png'
import uncheckImg from '@/static/images/no-select.png'
import jdCheckImg from '@/static/images/jd-select.png'
import { getBizCode } from '@utils/curEnv.js'
import woModAddressIcon from '@/static/images/wo-mod-address-icon.png'
import jdWoModAddressIcon from '@/static/images/jd-mod-address-icon.png'

// ==================== 路由和状态管理 ====================
// 路由实例，用于页面导航和跳转
const router = useRouter()
// 当前路由信息，用于获取页面参数
const route = useRoute()
// 用户状态管理实例，用于获取地址列表数据
const userStore = useUserStore()
// 渠道码
const bizCode = getBizCode()
// ==================== 表单状态管理 ====================
// 表单验证状态，控制表单的有效性
const isFormValid = ref(false)
// 原地址编辑表单数据，用于弹窗编辑
const originalAddressFormData = ref({})

// ==================== 订单地址编辑状态管理 ====================
// 当前订单ID，用于地址修改API调用
const orderId = ref('')
// 原地址信息，从路由参数中获取并展示
const originalAddressInfo = ref({
  recName: '',      // 收货人姓名
  recPhone: '',     // 收货人电话
  recAddress: ''    // 收货地址
})
// 当前选中的地址索引，用于标识用户选择的新地址
const selectedIndex = ref('')
// 加载状态，控制按钮禁用和加载提示
const isLoading = ref(false)
// 地址编辑弹窗显示状态
const showLocationSelector = ref(false)

// ==================== DOM 元素引用管理 ====================
// 地址编辑弹窗组件引用
const addressEditPopupRef = ref(null)
// 地址列表容器的 DOM 引用，用于滚动定位
const addressListRef = ref(null)

// ==================== 静态资源和常量 ====================
// 根据业务代码选择对应的选中图标
const checkImgSrc = (getBizCode() === 'ygjd') ? jdCheckImg : checkImg
// 未选中状态的图标
const uncheckImgSrc = uncheckImg

// ==================== 计算属性 ====================
// 地址列表计算属性，从用户状态管理中获取地址数据
const addressList = computed(() => userStore.addressList || [])

// ==================== 表单验证和事件处理 ====================
// 表单验证状态回调函数
// 当弹窗中的表单验证状态发生变化时触发
const onFormValidate = ({ isValid }) => {
  isFormValid.value = isValid
}

// 地区选择变化回调函数
// 当用户在弹窗中选择地区时触发，可用于实时处理地区变化逻辑
const onRegionChange = (regionData) => {
  // 记录地区变化信息，便于调试和数据追踪
  console.log('地区变化:', regionData)
}

// ==================== 页面初始化和数据处理 ====================
// 初始化订单地址编辑模式
// 从路由参数中获取订单信息和原地址数据
const initOrderAddressEditMode = () => {
  // 从路由查询参数中获取地址信息字符串
  const addressInfoStr = route.query.addressInfo

  if (addressInfoStr) {
    try {
      // 解码并解析地址信息JSON字符串
      originalAddressInfo.value = JSON.parse(decodeURIComponent(addressInfoStr))
    } catch (e) {
      // 解析失败时记录错误并提示用户
      console.error('解析地址信息失败', e)
      showToast('地址信息解析失败')
    }
  }

  // 从路由查询参数中获取订单ID
  orderId.value = route.query.orderId || ''
  if (!orderId.value) {
    // 缺少订单ID时记录错误并提示用户
    console.error('缺少订单ID参数')
    showToast('缺少订单ID参数')
  }
}

// 获取用户地址列表数据
// 强制刷新地址列表，确保获取最新的地址信息
const fetchAddressList = async () => {
  try {
    // 强制从服务器获取最新的地址列表数据
    await userStore.queryAddrList({ force: true })
    // 等待DOM更新后执行滚动定位
    await nextTick(() => {
      scrollToSelectedAddress()
    })
  } catch (err) {
    // 获取失败时记录错误并提示用户
    console.error('获取地址列表失败', err)
    showToast('获取地址列表失败，请重试')
  }
}

// 滚动到当前选中的地址项
// 用于在页面加载后自动定位到选中的地址
const scrollToSelectedAddress = () => {
  // 获取地址列表容器元素
  const addressListEl = addressListRef.value
  if (!addressListEl) return

  // 查找当前选中的地址项元素
  const selectedEl = addressListEl.querySelector('.cur')
  if (selectedEl) {
    // 使用平滑滚动将选中项滚动到视口中心
    selectedEl.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

// ==================== 用户交互处理 ====================
// 选择地址项的处理函数
// 用户点击地址项时更新选中状态
const selectAddress = (index) => {
  // 如果正在加载中，阻止选择操作
  if (isLoading.value) return
  // 更新选中的地址索引
  selectedIndex.value = index
}

// 编辑原地址的处理函数
// 打开弹窗编辑当前订单的原收货地址
const editOriginalAddress = () => {
  // 将原地址信息转换为表单组件需要的数据格式
  originalAddressFormData.value = {
    ...originalAddressInfo.value,
    recName: originalAddressInfo.value.recName || '',      // 收货人姓名
    recPhone: originalAddressInfo.value.recPhone || '',    // 收货人电话
    addrDetail: originalAddressInfo.value.recAddress || '', // 详细地址
  }
  // 显示地址编辑弹窗
  showLocationSelector.value = true
}

// 新增地址的处理函数
// 跳转到地址新增页面
const addNewAddress = () => {
  // 如果正在加载中，阻止跳转操作
  if (isLoading.value) return
  // 跳转到地址新增页面
  router.push({
    path: '/addr/add'
  })
}

// ==================== 地址修改业务逻辑 ====================
// 保存原地址修改的处理函数
// 处理用户在弹窗中编辑原地址后的保存操作
const saveOriginalAddress = async (addressFormRef) => {
  // 如果正在加载中，阻止重复提交
  if (isLoading.value) return

  // 验证表单数据的有效性
  if (!addressFormRef?.validateForm()) return

  // 设置加载状态并显示加载提示
  isLoading.value = true
  showLoadingToast()

  try {
    // 从表单组件中获取用户输入的数据
    const formData = addressFormRef.formData

    // 构建新的地址信息对象
    const newAddressInfo = {
      recName: formData.recName,        // 收货人姓名
      recPhone: formData.recPhone,      // 收货人电话
      recAddress: formData.addrDetail   // 详细地址
    }

    // 构建订单地址修改的请求参数
    const orderAddressRecord = JSON.stringify({
      orderId: orderId.value,           // 订单ID
      newAddress: newAddressInfo        // 新的地址信息
    })

    // 调用订单地址修改API
    const [err] = await modifyOrderAddress(orderAddressRecord)
    closeToast()

    if (err) {
      // 修改失败时处理错误
      handleOrderAddressError(err)
    } else {
      // 修改成功时更新本地显示的原地址信息
      originalAddressInfo.value = newAddressInfo
      handleOrderAddressSuccess()
    }
  } catch (e) {
    // 捕获网络异常或其他错误
    console.error('修改地址异常', e)
    showToast('网络异常，请稍后重试')
  } finally {
    // 无论成功还是失败，都要重置加载状态和关闭弹窗
    isLoading.value = false
    showLocationSelector.value = false
  }
}

// 确认订单地址修改的处理函数
// 用户选择新地址后点击确认修改按钮的处理逻辑
const confirmOrderAddressChange = async () => {
  // 验证用户是否选择了有效的地址
  if (selectedIndex.value === '' || selectedIndex.value < 0 || selectedIndex.value >= addressList.value.length) {
    showToast('请选择新收货地址')
    return
  }

  // 如果正在加载中，阻止重复提交
  if (isLoading.value) return

  // 设置加载状态并显示加载提示
  isLoading.value = true
  showLoadingToast()

  try {
    // 获取用户选中的地址数据
    const selectedAddress = addressList.value[selectedIndex.value]

    // 构建订单地址修改的请求参数
    const orderAddressRecord = JSON.stringify({
      orderId: orderId.value,           // 订单ID
      newAddress: selectedAddress       // 用户选择的新地址
    })

    // 调用订单地址修改API
    const [err] = await modifyOrderAddress(orderAddressRecord)
    closeToast()

    if (err) {
      // 修改失败时处理错误
      handleOrderAddressError(err)
    } else {
      // 修改成功时处理成功逻辑
      handleOrderAddressSuccess()
    }
  } catch (e) {
    // 捕获网络异常或其他错误
    console.error('修改地址异常', e)
    showToast('网络异常，请稍后重试')
  } finally {
    // 无论成功还是失败，都要重置加载状态
    isLoading.value = false
  }
}

// ==================== 结果处理和反馈 ====================
// 处理订单地址修改失败的情况
// 根据不同的错误类型提供相应的用户提示
const handleOrderAddressError = (err) => {
  if (err.code === '9999') {
    // 一般性错误，直接显示错误信息
    showToast(err.msg || '地址修改失败')
  } else {
    // 其他类型的错误，可能需要用户确认
    // 这里可以根据业务需求使用 vant 的 Dialog 组件进行确认
    showToast(err.msg || '地址修改失败')
  }
}

// 处理订单地址修改成功的情况
// 显示成功提示并自动返回上一页
const handleOrderAddressSuccess = () => {
  // 显示修改成功的提示信息
  showToast('修改信息已提交，修改成功后将更新订单信息。')
  // 延迟1.5秒后自动返回上一页，给用户足够时间查看提示
  setTimeout(() => {
    router.back()
  }, 1500)
}

// ==================== 生命周期管理 ====================
// 组件挂载时的初始化操作
onMounted(async () => {
  try {
    // 初始化订单地址编辑模式，解析路由参数
    initOrderAddressEditMode()
    // 获取用户的地址列表数据
    await fetchAddressList()
  } catch (error) {
    // 初始化失败时记录错误并提示用户
    console.error('页面初始化失败:', error)
    showToast('页面初始化失败')
  }
})
</script>

<style scoped lang="less">
.address-form {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #F8F9FA 0%, #FFFFFF 100%);

  // 订单地址编辑模式样式
  &.order-address-edit {
    background-color: #F8F9FA;
    height: 100vh;
    padding-bottom: 50px;
    overflow: hidden;
  }

  &__header {
    padding: 24px 16px 16px;
    text-align: center;
    background: #FFFFFF;
    border-bottom: 1px solid #F0F2F5;
  }

  &__title {
    font-size: 20px;
    font-weight: 600;
    color: #171E24;
    margin: 0 0 8px 0;
    letter-spacing: 0.3px;
  }

  &__subtitle {
    font-size: 13px;
    color: #718096;
    margin: 0;
    letter-spacing: 0.1px;
  }

  &__content {
    flex: 1;
    padding: 10px;
    background-color: #F8F9FA;
  }
}

.tip-box {
  padding: 9px 20px;
  background-color: #FFF5E6;

  .tip-content {
    color: var(--wo-biz-theme-color);
    font-size: 11px;
    line-height: 18px;
  }
}

.address-info-section {
  padding: 15px 20px;
  background-color: #FFFFFF;

  .section-title {
    font-size: 15px;
    font-weight: 600;
    color: #171E24;
    margin-bottom: 10px;
  }

  .address-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .address-info-left {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .contact {
        font-size: 14px;
        color: #171E24;
        margin-bottom: 5px;
      }

      .edit-op {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        font-size: 13px;
        color: #4A5568;

        .edit-icon {
          margin-left: 5px;
          width: 15px;
          height: 15px;
        }
      }
    }

    .address-info-right {
      .address {
        font-size: 13px;
        color: #4A5568;
        line-height: 18px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
      }
    }
  }
}

.address-list-container {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 20px;
  background-color: #FFFFFF;

  .empty-address {
    padding: 30px 0;
    text-align: center;
    color: #718096;
    font-size: 14px;
  }

  .address-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #E2E8EE;

    &:last-child {
      border-bottom: none;
    }

    &.cur {
      .radio-btn img {
        opacity: 1;
      }
    }

    .address-content {
      flex: 1;

      .contact {
        font-size: 13px;
        color: #171E24;
        margin-bottom: 5px;
      }

      .address {
        font-size: 13px;
        color: #4A5568;
        line-height: 18px;
      }
    }

    .radio-btn {
      margin-left: 10px;

      img {
        width: 18px;
        height: 18px;
        display: block;
      }
    }
  }
}

.select-address-section {
  margin-top: 10px;
  padding: 17px 20px;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;

  .title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 14px;
      font-weight: 600;
      color: #171E24;
    }

    .add-address {
      font-size: 14px;
      color: #FF7A0A;
    }
  }
}
</style>
