<!--
/**
 * 地址列表页面组件
 *
 * 主要功能：
 * 1. 展示用户的所有收货地址列表，支持地址的查看、编辑、删除和设置默认地址
 * 2. 提供骨架屏加载效果，优化用户体验，在数据加载过程中显示占位内容
 * 3. 支持地址的增删改查操作，包括新增地址、编辑地址信息、删除地址和设置默认地址
 * 4. 集成空状态页面，当用户没有地址时显示友好的空状态提示和引导
 * 5. 实现地址列表的自动滚动定位，优先显示默认地址位置
 * 6. 提供防抖机制，避免用户快速点击导致的重复操作
 * 7. 集成确认弹窗，在删除地址时提供二次确认保护
 *
 * 技术特点：
 * - 使用Vue 3 Composition API实现响应式状态管理
 * - 采用shallowRef优化大列表性能，减少不必要的深度响应式监听
 * - 集成Pinia状态管理，实现地址数据的全局共享和同步
 * - 实现DOM元素引用管理，支持精确的滚动定位功能
 * - 使用防抖机制优化用户交互，避免重复操作
 * - 支持骨架屏加载和空状态展示，提升用户体验
 *
 * 使用场景：
 * - 用户查看和管理收货地址列表时
 * - 需要选择或设置默认收货地址时
 * - 地址信息的增删改查操作场景
 */
-->

<template>
  <!-- 地址列表页面主容器，根据是否有地址数据动态添加样式类 -->
  <div class="address-page" :class="{ 'address-page--has-data': hasAddresses }">
    <!-- 页面内容容器，包含列表和空状态 -->
    <div class="address-page__container">
      <!-- 骨架屏加载状态，在数据加载时显示占位内容 -->
      <div class="address-page__list" v-if="loading">
        <!-- 循环渲染骨架屏项目，提供加载时的视觉反馈 -->
        <div v-for="index in skeletonCount" :key="`skeleton-${index}`" class="address-page__item">
          <!-- 单个地址项的骨架屏结构 -->
          <div class="address-skeleton">
            <!-- 骨架屏内容区域，模拟地址信息布局 -->
            <div class="address-skeleton__content">
              <!-- 骨架屏头部区域，模拟用户信息 -->
              <div class="address-skeleton__header">
                <!-- 用户信息骨架屏，包含姓名和电话占位 -->
                <div class="address-skeleton__user-info">
                  <!-- 姓名占位线条 -->
                  <div class="skeleton-line skeleton-name"></div>
                  <!-- 电话占位线条 -->
                  <div class="skeleton-line skeleton-phone"></div>
                </div>
              </div>
              <!-- 地址信息占位线条 -->
              <div class="skeleton-line skeleton-address"></div>
              <!-- 详细地址占位线条 -->
              <div class="skeleton-line skeleton-address-detail"></div>
            </div>
            <!-- 骨架屏操作按钮区域 -->
            <div class="address-skeleton__actions">
              <!-- 操作按钮占位 -->
              <div class="skeleton-line skeleton-btn"></div>
              <div class="skeleton-line skeleton-btn"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 地址列表内容区域，当数据加载完成且有地址时显示 -->
      <div class="address-page__list" ref="addressListRef" v-if="!loading && hasAddresses">
        <!-- 循环渲染地址列表项 -->
        <div v-for="address in addressList" :key="address.addressId"
          :ref="el => setAddressItemRef(el, address.addressId)" :data-address-id="address.addressId"
          class="address-page__item">
          <!-- 地址项组件，处理地址的展示和交互 -->
          <!-- 监听点击事件用于设置默认地址 -->
          <!-- 监听编辑事件用于跳转到编辑页面 -->
          <!-- 监听删除事件用于删除地址 -->
          <AddressItem :address="address" @click="handleSelectAddress" @edit="handleEditAddress"
            @delete="handleDeleteAddress" />
        </div>
      </div>

      <!-- 空状态页面，当没有地址数据时显示 -->
      <!-- 提供友好的空状态提示和新增地址的引导 -->
      <WoEmpty v-if="!loading && !hasAddresses" @add="handleAddAddress" image="@/static/images/empty-address.png"
        description="暂无收货地址" class="address-page__empty" />
    </div>

    <!-- 底部操作栏，提供新增地址的快捷入口 -->
    <AddressActionBar size="xlarge" @add="handleAddAddress" class="address-page__actions" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, shallowRef, readonly, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { debounce } from 'es-toolkit'
import { isEmpty } from 'es-toolkit/compat'
import { useUserStore } from '@store/modules/user.js'
import { updateUserDefaultAddr, deleteAddr } from '@api/index.js'
import AddressItem from '@components/Common/Address/AddressItem.vue'
import WoEmpty from '@components/WoElementCom/WoEmpty.vue'
import AddressActionBar from '@components/WoElementCom/WoActionBar.vue'
import { useAlert } from '@/composables/index.js'

// ==================== 路由和状态管理 ====================
// 路由实例，用于页面导航和跳转
const router = useRouter()
// 用户状态管理实例，用于获取和更新地址数据
const userStore = useUserStore()
// 弹窗确认实例，用于删除地址时的二次确认
const $alert = useAlert()

// ==================== 响应式状态管理 ====================
// 地址列表数据，使用 shallowRef 优化大列表性能
const addressList = shallowRef([])
// 加载状态，控制骨架屏的显示和隐藏
const loading = ref(false)
// 错误状态，用于记录和处理加载过程中的错误
const error = ref(null)

// ==================== DOM 元素引用管理 ====================
// 地址列表容器的 DOM 引用，用于滚动定位
const addressListRef = ref(null)
// 地址项元素的 Map 集合，用于精确定位特定地址项
const addressItemRefs = ref(new Map())

// ==================== 常量和计算属性 ====================
// 骨架屏显示的数量，提供加载时的视觉反馈
const skeletonCount = 3
// 计算属性：判断是否有地址数据，用于控制页面布局和显示状态
const hasAddresses = computed(() => !isEmpty(addressList.value))

// ==================== DOM 引用管理工具函数 ====================
// 设置地址项的 DOM 元素引用，用于后续的滚动定位
// 当组件挂载时添加引用，卸载时清理引用，避免内存泄漏
const setAddressItemRef = (el, addressId) => {
  if (el) {
    // 元素存在时，将其添加到引用 Map 中
    addressItemRefs.value.set(addressId, el)
  } else {
    // 元素不存在时（组件卸载），从引用 Map 中删除
    addressItemRefs.value.delete(addressId)
  }
}

// ==================== 数据处理工具函数 ====================
// 查找默认地址的工具函数
// 在地址列表中查找 isDefault 为 '1' 的地址项
const findDefaultAddress = (list) =>
  list.find(item => item.isDefault === '1')

// ==================== 数据加载和初始化 ====================
// 加载地址列表的主要业务函数
// 负责从服务器获取地址数据并处理加载状态
const loadAddressList = async () => {
  // 如果正在加载中，避免重复请求
  if (loading.value) return

  // 设置加载状态，清空错误信息，显示加载提示
  loading.value = true
  error.value = null
  showLoadingToast()

  try {
    // 强制刷新地址列表数据，确保获取最新信息
    await userStore.queryAddrList({ force: true })
    // 更新本地地址列表数据，如果没有数据则设为空数组
    addressList.value = userStore.addressList || []

    // 查找默认地址，如果存在则自动滚动到该位置
    const defaultAddress = findDefaultAddress(addressList.value)
    if (defaultAddress?.addressId) {
      // 等待 DOM 更新完成
      await nextTick()
      // 延迟执行滚动，确保 DOM 完全渲染
      setTimeout(() => {
        scrollToAddress(defaultAddress.addressId)
      }, 100)
    }
  } catch (err) {
    // 捕获加载过程中的错误，记录日志并显示错误提示
    console.error('获取地址列表失败:', err)
    error.value = err
    showToast(err.msg || '获取地址列表失败')
    // 加载失败时设置为空数组，避免显示错误数据
    addressList.value = []
  } finally {
    // 无论成功还是失败，都要关闭加载状态和提示
    loading.value = false
    closeToast()
  }
}

// 滚动到指定地址项的工具函数
// 用于在页面加载后自动定位到默认地址或指定地址
const scrollToAddress = (addressId) => {
  // 检查参数和容器引用的有效性
  if (!addressId || !addressListRef.value) {
    return
  }

  // 从引用 Map 中获取指定地址项的 DOM 元素
  const selectedElement = addressItemRefs.value.get(addressId)

  if (selectedElement) {
    // 使用平滑滚动将目标元素滚动到视口中心
    selectedElement.scrollIntoView({
      behavior: 'smooth',    // 平滑滚动动画
      block: 'center',       // 垂直方向居中显示
      inline: 'nearest'      // 水平方向就近显示
    })
  }
}

// ==================== 页面导航处理 ====================
// 处理新增地址的导航跳转
// 跳转到地址新增页面，让用户添加新的收货地址
const handleAddAddress = () => {
  router.push('/addr/add')
}

// 处理编辑地址的导航跳转
// 跳转到地址编辑页面，并传递地址数据用于回显
const handleEditAddress = (address) => {
  router.push({
    path: '/addr/edit',                    // 编辑页面路径
    query: { addrId: address.addressId, isEdit: 1 },  // 查询参数，标识编辑模式
    params: { address }                  // 路由参数，传递地址数据避免额外请求
  })
}

// ==================== 地址操作处理 ====================
// 处理删除地址的业务逻辑
// 使用防抖机制避免用户快速点击导致的重复操作
const handleDeleteAddress = debounce(async (address) => {
  try {
    // 显示删除确认弹窗，提供二次确认保护
    $alert({
      title: '删除地址',
      message: '确定要删除该地址吗？',
      showCancelButton: true,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      // 用户确认删除后的回调函数
      onConfirmCallback: async () => {
        // 显示删除过程的加载提示
        showLoadingToast()
        try {
          // 调用删除地址的 API 接口
          const [err] = await deleteAddr(address.addressId)

          if (!err) {
            // 删除成功，更新本地地址列表数据
            const newList = addressList.value.filter(item => item.addressId !== address.addressId)
            addressList.value = newList
            // 同步更新全局状态管理中的地址列表
            userStore.setAddrList(newList)

            // 如果删除的是默认地址，清空默认地址状态
            if (address.isDefault === '1') {
              userStore.setDefaultAddr(null)
            }

            // 显示删除成功提示
            showToast('删除成功')
          } else {
            // 删除失败，记录错误日志并显示错误提示
            console.error('删除地址失败:', err)
            showToast(err.msg || '删除地址失败')
          }
        } catch (error) {
          // 捕获删除过程中的异常
          console.error('删除地址异常:', error)
          showToast('删除地址失败')
        } finally {
          // 无论成功还是失败，都要关闭加载提示
          closeToast()
        }
      }
    })
  } catch (error) {
    // 捕获弹窗显示过程中的异常
    console.error('删除地址弹窗异常:', error)
  }
}, 300)

// 处理选择地址（设置默认地址）的业务逻辑
// 使用防抖机制避免用户快速点击导致的重复操作
const handleSelectAddress = debounce(async (address) => {
  // 如果正在加载中或该地址已经是默认地址，则不执行操作
  if (loading.value || address.isDefault === '1') return

  // 显示设置默认地址的加载提示
  showLoadingToast()

  try {
    // 调用设置默认地址的 API 接口
    const [err] = await updateUserDefaultAddr(address.addressId)

    if (!err) {
      // 设置成功，更新本地地址列表的默认状态
      const newList = addressList.value.map(item => ({
        ...item,
        // 只有当前选中的地址设为默认，其他地址取消默认状态
        isDefault: item.addressId === address.addressId ? '1' : '0'
      }))

      // 更新本地地址列表数据
      addressList.value = newList
      // 同步更新全局状态管理中的地址列表
      userStore.setAddrList(newList)
      // 更新全局状态管理中的默认地址信息
      userStore.setDefaultAddr({ ...address, isDefault: '1' })

      // 显示设置成功提示
      showToast('设置默认地址成功')
    } else {
      // 设置失败，记录错误日志并显示错误提示
      console.error('设置默认地址失败:', err)
      showToast(err.msg || '设置默认地址失败')
    }
  } catch (error) {
    // 捕获设置过程中的异常
    console.error('设置默认地址异常:', error)
    showToast('设置默认地址失败')
  } finally {
    // 无论成功还是失败，都要关闭加载提示
    closeToast()
  }
}, 300)

// ==================== 生命周期管理 ====================
// 组件挂载时的初始化操作
onMounted(() => {
  // 页面加载时自动获取地址列表数据
  loadAddressList()
})

// 组件卸载时的清理操作
onUnmounted(() => {
  // 当前组件没有需要特殊清理的资源
  // DOM 引用会在组件销毁时自动清理
})

// ==================== 组件对外接口 ====================
// 向父组件暴露的方法和数据，用于外部调用
defineExpose({
  // 滚动到指定地址的方法，可供父组件调用
  scrollToAddress,
  // 重新加载地址列表的方法，可供父组件刷新数据
  loadAddressList,
  // 只读的地址列表数据，防止外部直接修改
  addressList: readonly(addressList)
})
</script>

<style scoped lang="less">
.address-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F8F9FA;
  overflow: hidden;
  box-sizing: border-box;
  contain: layout style paint;

  &--has-data {
    padding-bottom: 65px;
  }

  &__container {
    flex: 1;
    padding: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &__list {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    will-change: scroll-position;
    transform: translateZ(0);
  }

  &__item {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__empty {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    will-change: transform;
  }
}

.address-skeleton {
  padding: 15px;
  background-color: #FFFFFF;
  border-radius: 4px;
  margin-bottom: 8px;
  contain: layout style paint;

  &__content {
    margin-bottom: 16px;
  }

  &__header {
    margin-bottom: 11px;
  }

  &__user-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
  will-change: background-position;
}

.skeleton-name {
  width: 60px;
  height: 16px;
}

.skeleton-phone {
  width: 100px;
  height: 16px;
}

.skeleton-address {
  width: 100%;
  height: 13px;
  margin-bottom: 6px;
}

.skeleton-address-detail {
  width: 80%;
  height: 13px;
}

.skeleton-btn {
  width: 40px;
  height: 25px;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}
</style>
