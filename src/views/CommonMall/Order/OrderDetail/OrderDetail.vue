<!--
/**
 * 订单详情页面组件
 *
 * 主要功能：
 * 1. 展示订单完整信息，包括订单状态、商品列表、价格明细、收货地址等
 * 2. 提供订单操作功能，支持取消订单、去支付、申请售后、再次购买等操作
 * 3. 集成物流跟踪功能，实时显示订单配送状态和物流信息
 * 4. 支持地址修改功能，在特定订单状态下允许用户修改收货地址
 * 5. 区分不同业务场景，支持普通商城和政企业务的差异化展示
 * 6. 集成京东商品支持，处理京东订单的特殊逻辑
 * 7. 提供售后申请功能，支持退款、退货、换货等售后服务
 * 8. 实现订单状态实时更新，提供准确的订单进度信息
 *
 * 技术特点：
 * - 使用骨架屏优化加载体验
 * - 采用计算属性实现响应式数据处理
 * - 集成多个状态管理store，统一管理订单和售后数据
 * - 使用组合式API提升代码复用性
 * - 支持多种订单状态的动态操作按钮
 *
 * 使用场景：
 * - 用户查看订单详细信息
 * - 订单状态跟踪和物流查询
 * - 订单相关操作执行
 * - 售后服务申请和处理
 */
-->

<template>
  <!-- 订单详情页面主容器 -->
  <div class="order-detail">
    <!-- 订单详情加载骨架屏，在数据加载时显示 -->
    <OrderDetailSkeleton v-if="loading" />

    <!-- 订单状态头部区域 -->
    <!-- 显示订单当前状态和相关时间信息 -->
    <header v-else class="order-detail__header">
      <div class="order-detail__status">
        <!-- 订单状态主标题 -->
        <h2 class="order-detail__status-title">{{ statusTitle }}</h2>
        <!-- 订单状态副标题，包含详细状态信息和时间 -->
        <div class="order-detail__status-subtitle">
          <!-- 状态描述文字 -->
          <span class="order-detail__status-text" v-if="subtitleText">{{ subtitleText }}</span>
          <!-- 状态相关时间信息 -->
          <span class="order-detail__status-time" v-if="subtitleTime">{{ subtitleTime }}</span>
          <!-- 状态后缀文字 -->
          <span class="order-detail__status-text" v-if="subtitleSuffix">{{ subtitleSuffix }}</span>
        </div>
      </div>
    </header>

    <!-- 订单详情主内容区域 -->
    <main v-if="!loading" class="order-detail__main">
      <!-- 物流状态卡片 -->
      <!-- 在特定订单状态下显示物流跟踪信息 -->
      <LogisticsStatusCard
        v-if="shouldShowLogisticsModule"
        :status-text="logisticsDisplayInfo.statusText"
        :last-track="lastOrderTrack"
        :show-arrow="logisticsDisplayInfo.showArrow"
        :is-completed="currentOrderStatus === '9'"
        :receiver-name="receiverInfo.name"
        :receiver-phone="receiverInfo.phone"
        :receiver-address="fullAddress"
        @view-logistics="handleViewLogistics" />

      <!-- 收货地址信息卡片 -->
      <!-- 显示收货地址详情，支持地址修改功能 -->
      <AddressInfoCard
        v-if="orderInfo.addressInfo && shouldShowAddressModule"
        :receiver-name="receiverInfo.name"
        :receiver-phone="receiverInfo.phone"
        :full-address="fullAddress"
        :address-update-state="addressUpdateState"
        :order-state="currentOrderStatus"
        :is-j-d="isJD"
        :order-info="orderInfo"
        :receive-data="receiveData"
        @edit-address="handleEditAddress" />

      <!-- 订单商品列表卡片 -->
      <!-- 展示订单中的所有商品信息和操作按钮 -->
      <WoCard v-if="transformedGoodsList.length > 0" class="order-detail__goods">
        <!-- 遍历显示每个商品项 -->
        <OrderGoodsCard
          v-for="(goodsItem, index) in transformedGoodsList"
          :key="goodsItem.id || index"
          :item="goodsItem"
          :item-id="goodsItem.id || index"
          :image-size="90"
          :min-height="110"
          :show-actions="true"
          @click="handleGoodsClick(goodsItem)">
          <!-- 商品操作按钮插槽 -->
          <!-- 根据商品状态动态显示不同的操作按钮 -->
          <template #actions="{ item }">
            <WoButton
              v-for="action in getItemActions(item)"
              :key="action.key"
              size="small"
              :type="action.type || 'primary'"
              @click.stop="action.handler(item)">
              {{ action.label }}
            </WoButton>
          </template>
        </OrderGoodsCard>
      </WoCard>

      <!-- 订单价格明细卡片 -->
      <!-- 仅在非政企业务时显示价格详情 -->
      <WoCard v-if="orderInfo && bizCode !== 'zq'" class="order-detail__price">
        <!-- 商品总价行 -->
        <InfoRow label="商品总价">
          <template #value>
            <PriceDisplay :price="orderInfo.orderPrice" size="small" />
          </template>
        </InfoRow>
        <!-- 运费信息行，京东商品显示特殊提示 -->
        <InfoRow v-if="isJD && totalFreight" label="运费" value="运费已分摊至商品金额" />
        <InfoRow v-else label="运费">
          <template #value>
            <PriceDisplay :price="formatMoney(0)" size="small" :bold="false" />
          </template>
        </InfoRow>
        <!-- 应付款/实付款行，根据订单状态显示不同标签 -->
        <InfoRow :label="priceLabel">
          <template #value>
            <PriceDisplay :price="orderInfo.orderPrice" size="medium" :color="priceColor" />
          </template>
        </InfoRow>
      </WoCard>

      <!-- 订单基本信息卡片 -->
      <!-- 显示订单号、收货信息、下单时间等基础信息 -->
      <WoCard v-if="orderInfo" class="order-detail__info">
        <!-- 订单号行，支持复制功能 -->
        <InfoRow :label="bizCode === 'zq' ? '采购单号' : '交付订单号'">
          <template #value>
            <div class="order-detail__order-number">
              <span class="order-detail__order-number-text">{{ orderInfo.bizOrderId }}</span>
              <span class="order-detail__order-number-copy" @click="copyText(orderInfo.bizOrderId)">复制</span>
            </div>
          </template>
        </InfoRow>
        <!-- 收货信息行，仅在已签收状态且非政企业务时显示 -->
        <InfoRow label="收货信息" v-if="orderInfo.addressInfo && currentOrderStatus === '9' && bizCode !== 'zq'">
          <template #value>
            <div class="order-detail__receiver-info">
              <div class="order-detail__receiver-contact">{{ receiverInfo.name }} {{ receiverInfo.phone }}</div>
              <div class="order-detail__receiver-address">{{ fullAddress }}</div>
            </div>
          </template>
        </InfoRow>
        <!-- 下单时间行 -->
        <InfoRow label="下单时间" :value="formatDate(orderInfo.orderDate)" v-if="orderInfo.orderDate" />
        <!-- 交易时间行，仅在非政企业务时显示 -->
        <InfoRow label="交易时间" :value="formatDate(orderInfo.orderDate)" v-if="orderInfo.orderDate && bizCode !== 'zq'" />
        <!-- 付款方式行，仅在非政企业务时显示 -->
        <InfoRow label="付款方式" value="在线支付" v-if="bizCode !== 'zq'" />
        <!-- 企业信息行，仅在政企业务时显示 -->
        <InfoRow label="企业信息" :value="enterpriseName" v-if="bizCode === 'zq'" />
      </WoCard>

      <!-- 保证金信息卡片 -->
      <!-- 当订单包含保证金时显示相关信息 -->
      <DepositInfoCard
        v-if="hasDeposit"
        :deposit-amount="depositAmount"
        :deposit-period="depositPeriod"
        @view-deposit="handleViewDeposit" />

      <!-- 底部操作栏占位符 -->
      <WoActionBarPlaceholder />

      <!-- 底部固定操作栏 -->
      <WoActionBar>
        <!-- 操作按钮容器，根据订单状态调整样式 -->
        <div class="order-detail__action-bar"
          :class="{ 'order-detail__action-bar--pending-payment': currentOrderStatus === '0' }">
          <!-- 动态生成的操作按钮 -->
          <WoButton
            v-for="action in getBottomActions(currentOrderStatus)"
            :key="action.key"
            :type="action.type"
            :size="currentOrderStatus === '0' ? 'large' : 'medium'"
            @click="action.handler">
            <!-- 去支付按钮特殊模板，显示价格信息 -->
            <template v-if="action.key === 'goToPay' && currentOrderStatus === '0'">
              <span style="margin-right: 5px;">去支付</span>
              <PriceDisplay :price="orderInfo.orderPrice" size="small" color="white" />
            </template>
            <!-- 其他按钮的默认模板 -->
            <template v-else>
              {{ action.label }}
            </template>
          </WoButton>
        </div>
      </WoActionBar>
    </main>

    <!-- 售后申请过期提示弹窗 -->
    <!-- 当商品超过售后期限时显示 -->
    <ExpirationPopup
      v-model:visible="expirationPopupVisible"
      title=""
      main-text="抱歉，订单已过售后申请时效"
      sub-text="商品已超过售后期限，如需售后可联系客服处理"
      confirm-text="确定"
      @close="expirationPopupVisible = false"
      @confirm="expirationPopupVisible = false" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, shallowRef, markRaw, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { get, reduce, map, filter, some, isEmpty } from 'es-toolkit/compat'
import dayjs from 'dayjs'
import useClipboard from 'vue-clipboard3'
import { formSubmit } from 'commonkit'

import PriceDisplay from '@components/Common/PriceDisplay.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderGoodsCard from '@components/GoodsListCommon/OrderGoodsCard.vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import InfoRow from '@components/Common/InfoRow.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import OrderDetailSkeleton from './components/OrderDetailSkeleton.vue'
import LogisticsStatusCard from './components/LogisticsStatusCard.vue'
import AddressInfoCard from './components/AddressInfoCard.vue'
import DepositInfoCard from './components/DepositInfoCard.vue'
import ExpirationPopup from '@components/Common/ExpirationPopup/ExpirationPopup.vue'

import orderStatus from '@utils/orderState.js'
import { getOrderInfo, getOrderExpress, getExpress, modOrderListShow, cancelOrder, repayOrder, jdAddressCheck, jdModifyOrderAddress, verifySupplierOrderRepurchased, isCheckGoodsExistsBol } from '@api/interface/order.js'
import { addOneClick } from '@api/interface/goods.js'
import { getBizCode } from '@utils/curEnv.js'
import { buyProductCart, buyProductCartSession } from '@utils/storage.js'
import { analyzeLogisticsStatus, isFinalStatus } from '@utils/logisticsStatusAnalyzer.js'
import { useAlert } from "@/composables/index.js"
import { useUserStore } from "@store/modules/user.js"
import { useAfterSalesStore } from "@store/modules/afterSales.js"
import { useOrderAfterSalesActions } from '@/composables/useOrderAfterSalesActions.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo } from "@utils/zqInfo.js"
import {JD_GOODS_CODE} from "@utils/types.js";

// ==================== 核心工具和依赖实例 ====================
// 剪贴板操作工具，用于复制订单号等文本内容
const { toClipboard } = useClipboard()
// 路由导航实例，用于页面跳转
const router = useRouter()
// 当前路由信息实例，用于获取路由参数
const route = useRoute()
// 弹窗提示工具实例，用于显示确认对话框
const $alert = useAlert()
// 用户状态管理实例，管理用户信息和地址数据
const userStore = useUserStore()
// 售后状态管理实例，管理售后相关数据
const afterSalesStore = useAfterSalesStore()

// ==================== 售后操作相关 ====================
// 售后操作组合式函数，提供售后相关功能
const {
  expirationPopupVisible,    // 售后过期弹窗显示状态
  generateActionButtons      // 生成售后操作按钮函数
} = useOrderAfterSalesActions()

// ==================== 路由参数管理 ====================
// 响应式路由参数对象，用于获取订单ID等参数
const routeParams = reactive(route.params)
// 响应式路由查询参数对象，用于获取查询参数
const routeQuery = reactive(route.query)

// ==================== 业务相关计算属性 ====================
// 获取售后信息，从售后状态管理中获取当前售后数据
const afterSalesInfo = computed(() => afterSalesStore.getAfterSalesInfo)

// 获取当前业务代码，用于区分不同业务场景
const bizCode = computed(() => getBizCode())

// 获取企业名称，用于政企业务页面显示企业信息
const enterpriseName = computed(() => {
  const enterpriseInfo = getEnterpriseManagerInfo()
  return enterpriseInfo ? enterpriseInfo.ciName : '-'
})

// ==================== 模块显示控制常量 ====================
// 物流模块显示状态：已退款(10)、已签收(9)、配送中(5)
const LOGISTICS_ALLOWED_STATUSES = markRaw(['10', '9', '5'])
// 地址模块显示状态：待付款(0)、待发货(1)、待发货(3)、已取消(2)
const ADDRESS_ALLOWED_STATUSES = markRaw(['0', '1', '3', '2'])

// ==================== 模块显示控制计算属性 ====================
// 判断是否显示物流跟踪模块
// 根据订单状态决定是否显示物流信息
const shouldShowLogisticsModule = computed(() => {
  return LOGISTICS_ALLOWED_STATUSES.indexOf(currentOrderStatus.value) !== -1
})

// 判断是否显示地址信息模块
// 根据订单状态决定是否显示地址信息
const shouldShowAddressModule = computed(() => {
  return ADDRESS_ALLOWED_STATUSES.indexOf(currentOrderStatus.value) !== -1
})

// ==================== 价格相关计算属性 ====================
// 计算总运费，根据订单状态使用不同的计算方式
const totalFreight = computed(() => {
  const state = currentOrderStatus.value
  // 待付款状态(0)使用订单信息中的总运费
  if (state === '0') {
    return get(orderInfo.value, 'totalFreight', 0)
  }
  // 其他状态累计各供应商的运费
  return reduce(orderDetailSupplierOrderVoList.value, (sum, item) => sum + get(item, 'freight', 0), 0)
})

// 价格标签文字，根据订单状态显示不同标签
const priceLabel = computed(() => {
  const status = currentOrderStatus.value
  // 待付款(0)或已取消(2)显示应付款，其他显示实付款
  return (status === '0' || status === '2') ? '应付款' : '实付款'
})

// 价格显示颜色，待付款状态显示橙色突出显示
const priceColor = computed(() => {
  // 待付款状态(0)显示橙色，突出显示待支付金额
  return currentOrderStatus.value === '0' ? 'orange' : undefined
})

// ==================== 地址相关计算属性 ====================
// 拼接完整收货地址，将省市区县详细地址组合为完整地址字符串
const fullAddress = computed(() => {
  const addressInfo = get(orderInfo.value, 'addressInfo')
  if (!addressInfo) return ''

  const { provinceName, cityName, countyName, townName, addrDetail } = addressInfo
  const addressParts = filter([provinceName, cityName, countyName, townName, addrDetail], Boolean)
  return addressParts.join('')
})

// 获取收货人基本信息，提取收货人姓名和电话
const receiverInfo = computed(() => {
  const addressInfo = get(orderInfo.value, 'addressInfo')
  return addressInfo ? {
    name: get(addressInfo, 'recName', ''),     // 收货人姓名
    phone: get(addressInfo, 'recPhone', '')    // 收货人电话
  } : { name: '', phone: '' }
})

// 地址更新状态，用于判断地址修改权限
const addressUpdateState = computed(() => {
  return get(orderInfo.value, 'addressUpdateState', '00')
})

// 收货人完整信息，用于地址编辑功能
const receiveData = computed(() => {
  const addressInfo = get(orderInfo.value, 'addressInfo')
  if (!addressInfo) return {}

  const {
    recName,
    recPhone,
    provinceName,
    cityName,
    countyName,
    townName,
    addrDetail
  } = addressInfo

  return {
    recName,                                                                                    // 收货人姓名
    recPhone,                                                                                   // 收货人电话
    recAddress: (provinceName || '') + (cityName || '') + (countyName || '') + (townName || '') + addrDetail,  // 完整地址
    ...addressInfo                                                                              // 其他地址信息
  }
})

// ==================== 工具函数 ====================
// 金额格式化函数，将分转换为元并保留两位小数
const formatMoney = (amount) => (amount / 100).toFixed(2)

// ==================== 订单状态管理 ====================
// 当前订单状态
const currentOrderStatus = ref('')

// 支付倒计时状态
const countdown = ref({
  hours: 0,      // 小时
  minutes: 30,   // 分钟
  seconds: 0     // 秒
})

// 收货倒计时状态
const receiptCountdown = ref({
  days: 12,      // 天数
  hours: 12,     // 小时
  minutes: 14    // 分钟
})

// 倒计时定时器引用
let countdownTimer = null

// ==================== 订单基础信息管理 ====================
// 订单ID，从路由参数或查询参数获取
const orderId = ref(routeParams.orderId || routeQuery.orderId)
// 是否为支付页面跳转标识
const isPay = ref(routeQuery.isPay || 0)
// 订单详细信息对象
const orderInfo = shallowRef({})
// 订单供应商列表信息
const orderDetailSupplierOrderVoList = shallowRef([])
// 是否包含京东商品标识
const isJD = ref(false)
// 是否包含中石通商品标识
const isZST = ref(false)

// ==================== 物流相关状态管理 ====================
// 订单物流信息
const orderExpress = shallowRef({})
// 订单包裹列表
const orderPackageList = shallowRef([])
// 物流跟踪记录
const orderTrack = shallowRef([])
// 最新物流跟踪记录
const lastOrderTrack = shallowRef(null)
// 物流状态分析结果
const logisticsAnalysis = shallowRef(null)

// ==================== 页面状态管理 ====================
// 数据是否已加载标识
const dataLoaded = ref(false)
// 页面加载状态
const loading = ref(true)

// ==================== 保证金相关状态管理 ====================
// 是否包含保证金
const hasDeposit = ref(false)
// 保证金订单ID
const depositOrderId = ref('')
// 保证金金额
const depositAmount = ref(0)
// 保证金期限
const depositPeriod = ref(0)

// ==================== 支付相关状态管理 ====================
// 对话框显示状态
const dialogShow = ref(false)
// 微信支付相关信息
const wapay = ref({
  bizOrderId: '',      // 业务订单ID
  encryptContent: '',  // 加密内容
  wapURL: ''          // 支付URL
})

// ==================== 商品列表数据处理 ====================
// 转换订单商品列表为统一格式
const transformedGoodsList = computed(() => {
  return reduce(orderDetailSupplierOrderVoList.value, (result, supplierOrder) => {
    const subOrderList = get(supplierOrder, 'orderDetailSupplierSubOrderVoList', [])

    const transformedItems = map(subOrderList, subOrder => {
      const sku = get(subOrder, 'skuNumInfoList[0]')
      const skuData = get(sku, 'sku', {})

      return {
        id: `${get(skuData, 'goodsId', '')}_${get(skuData, 'skuId', '')}`,    // 商品唯一标识
        rawData: supplierOrder,
        subOrderRawData: subOrder,
        price: get(skuData, 'price', 0),
        quantity: get(sku, 'skuNum', 0),
        detailImageUrl: get(skuData, 'detailImageUrl[0]', ''),
        orderState: get(subOrder, 'orderState', ''),
        skuNumInfoList: [{
          sku: {
            ...skuData,
            goodsId: get(skuData, 'goodsId', ''),
            skuId: get(skuData, 'skuId', ''),
            name: get(skuData, 'name', ''),
            detailImageUrl: get(skuData, 'detailImageUrl[0]', ''),
            param: get(skuData, 'param', ''),
            param1: get(skuData, 'param1', ''),
            param2: get(skuData, 'param2', ''),
            param3: get(skuData, 'param3', ''),
            param4: get(skuData, 'param4', ''),
            price: get(skuData, 'price', 0),
          },
          skuNum: get(sku, 'skuNum', 0)
        }]
      }
    })

    return [...result, ...transformedItems]
  }, [])
})

// ==================== 订单状态显示计算属性 ====================
// 订单状态主标题，根据订单状态码获取对应的状态文字
const statusTitle = computed(() => orderStatus(currentOrderStatus.value))

// 订单状态副标题前缀文字，根据不同状态显示相应的提示文字
const subtitleText = computed(() => {
  const status = currentOrderStatus.value
  if (status === '0') return '剩 '      // 待付款状态显示剩余时间前缀
  if (status === '5') return '还剩 '    // 配送中状态显示剩余时间前缀
  return ''
})

// 订单状态副标题时间信息，显示倒计时或配送时间
const subtitleTime = computed(() => {
  const status = currentOrderStatus.value
  if (status === '0') {  // 待付款状态显示支付倒计时
    const { hours, minutes, seconds } = countdown.value
    return `${formatTime(hours)}:${formatTime(minutes)}:${formatTime(seconds)}`
  }
  if (status === '5') {  // 配送中状态显示预计送达时间
    const { days, hours, minutes } = receiptCountdown.value
    return `${days}天${hours}小时${minutes}分`
  }
  return ''
})

// 订单状态副标题后缀文字，根据不同状态显示相应的说明文字
const subtitleSuffix = computed(() => {
  const status = currentOrderStatus.value
  if (status === '0') return ' 支付关闭'        // 待付款状态的关闭提示
  if (status === '2') return '超时未支付自动关闭'  // 已取消状态的取消原因
  if (status === '5') return ' 支付确认'        // 配送中状态的确认提示
  return ''
})

// 物流显示信息计算属性，处理物流状态的展示逻辑
const logisticsDisplayInfo = computed(() => {
  // 如果没有物流分析数据，返回默认的无物流信息状态
  if (!logisticsAnalysis.value) {
    return {
      statusText: '暂无物流信息',
      statusColor: '#999999',
      showArrow: false,
      confidence: 0
    }
  }

  // 根据物流分析结果构建显示信息
  const analysis = logisticsAnalysis.value
  return {
    statusText: analysis.statusText,                    // 物流状态文字描述
    showArrow: !!lastOrderTrack.value,                  // 是否显示箭头，有物流记录时显示
    confidence: analysis.confidence,                    // 物流状态置信度
    isFinal: isFinalStatus(analysis.status)            // 是否为最终状态
  }
})

// ==================== 工具函数 ====================
// 时间格式化函数，将数字时间格式化为两位数字符串
const formatTime = (time) => time.toString().padStart(2, '0')

// 日期格式化函数，将日期对象格式化为标准日期时间字符串
const formatDate = (date) => date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : ''

// 文本复制函数，将指定文本复制到剪贴板并显示提示
const copyText = async (text) => {
  try {
    await toClipboard(text)
    showToast('复制成功')
  } catch (e) {
    console.error('复制失败:', e)
    showToast('复制失败')
  }
}


// ==================== 倒计时管理函数 ====================
// 启动支付倒计时，仅在待付款状态下启动
const startCountdown = (orderData = null) => {
  // 清除之前的倒计时
  clearCountdown()

  // 仅在待付款状态(0)时启动倒计时
  if (currentOrderStatus.value === '0') {
    let remainingTime = 0

    // 根据订单数据计算剩余支付时间
    if (orderData) {
      const orderTime = new Date(orderData.orderDate || orderData.createTime).getTime()
      const currentTime = new Date().getTime()
      const paymentTimeout = orderData.paymentTimeout || 30 * 60 * 1000  // 默认30分钟

      remainingTime = Math.max(0, paymentTimeout - (currentTime - orderTime))
    } else {
      remainingTime = 30 * 60 * 1000  // 默认30分钟
    }

    // 将剩余时间转换为时分秒格式
    const totalSeconds = Math.floor(remainingTime / 1000)
    countdown.value.hours = Math.floor(totalSeconds / 3600)
    countdown.value.minutes = Math.floor((totalSeconds % 3600) / 60)
    countdown.value.seconds = totalSeconds % 60

    // 如果时间已到，直接设置为已取消状态
    if (remainingTime <= 0) {
      currentOrderStatus.value = '2'  // 已取消
      return
    }

    // 启动倒计时定时器，每秒更新一次
    countdownTimer = setInterval(() => {
      if (countdown.value.seconds > 0) {
        countdown.value.seconds--
      } else if (countdown.value.minutes > 0) {
        countdown.value.minutes--
        countdown.value.seconds = 59
      } else if (countdown.value.hours > 0) {
        countdown.value.hours--
        countdown.value.minutes = 59
        countdown.value.seconds = 59
      } else {
        // 倒计时结束，设置订单状态为已取消
        currentOrderStatus.value = '2'  // 已取消
        clearInterval(countdownTimer)
      }
    }, 1000)
  }
}

// 清除倒计时定时器
const clearCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// ==================== 数据获取函数 ====================
// 获取订单详情数据，包括订单信息、物流信息等
const getOrderInfoData = async () => {
  loading.value = true
  showLoadingToast()

  try {
    // 调用API获取订单详情
    const [err, json] = await getOrderInfo(orderId.value, isPay.value)
    closeToast()

    if (!err && json) {
      // 设置订单基础信息
      orderInfo.value = json

      // 处理保证金相关信息
      hasDeposit.value = !!(orderInfo.value.bondOrderId && orderInfo.value.bondPrice && orderInfo.value.bondTerm)

      if (orderInfo.value.bondOrderId) {
        depositOrderId.value = orderInfo.value.bondPrice
      }

      if (orderInfo.value.bondPrice) {
        depositAmount.value = (+orderInfo.value.bondPrice) / 100  // 分转元
      }

      if (orderInfo.value.bondTerm) {
        depositPeriod.value = (+orderInfo.value.bondTerm) / 12  // 月转年
      }

      // 设置供应商订单列表
      orderDetailSupplierOrderVoList.value = json.orderDetailSupplierOrderVoList || []

      // 确定当前订单状态，待付款状态使用主订单状态，其他使用子订单状态
      if (orderInfo.value.orderState === '0') {
        currentOrderStatus.value = orderInfo.value.orderState
      } else {
        currentOrderStatus.value = orderDetailSupplierOrderVoList.value[0]?.orderState || orderInfo.value.orderState
      }

      // 检查是否包含京东商品
      isJD.value = some(orderDetailSupplierOrderVoList.value, item =>
        get(item, 'supplierCode', '').indexOf(JD_GOODS_CODE) > -1
      )

      // 检查是否包含中石通商品
      isZST.value = some(orderDetailSupplierOrderVoList.value, item =>
        get(item, 'supplierCode', '').indexOf('zst') > -1
      )

      // 获取物流信息
      orderExpress.value = await getOrderExpressData()
      orderPackageList.value = orderExpress.value.orderPackageList

      // 处理物流跟踪数据
      if (!isEmpty(orderPackageList.value)) {
        for (const packageItem of orderPackageList.value) {
          const deliverInfo = await getExpressData(
            get(packageItem, 'deliverInfo.supplierSubOrderId'),
            get(packageItem, 'deliverInfo.expressNo')
          )

          const trackData = get(deliverInfo, 'orderTrack', [])
          if (!isEmpty(trackData)) {
            orderTrack.value = trackData
            lastOrderTrack.value = trackData[0]  // 最新的物流记录
            logisticsAnalysis.value = analyzeLogisticsStatus(trackData)
            break  // 找到第一个有物流数据的包裹即可
          }
        }
      }

      // 标记数据加载完成
      dataLoaded.value = true

      // 启动支付倒计时（如果是待付款状态）
      startCountdown(orderInfo.value)
    } else {
      showToast('获取订单信息失败')
    }
  } catch (error) {
    closeToast()
    showToast('获取订单信息失败')
    console.error('获取订单信息错误:', error)
  } finally {
    loading.value = false
  }
}

// 用户角色类型计算属性，用于权限控制和功能限制
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// 获取订单物流数据
const getOrderExpressData = async () => {
  showLoadingToast()
  const [err, json] = await getOrderExpress(orderId.value, roleType.value)
  closeToast()
  if (!err) return json
  return {}
}

// 获取具体的物流跟踪数据
const getExpressData = async (orderIdParam, expressNo) => {
  showLoadingToast()
  const [err, json] = await getExpress(orderIdParam, expressNo, roleType.value)
  closeToast()
  if (!err) return json
  return {}
}

// ==================== 商品操作函数 ====================
// 处理加入购物车操作，将订单中的商品重新加入购物车
const handleAddToCart = async (item) => {
  try {
    const subOrder = item
    const { sku } = subOrder.skuNumInfoList[0]
    const { goodsId, skuId, supplierCode } = sku

    // 构建商品参数
    const params = {
      goodsId,
      skuId,
      supplierCode
    }

    // 检查商品ID和SKU ID是否有效
    if (!goodsId || !skuId) {
      showToast('部分商品无货或已下架无法添加购物车')
      return
    }

    showLoadingToast()

    // 检查商品是否存在且可购买
    const [err1, json] = await isCheckGoodsExistsBol(params)
    if (!err1) {
      if (json) {
        params.goodsId = json  // 更新商品ID
      } else {
        closeToast()
        showToast('部分商品无货或已下架无法添加购物车!')
        return
      }
    } else {
      closeToast()
      showToast('部分商品无货或已下架无法添加购物车！')
      return
    }

    // 构建地址信息
    const info = userStore.curAddressInfo
    const addressInfo = JSON.stringify({
      provinceId: info.provinceId,
      provinceName: info.provinceName,
      cityId: info.cityId,
      cityName: info.cityName,
      countyId: info.countyId,
      countyName: info.countyName,
      townId: info.townId,
      townName: info.townName
    })

    // 调用加入购物车API
    const [err2] = await addOneClick({
      ...params,
      bizCode: getBizCode('ORDER'),
      addressInfo: addressInfo
    })

    closeToast()

    if (err2) {
      showToast(err2.msg || '加入购物车失败')
      return
    }

    showToast('加入购物车成功！')
  } catch (error) {
    closeToast()
    showToast('加入购物车失败，请重试')
    console.error('加购物车错误:', error)
  }
}



// ==================== 订单操作函数 ====================
// 处理取消订单操作，显示确认对话框后执行取消
const handleCancelOrder = async () => {
  // 取消订单的具体执行函数
  const cancelOrderFn = async () => {
    showLoadingToast()
    const [err] = await cancelOrder(orderId.value)
    closeToast()
    if (!err) {
      // 清除支付倒计时
      clearCountdown()

      // 重新获取订单信息以更新状态
      await getOrderInfoData()

      showToast('订单取消成功')
    } else {
      showToast(err.msg || '取消订单失败')
    }
  }

  // 显示确认对话框
  $alert({
    title: '',
    message: '取消后将无法恢复，您确定要取消订单吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    onConfirmCallback: async () => {
      await cancelOrderFn()
    }
  })
}

// 处理去支付操作，重新发起订单支付
const handleGoToPay = async () => {
  showLoadingToast()
  try {
    // 调用重新支付API
    const [res, json] = await repayOrder(orderId.value)
    closeToast()

    if (res.code === '0000') {
      // 根据业务类型处理支付逻辑
      if (getBizCode() === 'fupin' && json.isNeedCompanyInsert === 'true') {
        // 扶贫业务需要显示对话框
        dialogShow.value = true
        wapay.value.encryptContent = json.encryptContent
        wapay.value.wapURL = json.wapURL
        wapay.value.bizOrderId = json.storeOrderId
      } else {
        // 普通业务直接跳转支付
        formSubmit(json.wapURL, { param: json.encryptContent })
      }
    } else if (res.code === '2091070302' && !isEmpty(res.data)) {
      // 处理商品状态异常的情况
      if (some(json, ['state', '2'])) {
        showToast('您的订单中有商品已下架')
      } else if (some(json, ['state', '3'])) {
        showToast('您的订单中有无货商品')
      } else if (some(json, ['state', '4'])) {
        showToast('您的订单中有商品库存不足')
      }
    } else {
      showToast(res.msg)
    }
  } catch (error) {
    closeToast()
    console.error('支付失败:', error)
    showToast('支付失败，请重试')
  }
}

// 处理删除订单操作，将订单移至回收站
const handleDeleteOrder = async () => {
  // 删除订单的具体执行函数
  const deleteOrderFn = async () => {
    showLoadingToast()

    try {
      // 如果有供应商订单列表，批量删除所有子订单
      if (!isEmpty(orderDetailSupplierOrderVoList.value)) {
        const deletePromises = map(orderDetailSupplierOrderVoList.value, supplierOrder =>
          modOrderListShow({
            supplierOrderId: supplierOrder.id,
            isDelete: 1  // 标记为删除状态
          })
        )

        await Promise.all(deletePromises)
      } else {
        // 如果没有子订单，直接删除主订单
        const [err] = await modOrderListShow({
          supplierOrderId: orderId.value,
          isDelete: 1
        })
        if (err) {
          throw new Error(err.msg)
        }
      }

      closeToast()

      // 清除倒计时定时器
      clearCountdown(orderId.value)

      showToast('订单删除成功')

      // 延迟返回上一页
      setTimeout(() => {
        router.back()
      }, 1500)

    } catch (error) {
      closeToast()
      showToast(error.message || '删除失败')
    }
  }

  try {
    // 显示删除确认对话框
    $alert({
      title: '',
      message: '确认删除该订单？',
      confirmButtonText: '确定',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        await deleteOrderFn()
      }
    })
  } catch (error) {
    console.error('删除订单操作错误:', error)
  }
}


// ==================== 再次购买功能 ====================
// 再次购买核心逻辑，验证商品可购买性并跳转到订单确认页
const buyAgainAction = async (orderInfo) => {
  const bizCode = getBizCode('ORDER')
  showLoadingToast()

  try {
    let allValidGoodsList = []  // 所有可购买的商品列表
    let totalSkuCount = 0       // 原订单商品总数

    // 如果有供应商订单列表，批量验证所有子订单
    if (orderDetailSupplierOrderVoList.value?.length > 0) {
      const verifyPromises = orderDetailSupplierOrderVoList.value.map(supplierOrder => {
        return verifySupplierOrderRepurchased({
          bizCode,
          supplierOrderId: supplierOrder.id
        })
      })

      const results = await Promise.all(verifyPromises)

      // 汇总所有验证结果
      results.forEach(([err, res], index) => {
        if (!err && res?.validGoodsList) {
          allValidGoodsList = [...allValidGoodsList, ...res.validGoodsList]
        }
        const supplierOrder = orderDetailSupplierOrderVoList.value[index]
        if (supplierOrder.orderDetailSupplierSubOrderVoList) {
          totalSkuCount += supplierOrder.orderDetailSupplierSubOrderVoList.length
        }
      })
    } else {
      // 如果没有子订单，直接验证主订单
      const [err, res] = await verifySupplierOrderRepurchased({
        bizCode,
        supplierOrderId: orderInfo.id
      })

      if (!err && res) {
        allValidGoodsList = res.validGoodsList || []
      }
      totalSkuCount = orderInfo.skuNumInfoList?.length || 0
    }

    closeToast()

    // 检查是否有可购买的商品
    if (allValidGoodsList.length === 0) {
      showToast('订单中的商品都卖光了，在看看其他商品吧~')
      return
    }

    // 将可购买商品存储到缓存中
    buyProductCart.set(allValidGoodsList)
    buyProductCartSession.set(allValidGoodsList)

    // 根据可购买商品数量决定跳转逻辑
    if (totalSkuCount === allValidGoodsList.length) {
      // 所有商品都可购买，直接跳转
      router.push('/orderconfirm')
    } else if (totalSkuCount > allValidGoodsList.length) {
      // 部分商品不可购买，显示提示后跳转
      $alert({
        title: '',
        message: '部分商品无货或已下架无法购买!',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        onConfirmCallback: () => router.push('/orderconfirm')
      })
    }
  } catch (error) {
    closeToast()
    console.error('再次购买检查失败:', error)
    showToast('操作失败，请重试')
  }
}

// 处理再次购买按钮点击事件
const handleBuyAgain = async () => {
  try {
    await buyAgainAction(orderInfo.value)
  } catch (error) {
    showToast('再次购买失败')
    console.error('再次购买错误:', error)
  }
}

// 处理催促发货操作，根据订单时间显示不同的提示信息
const handleUrgeDelivery = () => {
  const { orderDate } = orderInfo.value
  const targetDate = dayjs(orderDate)
  const now = dayjs()
  const diff = now.diff(targetDate, 'millisecond')
  const diffInHours = diff / (1000 * 60 * 60)
  const isWithin48Hours = Math.abs(diffInHours) <= 48

  if (isWithin48Hours) {
    // 48小时内的订单，显示正常发货时效提示
    const dateAdd48 = targetDate.add(48, 'hour')
    const formattedDate = dateAdd48.format('M月DD日')
    $alert({
      messageHtml: `<div>您的商品目前处于正常配送时效内，商家将于<span style="color:var(--wo-biz-theme-color)">${formattedDate}</span>前发货，请您耐心等待。</div>`,
      confirmButtonText: '确定',
      allowHtml: true,
      messageAlign: 'center'
    })
  } else {
    // 超过48小时的订单，显示催促发货提示
    $alert({
      message: '给您带来的不便深感抱歉，已为您提醒商家发货，请您耐心等待。',
      confirmButtonText: '确定',
      messageAlign: 'center'
    })
  }
}

// ==================== 物流和地址相关函数 ====================
// 处理查看物流操作，跳转到物流详情页面
const handleViewLogistics = async () => {
  const { orderDate } = orderInfo.value
  const now = dayjs()
  const orderDateDayjs = dayjs(orderDate)
  const endTimeSub180 = now.subtract(12, 'month')
  const isWithinScope = orderDateDayjs.isBefore(endTimeSub180, 'minute')

  // 检查物流信息是否已失效（超过12个月）
  if (isWithinScope) {
    showToast('物流信息已失效 ！')
    return
  }

  try {
    // 获取订单物流信息
    const [err, orderExpress] = await getOrderExpress(orderId.value)

    if (err) {
      showToast('查询物流信息失败')
      return
    }

    // 检查是否有物流包裹信息
    const packageList = get(orderExpress, 'orderPackageList', [])
    if (!isEmpty(packageList)) {
      // 跳转到物流详情页面
      router.push({
        name: 'user-order-entry-express',
        params: {
          orderExpress: orderExpress
        },
        query: {
          orderId: orderId.value
        }
      })
      return
    }

    showToast('物流信息已失效 ！')
  } catch (error) {
    console.error('查询物流信息失败:', error)
    showToast('查询物流信息失败')
  }
}

// 处理查看保证金详情操作，跳转到保证金详情页面
const handleViewDeposit = () => {
  const url = `/itf-fi-core-web/richer/assetDetail?orderNo=${orderInfo.value.bondOrderId}&orgCode=zbbank`
  window.location.href = url
}

// 处理修改收货地址操作，跳转到地址编辑页面
const handleEditAddress = async () => {
  // 京东商品需要特殊的地址修改检查
  if (isJD.value) {
    showLoadingToast()
    const [err] = await jdModifyOrderAddress(orderInfo.value.bizOrderId)
    closeToast()
    if (err) {
      showToast(err.msg || '地址修改检查失败')
      return
    }
  }

  // 跳转到地址编辑页面，传递订单ID和当前地址信息
  router.push({
    path: '/user/order/address-edit',
    query: {
      orderId: orderInfo.value.bizOrderId,
      addressInfo: encodeURIComponent(JSON.stringify(receiveData.value))
    }
  })
}

// ==================== 商品操作相关函数 ====================
// 获取商品项的操作按钮配置
const getItemActions = (item) => {
  return generateActionButtons(item, {
    showAddToCart: true,                              // 显示加入购物车按钮
    handleAddToCart: handleAddToCart,                 // 加入购物车处理函数
    useSubOrderData: true,                            // 使用子订单数据
    afterSalesInfo: afterSalesInfo.value,            // 售后信息
    currentOrderStatus: currentOrderStatus.value     // 当前订单状态
  })
}

// 处理商品点击事件，跳转到商品详情页面
const handleGoodsClick = async (goodsItem) => {
  try {
    // 从商品数据中获取goodsId和skuId
    const skuNumInfoList = goodsItem.skuNumInfoList || []
    if (skuNumInfoList.length > 0) {
      const firstSku = skuNumInfoList[0].sku
      let { goodsId, skuId, supplierCode } = firstSku

      if (goodsId) {
        // 检查商品是否存在且可访问
        const params = {
          goodsId,
          skuId,
          supplierCode
        }

        showLoadingToast()
        const [err, json] = await isCheckGoodsExistsBol(params)
        closeToast()

        if (!err) {
          if (json) {
            goodsId = json
            // 跳转到商品详情页面
            router.push({
              name: 'goods-detail',
              params: {
                goodsId: goodsId,
                skuId: skuId || undefined
              }
            })
          } else {
            showToast('由于该商品已下架，为您跳转至商城首页，请您选购其他商品吧~')
            // 延迟跳转到当前商城首页
            setTimeout(() => {
              const hostname = window.location.hostname
              if (hostname === 'epay.10010.com') {
                window.location.href = `/canary-ps-ccms-biz/home?distri_biz_code=${getBizCode()}`
              } else {
                window.location.href = `/ps-ccms-biz/home?distri_biz_code=${getBizCode()}`
              }
            }, 2000)
            return
          }
        } else {
          showToast('商品信息获取失败，请重试')
        }
      } else {
        showToast('商品信息不完整，无法查看详情')
      }
    } else {
      showToast('商品信息不完整，无法查看详情')
    }
  } catch (error) {
    closeToast()
    console.error('跳转商品详情失败:', error)
    showToast('跳转失败，请重试')
  }
}

// ==================== 底部操作按钮配置 ====================
// 根据订单状态获取底部操作按钮配置
const getBottomActions = (orderState) => {
  const actions = []

  switch (orderState) {
    case '0':  // 待付款状态
      actions.push(
        {
          key: 'cancelOrder',
          label: '取消订单',
          type: 'text',
          handler: handleCancelOrder
        },
        {
          key: 'goToPay',
          label: '去支付',
          type: 'gradient',
          handler: handleGoToPay
        }
      )
      break

    case '2':  // 已取消状态
      actions.push(
        {
          key: 'deleteOrder',
          label: '删除订单',
          type: 'secondary',
          handler: handleDeleteOrder
        },
        {
          key: 'buyAgain',
          label: '再次购买',
          type: 'gradient',
          handler: handleBuyAgain
        }
      )
      break

    case '1':  // 待发货状态
    case '3':  // 待发货状态
      actions.push(
        {
          key: 'buyAgain',
          label: '再次购买',
          type: 'secondary',
          handler: handleBuyAgain
        },
        {
          key: 'urgeDelivery',
          label: '催发货',
          type: 'gradient',
          handler: handleUrgeDelivery
        }
      )
      break

    case '5':  // 配送中状态
      actions.push(
        {
          key: 'viewLogistics',
          label: '查看物流',
          type: 'secondary',
          handler: handleViewLogistics
        },
        {
          key: 'urgeDelivery',
          label: '催发货',
          type: 'gradient',
          handler: handleUrgeDelivery
        }
      )
      break

    case '9':  // 已签收状态
      actions.push(
        {
          key: 'deleteOrder',
          label: '删除订单',
          type: 'secondary',
          handler: handleDeleteOrder
        },
        {
          key: 'viewLogistics',
          label: '查看物流',
          type: 'secondary',
          handler: handleViewLogistics
        }
        // 已签收状态不显示催发货按钮
      )
      break

    case '10':  // 已退款状态
      actions.push(
        {
          key: 'deleteOrder',
          label: '删除订单',
          type: 'secondary',
          handler: handleDeleteOrder
        },
        {
          key: 'buyAgain',
          label: '再次购买',
          type: 'gradient',
          handler: handleBuyAgain
        }
      )
      break
  }

  return actions
}

// ==================== 生命周期管理 ====================
// 组件挂载时获取订单详情数据
onMounted(() => {
  getOrderInfoData()
})

// 组件卸载时清理倒计时定时器
onUnmounted(() => {
  clearCountdown()
})
</script>

<style scoped lang="less">
.order-detail {
  min-height: 100vh;
  background-color: #F8F9FA;

  &__header {
    width: 100%;
    height: 70px;
    background: #FFFFFF;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &__main {
    padding: 8px 10px;
    box-sizing: border-box;
  }

  &__status {
    &-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #171E24;
      line-height: 1.5;
    }

    &-subtitle {
      font-size: 15px;
      font-weight: 400;
      line-height: 1.5;
      color: #171E24;
    }

    &-time {
      color: var(--wo-biz-theme-color);
      font-weight: 600;
    }
  }



  &__order-number {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;

    &-text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      ;
      max-width: calc(100% - 24px);
    }

    &-copy {
      flex-shrink: 0;
      margin-left: 8px;
      font-size: 11px;
      color: #171E24;
      background: #F6F6F6;
      border-radius: 10px;
      width: 38px;
      height: 18px;
      box-sizing: border-box;
      text-align: center;
      line-height: 18px;
      cursor: pointer;
    }
  }

  &__receiver-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    line-height: 1.5;
  }

  &__receiver-contact {
    font-size: 13px;
    color: #171E24;
  }

  &__receiver-address {
    flex: 1;
    font-size: 13px;
    color: #171E24;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    ;
  }

  &__action-bar {
    width: 100%;
    display: flex;
    gap: 12px;
    justify-content: flex-end;

    &--pending-payment {
      justify-content: space-between;
    }
  }

}
</style>
