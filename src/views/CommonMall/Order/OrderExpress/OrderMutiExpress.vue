<!--
/**
 * 多快递包裹页面组件
 *
 * 主要功能：
 * 1. 展示订单中的多个快递包裹列表
 * 2. 支持点击单个包裹跳转到对应的物流详情页面
 * 3. 区分京东商品和普通商品的不同状态显示
 * 4. 提供多包裹提示信息，帮助用户理解订单分包情况
 * 5. 支持政企业务的角色权限控制
 *
 * 包裹状态：
 * - 已发货包裹：显示具体的物流状态信息
 * - 未发货包裹：显示待发货状态
 * - 京东商品：使用京东专用的状态映射
 * - 普通商品：使用标准的快递状态映射
 *
 * 技术特点：
 * - 使用shallowRef优化大数组的响应式性能
 * - 集成memoize缓存优化重复计算
 * - 使用防抖优化点击事件，避免重复跳转
 * - 支持路由参数和API数据的双重数据源
 *
 * 使用场景：
 * - 订单包含多个快递包裹时的物流查询
 * - 从订单详情页跳转而来的多包裹展示
 */
-->

<template>
  <!-- 多快递包裹页面主容器 -->
  <main class="multi-express">
    <!-- 多快递加载骨架屏 -->
    <MultiExpressSkeleton v-if="isLoading" :item-count="2" />

    <!-- 多快递内容区域 -->
    <template v-else>
      <!-- 多包裹提示组件 -->
      <!-- 当包裹数量大于1时显示提示信息 -->
      <MultiExpressNotice
        v-if="shouldShowNotice"
        :package-count="expressList.length"
      />

      <!-- 快递包裹列表 -->
      <div class="multi-express__list">
        <!-- 遍历显示每个快递包裹 -->
        <PackageItem
          v-for="(item, index) in expressList"
          :key="`package-${index}`"
          :package-data="item"
          :index="index"
          :is-j-d-package="isJDGoods(item)"
          :status-text="getStatusText(item)"
          @click="handleExpressClick"
        />
      </div>
    </template>
  </main>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, shallowRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showLoadingToast, closeToast } from 'vant'
import { debounce, memoize } from 'es-toolkit'
import { get } from 'es-toolkit/compat'
import MultiExpressSkeleton from './components/MultiExpressSkeleton.vue'
import MultiExpressNotice from './components/MultiExpressNotice.vue'
import PackageItem from './components/PackageItem.vue'

import { getOrderExpress } from '@api/interface/order.js'
import { getBizCode } from '@utils/curEnv.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo } from "@utils/zqInfo.js"
import orderState from '@utils/orderState.js'
import { JD_GOODS_CODE } from '@utils/types.js'

// ==================== 核心依赖实例 ====================
// 当前路由信息实例
const route = useRoute()
// 路由导航实例
const router = useRouter()

// ==================== 多快递状态管理 ====================
// 订单ID
const orderId = ref('')
// 订单快递信息，使用shallowRef优化性能
const orderExpress = shallowRef({})
// 页面加载状态
const isLoading = ref(true)

// ==================== 快递列表计算属性 ====================
// 合并已发货和未发货的包裹列表
const expressList = computed(() => {
  if (!orderExpress.value || typeof orderExpress.value !== 'object') return []

  const orderPackageList = get(orderExpress.value, 'orderPackageList', [])  // 已发货包裹
  const notDelivered = get(orderExpress.value, 'notDelivered', [])          // 未发货包裹
  return [...orderPackageList, ...notDelivered]
})

// 判断是否显示多包裹提示
const shouldShowNotice = computed(() => expressList.value.length > 1)

// ==================== 商品类型判断 ====================
// 京东商品代码正则表达式
const JD_CODE_REGEX = new RegExp(JD_GOODS_CODE)

// 使用memoize缓存京东商品判断结果
const isJDGoods = memoize((item) => {
  const supplierCode = get(item, 'supplierSubOrderList[0].supplier.code', '')
  return JD_CODE_REGEX.test(supplierCode)
})

// ==================== 用户角色信息 ====================
// 获取用户角色类型，用于政企业务的权限控制
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// ==================== 数据获取功能 ====================
// 获取订单快递信息的异步函数
const getOrderExpressData = async () => {
  try {
    showLoadingToast()
    const bizCode = getBizCode()
    // 政企业务需要传递角色类型参数
    const roleTypeParam = bizCode === 'zq' ? roleType.value : ''
    const [err, json] = await getOrderExpress(orderId.value, roleTypeParam)
    closeToast()

    if (!err && json) {
      return json
    }
    return {}
  } catch (error) {
    return {}
  }
}

// ==================== 用户交互功能 ====================
// 处理快递包裹点击事件，使用防抖避免重复跳转
const handleExpressClick = debounce((index) => {
  const item = expressList.value[index]
  const deliverInfo = get(item, 'deliverInfo')

  // 只有包含快递信息的包裹才能跳转
  if (deliverInfo?.expressName) {
    router.push({
      name: 'user-order-express',
      query: {
        orderId: orderId.value,
        supplierSubOrderId: deliverInfo.supplierSubOrderId,
        expressNo: deliverInfo.expressNo
      }
    })
  }
}, 300)

// ==================== 快递状态映射 ====================
// 快递状态码到中文描述的映射表
const EXPRESS_STATE_MAP = new Map([
  ['0', '运输中'],
  ['1', '已揽件'],
  ['2', '疑难件'],
  ['3', '已签收'],
  ['4', '退签'],
  ['5', '派送中'],
  ['6', '退回'],
  ['7', '转单'],
  ['10', '待清关'],
  ['11', '清关中'],
  ['12', '已清关'],
  ['13', '清关异常'],
  ['14', '收件人拒签']
])

// 使用memoize缓存普通快递状态转换结果
const toExpressState = memoize((expressState) =>
  EXPRESS_STATE_MAP.get(String(expressState)) || '待揽件'
)

// 使用memoize缓存京东快递状态转换结果
const toJDExpressState = memoize((state) => orderState(state))

// ==================== 状态文字获取功能 ====================
// 根据商品类型获取对应的状态文字
const getStatusText = (item) => {
  // 京东商品使用京东专用状态映射
  if (isJDGoods(item)) {
    return toJDExpressState(item.orderState)
  }

  // 普通商品使用标准快递状态映射
  const expressState = get(item, 'deliverInfo.expressState')
  if (expressState && toExpressState(expressState)) {
    return toExpressState(expressState)
  }

  return ''
}

// ==================== 生命周期管理 ====================
// 组件挂载时初始化数据
onMounted(async () => {
  // 从路由查询参数获取订单ID
  orderId.value = route.query.orderId
  // 尝试从路由参数获取快递信息
  const orderExpressParam = route.params.orderExpress

  // 优先使用路由参数中的数据，避免不必要的API调用
  if (orderExpressParam) {
    orderExpress.value = orderExpressParam
    isLoading.value = false
  } else {
    // 路由参数中没有数据时，从API获取快递信息
    try {
      const data = await getOrderExpressData()
      orderExpress.value = data
    } finally {
      isLoading.value = false
    }
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  closeToast()
})
</script>

<style lang="less" scoped>
.multi-express {
  background-color: #f7f7f7cc;
  min-height: 100vh;
  contain: layout style;

  &__list {
    margin-top: -10px;
    padding: 0 10px;
  }
}
</style>
