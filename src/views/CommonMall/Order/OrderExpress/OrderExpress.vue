<!--
/**
 * 订单物流详情页面组件
 *
 * 主要功能：
 * 1. 展示单个快递包裹的详细物流跟踪信息
 * 2. 提供快递单号复制功能，方便用户在第三方平台查询
 * 3. 根据物流数据状态智能显示不同的界面状态
 * 4. 支持政企业务的角色权限控制
 * 5. 集成物流时间轴展示，提供清晰的物流进度可视化
 *
 * 界面状态：
 * - 有物流跟踪数据：显示完整的物流时间轴
 * - 有快递信息但无跟踪数据：显示查询指引
 * - 无任何物流信息：显示空状态提示
 * - 数据加载中：显示骨架屏
 *
 * 技术特点：
 * - 使用shallowRef优化大数组的响应式性能
 * - 集成memoize缓存优化重复计算
 * - 支持剪贴板操作，提升用户体验
 * - 采用组件化设计，职责分离清晰
 *
 * 使用场景：
 * - 用户查看单个快递包裹的物流详情
 * - 从订单详情页或多快递页面跳转而来
 */
-->

<template>
  <!-- 物流详情页面主容器 -->
  <main class="express-page">
    <!-- 物流头部信息组件 -->
    <!-- 显示订单号、快递公司名称、快递单号等基本信息 -->
    <ExpressHeader
      :order-id="orderId"
      :express-name="displayData.expressName"
      :express-no="displayData.expressNo"
      @copy="handleCopyExpressNo"
    />

    <!-- 物流时间轴组件 -->
    <!-- 当有完整物流跟踪数据时显示 -->
    <ExpressTimeline
      v-if="orderTrackStatus === 1"
      :tracking-data="orderTrack"
    />

    <!-- 物流查询指引组件 -->
    <!-- 当有快递信息但无跟踪数据时显示 -->
    <ExpressQueryGuide v-else-if="orderTrackStatus === 2" />

    <!-- 物流空状态组件 -->
    <!-- 当无任何物流信息时显示 -->
    <ExpressEmptyState v-else-if="orderTrackStatus === 0" />

    <!-- 物流加载骨架屏 -->
    <!-- 数据加载过程中显示 -->
    <ExpressSkeleton v-else :skeleton-count="3" />
  </main>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, shallowRef } from 'vue'
import { useRoute } from 'vue-router'
import { showLoadingToast, closeToast, showToast } from 'vant'
import { memoize } from 'es-toolkit'
import { isEmpty, get, defaults } from 'es-toolkit/compat'
import useClipboard from 'vue-clipboard3'

import ExpressHeader from './components/ExpressHeader.vue'
import ExpressTimeline from './components/ExpressTimeline.vue'
import ExpressQueryGuide from './components/ExpressQueryGuide.vue'
import ExpressEmptyState from './components/ExpressEmptyState.vue'
import ExpressSkeleton from './components/ExpressSkeleton.vue'

import { getExpress } from '@api/interface/order.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo } from "@utils/zqInfo.js"
import { getBizCode } from '@utils/curEnv.js'

// ==================== 核心依赖实例 ====================
// 当前路由信息实例
const route = useRoute()
// 剪贴板操作工具
const { toClipboard } = useClipboard()

// ==================== 物流信息状态管理 ====================
// 订单ID
const orderId = ref('')
// 快递公司名称
const expressName = ref('--')
// 快递单号
const expressNo = ref('--')
// 物流跟踪记录列表，使用shallowRef优化性能
const orderTrack = shallowRef([])
// 物流跟踪状态：-1加载中，0无数据，1有完整数据，2有基本信息无跟踪数据
const orderTrackStatus = ref(-1)
// 页面加载状态
const isLoading = ref(true)

// ==================== 路由参数处理 ====================
// 格式化路由查询参数，提供默认值
const routeParams = computed(() =>
  defaults(route.query, {
    orderId: '',              // 订单ID
    supplierSubOrderId: '',   // 供应商子订单ID
    expressNo: ''             // 快递单号
  })
)

// ==================== 显示数据计算属性 ====================
// 格式化显示数据，确保有默认值
const displayData = computed(() => ({
  expressName: expressName.value || '--',  // 快递公司名称
  expressNo: expressNo.value || '--'       // 快递单号
}))

// ==================== 用户角色信息 ====================
// 获取用户角色类型，用于政企业务的权限控制
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// ==================== 初始化功能 ====================
// 使用memoize缓存基础信息初始化，避免重复计算
const initializeBasicInfo = memoize(() => {
  orderId.value = get(routeParams.value, 'orderId', '')
})

// ==================== 数据获取功能 ====================
// 获取快递物流信息的异步函数
const getExpressData = async (orderIdParam, expressNoParam, roleType) => {
  try {
    const bizCode = getBizCode()
    // 政企业务需要传递角色类型参数
    const roleTypeParam = bizCode === 'zq' ? roleType : ''
    const [err, json] = await getExpress(orderIdParam, expressNoParam, roleTypeParam)
    return err ? {} : json
  } catch (error) {
    return {}
  }
}

// ==================== 数据更新功能 ====================
// 更新物流信息并设置对应的显示状态
const updateExpressInfo = (deliverInfo) => {
  // 为物流信息提供安全的默认值
  const safeDeliverInfo = defaults(deliverInfo, {
    expressName: '--',    // 快递公司名称默认值
    expressNo: '--',      // 快递单号默认值
    orderTrack: []        // 物流跟踪记录默认值
  })

  // 更新物流基本信息
  expressName.value = safeDeliverInfo.expressName
  expressNo.value = safeDeliverInfo.expressNo
  orderTrack.value = safeDeliverInfo.orderTrack

  // 根据数据完整性设置显示状态
  if (!isEmpty(deliverInfo) && safeDeliverInfo?.orderTrack?.length > 0) {
    // 有完整的物流跟踪数据
    orderTrackStatus.value = 1
  } else if (!isEmpty(deliverInfo) && (safeDeliverInfo.expressName !== '--' || safeDeliverInfo.expressNo !== '--')) {
    // 有基本快递信息但无跟踪数据
    orderTrackStatus.value = 2
  } else {
    // 无任何物流信息
    orderTrackStatus.value = 0
  }
}

// ==================== 用户交互功能 ====================
// 处理快递单号复制功能
const handleCopyExpressNo = async (expressNumber) => {
  try {
    await toClipboard(expressNumber)
    showToast('复制成功')
  } catch (e) {
    showToast('复制失败')
  }
}

// ==================== 生命周期管理 ====================
// 组件挂载时初始化数据并加载物流信息
onMounted(async () => {
  // 初始化基础信息
  initializeBasicInfo()
  await nextTick()

  try {
    showLoadingToast()
    // 从路由参数获取必要的查询参数
    const { supplierSubOrderId, expressNo: expressNoParam } = routeParams.value
    // 获取物流详情数据
    const deliverInfo = await getExpressData(supplierSubOrderId, expressNoParam, roleType.value)
    // 更新物流信息并设置显示状态
    updateExpressInfo(deliverInfo)
  } catch (error) {
    // 数据加载失败时设置为空状态
    orderTrackStatus.value = 0
  } finally {
    isLoading.value = false
    closeToast()
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  closeToast()
})
</script>

<style lang="less" scoped>
.express-page {
  width: 100%;
  min-height: 100vh;
  background: #FFFFFF;
  will-change: transform;
  contain: layout style;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
