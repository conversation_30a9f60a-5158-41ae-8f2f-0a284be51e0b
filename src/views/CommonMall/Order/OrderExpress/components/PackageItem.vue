<template>
  <article class="package-item" @click="handleClick">
    <header class="package-item__header">
      <div class="package-item__info">
        <i class="package-item__icon"></i>
        <span class="package-item__title">包裹 {{ index + 1 }}</span>
        <span
          v-if="statusText"
          class="package-item__status"
          :class="{ 'package-item__status--jd': isJDPackage }"
        >
          {{ statusText }}
        </span>
      </div>
      <div class="package-item__express">
        <span class="package-item__express-info">
          <template v-if="expressInfo">
            {{ expressInfo }}
          </template>
          <template v-else>暂无快递信息</template>
        </span>
        <i v-if="expressInfo" class="package-item__arrow"></i>
      </div>
    </header>

    <div class="package-item__content">
      <ul class="goods-list">
        <li class="goods-list__item" v-for="(goods, goodsIdx) in goodsList" :key="`goods-${index}-${goodsIdx}`">
          <GoodsCard :goods="goods" :index="goodsIdx" />
        </li>
      </ul>
    </div>

    <footer class="package-item__footer">
      <div class="package-summary">
        <span class="package-summary__total">共{{ totalCount }}件商品</span>
      </div>
    </footer>
  </article>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import { get } from 'es-toolkit/compat'
import GoodsCard from './GoodsCard.vue'

const props = defineProps({
  packageData: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  isJDPackage: {
    type: Boolean,
    default: false
  },
  statusText: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['click'])

const { packageData, index, isJDPackage, statusText } = toRefs(props)

const expressInfo = computed(() => {
  const deliverInfo = get(packageData.value, 'deliverInfo')
  if (deliverInfo?.expressName) {
    return `${deliverInfo.expressName}：${deliverInfo.expressNo}`
  }
  return null
})

const goodsList = computed(() => {
  if (!packageData.value) return []

  if (packageData.value.supplierSubOrderList?.length) {
    return packageData.value.supplierSubOrderList.flatMap(subOrder => subOrder.skuNumInfoList || [])
  }
  return packageData.value.skuNumInfoList || []
})

const totalCount = computed(() =>
  get(packageData.value, 'deliverInfo.sendNum', 0)
)

const handleClick = () => {
  emit('click', index.value)
}
</script>

<style lang="less" scoped>
.package-item {
  margin-top: 10px;
  background-color: #FFFFFF;
  border-radius: 4px4;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    background-color: #f8f9fa;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 17px;
    border-bottom: 1px solid #e5e5e5a3;
  }

  &__info {
    display: flex;
    align-items: center;
    padding: 10px 0;
  }

  &__express {
    display: flex;
    align-items: center;
    padding: 10px 0;

    &-info {
      margin-right: 5px;
      font-size: 13px;
      color: #4A5568;
    }
  }

  &__icon {
    width: 15px;
    height: 15px;
    background-image: url(../assets/icon-goods.png);
    background-repeat: no-repeat;
    background-size: 100%;
    flex-shrink: 0;
  }

  &__title {
    margin-left: 3px;
    font-size: 14px;
    color: #171E24;
  }

  &__status {
    padding: 1px 3px;
    border: 1px solid var(--wo-biz-theme-color);
    border-radius: 2px;
    font-size: 9px;
    color: var(--wo-biz-theme-color);
    transform: scale(0.8);
    box-sizing: border-box;
    margin-left: 5px;

    &--jd {
      border-color: var(--wo-biz-theme-color);
      color: var(--wo-biz-theme-color);
    }
  }

  &__arrow {
    width: 17px;
    height: 17px;
    background-image: url(../assets/arrow.png);
    background-repeat: no-repeat;
    background-size: 100%;
    flex-shrink: 0;
  }

  &__content {
    margin: 0 15px;
    overflow: hidden;
  }

  &__footer {
    padding: 0 17px 15px;
    font-size: 13px;
    color: #171E24;
  }
}

.goods-list {
  display: flex;
  overflow-x: auto;
  margin-left: -15px;
  padding: 15px 0;
  -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };

  &__item {
    padding-left: 15px;
    display: flex;
    flex-shrink: 0;
  }
}

.package-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
