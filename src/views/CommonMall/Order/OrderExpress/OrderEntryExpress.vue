<!--
/**
 * 订单物流入口组件
 *
 * 主要功能：
 * 1. 作为物流查询的入口页面，自动判断订单物流情况并跳转到相应页面
 * 2. 支持单个快递和多个快递的智能路由分发
 * 3. 优先使用路由参数中的数据，避免不必要的API调用
 * 4. 集成政企业务支持，根据用户角色类型获取对应的物流信息
 * 5. 提供防抖优化的导航功能，避免重复跳转
 *
 * 技术特点：
 * - 无模板设计，纯逻辑组件
 * - 使用计算属性缓存复杂计算结果
 * - 集成防抖机制优化用户体验
 * - 支持路由参数和API数据的双重数据源
 *
 * 使用场景：
 * - 从订单详情页跳转到物流查询时的中转页面
 * - 自动判断物流包裹数量并分发到对应的物流页面
 */
-->

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showLoadingToast, closeToast } from 'vant'
import { debounce } from 'es-toolkit'
import { isEmpty }  from 'es-toolkit/compat'

import { getOrderExpress } from '@api/interface/order.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo } from "@utils/zqInfo.js"
import { getBizCode } from '@utils/curEnv.js'

// ==================== 核心依赖实例 ====================
// 当前路由信息实例
const route = useRoute()
// 路由导航实例
const router = useRouter()

// ==================== 订单物流状态管理 ====================
// 订单ID
const orderId = ref('')
// 订单物流信息
const orderExpress = ref({})
// 导航状态标识，防止重复跳转
const isNavigating = ref(false)

// ==================== 用户角色信息 ====================
// 获取用户角色类型，用于政企业务的权限控制
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// ==================== 数据获取功能 ====================
// 获取订单快递信息的异步函数
const getOrderExpressData = async () => {
  showLoadingToast()
  try {
    const bizCode = getBizCode()
    // 政企业务需要传递角色类型参数
    const roleTypeParam = bizCode === 'zq' ? roleType.value : ''
    const [err, json] = await getOrderExpress(orderId.value, roleTypeParam)
    closeToast()

    // 检查返回数据的有效性
    if (!err && !isEmpty(json)) {
      return json
    }
    return {}
  } catch (error) {
    closeToast()
    return {}
  }
}

// ==================== 物流信息处理 ====================
// 使用计算属性缓存物流信息的复杂计算
const expressInfo = computed(() => {
  const data = orderExpress.value
  if (isEmpty(data)) return { delivered: [], notDelivered: [], count: 0 }

  const delivered = data.orderPackageList || []      // 已发货包裹列表
  const notDelivered = data.notDelivered || []       // 未发货包裹列表
  const count = delivered.length + notDelivered.length  // 总包裹数量

  return { delivered, notDelivered, count }
})

// ==================== 路由跳转逻辑 ====================
// 根据物流包裹数量智能跳转到对应页面
const gotoExpress = () => {
  // 防止重复导航
  if (isNavigating.value) return

  const { delivered, count } = expressInfo.value

  // 没有物流信息时直接返回
  if (count === 0) {
    return
  }

  isNavigating.value = true

  try {
    if (count > 1) {
      // 多个快递包裹，跳转到多快递页面
      router.replace({
        name: 'user-order-muti-express',
        query: { orderId: orderId.value },
        params: { orderExpress: orderExpress.value }
      })
    } else {
      // 单个快递包裹，直接跳转到快递详情页面
      const firstPackage = delivered[0]
      if (!firstPackage?.deliverInfo) {
        return
      }

      router.replace({
        name: 'user-order-express',
        query: {
          orderId: orderId.value,
          supplierSubOrderId: firstPackage.deliverInfo.supplierSubOrderId,
          expressNo: firstPackage.deliverInfo.expressNo
        }
      })
    }
  } catch (error) {
    // 路由跳转失败时的错误处理
  } finally {
    isNavigating.value = false
  }
}

// 使用防抖优化导航函数，避免快速重复点击
const debouncedGotoExpress = debounce(gotoExpress, 100)

// ==================== 生命周期管理 ====================
// 组件挂载时初始化数据并执行跳转逻辑
onMounted(async () => {
  // 从路由查询参数获取订单ID
  orderId.value = route.query.orderId

  if (!orderId.value) {
    return
  }

  const routeOrderExpress = route.params.orderExpress

  // 优先使用路由参数中的数据，避免不必要的API调用
  if (routeOrderExpress && !isEmpty(routeOrderExpress) && routeOrderExpress.orderPackageList) {
    orderExpress.value = routeOrderExpress
  } else {
    // 路由参数中没有数据时，从API获取物流信息
    orderExpress.value = await getOrderExpressData()
  }

  // 执行防抖优化的跳转逻辑
  debouncedGotoExpress()
})

// 组件卸载时清理资源
onUnmounted(() => {
  closeToast()
  debouncedGotoExpress.cancel()
})
</script>
