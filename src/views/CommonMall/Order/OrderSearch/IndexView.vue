<!--
/**
 * 订单搜索入口页面组件
 *
 * 主要功能：
 * 1. 提供订单搜索的入口界面，包含搜索框和搜索历史
 * 2. 展示用户的搜索历史记录，支持快速重复搜索
 * 3. 支持搜索历史的使用和清除功能
 * 4. 集成防抖搜索，优化用户输入体验
 * 5. 自动聚焦搜索框，提升用户操作便利性
 *
 * 搜索功能：
 * - 支持商品名称、订单号等关键词搜索
 * - 自动保存搜索历史记录
 * - 支持测试模式参数传递
 *
 * 技术特点：
 * - 使用toRefs保持路由参数的响应性
 * - 集成防抖机制优化搜索性能
 * - 采用组件化设计，职责分离清晰
 * - 支持搜索历史的异步加载和管理
 *
 * 使用场景：
 * - 用户需要搜索特定订单时的入口页面
 * - 从订单列表页跳转而来的搜索功能
 */
-->

<template>
  <!-- 订单搜索页面主容器 -->
  <main class="order-search">
    <!-- 搜索头部组件 -->
    <!-- 提供搜索输入框和搜索功能 -->
    <SearchHeader
      ref="searchHeaderRef"
      v-model="searchKeyword"
      placeholder="搜索商品"
      @search="handleSearch"
    />

    <!-- 搜索历史组件 -->
    <!-- 当有历史记录时显示，支持快速重复搜索 -->
    <SearchHistory
      v-if="historyRecords.length > 0"
      :records="historyRecords"
      @use-keyword="useHistoryKeyword"
      @clear-all="clearAllHistory"
    />
  </main>
</template>

<script setup>
import { onMounted, ref, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce } from 'es-toolkit'
import { get } from 'es-toolkit/compat'
import { closeToast, showLoadingToast } from 'vant'

import SearchHeader from '@components/Common/SearchHeader.vue'
import SearchHistory from './components/SearchHistory.vue'

import { delHistoryRecord, getOrderSearchHistory } from '@api/interface/search.js'

// ==================== 核心依赖实例 ====================
// 当前路由信息实例
const route = useRoute()
// 路由导航实例
const router = useRouter()
// 使用toRefs保持路由查询参数的响应性
const { query } = toRefs(route)

// ==================== 搜索状态管理 ====================
// 搜索关键词
const searchKeyword = ref('')
// 搜索历史记录列表
const historyRecords = ref([])
// 搜索头部组件引用
const searchHeaderRef = ref(null)

// ==================== 搜索历史功能 ====================
// 获取搜索历史记录
const fetchHistoryRecords = async () => {
  showLoadingToast()
  const [err, data] = await getOrderSearchHistory()
  closeToast()
  if (!err && data) {
    historyRecords.value = data
  }
}

// 使用历史搜索关键词
const useHistoryKeyword = (keyword) => {
  searchKeyword.value = keyword
}

// 清除所有搜索历史
const clearAllHistory = async () => {
  const [err] = await delHistoryRecord({
    type: 'ALL'
  })
  if (!err) {
    historyRecords.value = []
  }
}

// ==================== 搜索功能 ====================
// 处理搜索事件，使用防抖优化性能
const handleSearch = debounce(() => {
  // 获取测试模式参数
  const testDMX = get(query.value, 'testDMX', false)
  // 去除关键词首尾空格
  const trimmedKeyword = searchKeyword.value?.trim()

  if (trimmedKeyword) {
    // 跳转到搜索结果页面
    router.push({
      path: '/user/order/searchList',
      query: {
        keyword: trimmedKeyword,
        testDMX
      }
    })
    // 刷新搜索历史记录
    fetchHistoryRecords()
  }
}, 300)

// ==================== 生命周期管理 ====================
// 组件挂载时初始化数据和聚焦搜索框
onMounted(() => {
  // 加载搜索历史记录
  fetchHistoryRecords()
  // 自动聚焦搜索输入框
  searchHeaderRef.value?.inputRef?.focus()
})
</script>
<style scoped lang="less">
.order-search {
  min-height: 100vh;
  background-color: #FFFFFF;
}
</style>
