<!--
/**
 * 商品搜索列表页面组件
 *
 * 主要功能：
 * 1. 提供商品搜索功能，支持关键词搜索和筛选条件组合查询
 * 2. 支持多种布局模式（列表布局和瀑布流布局），提供灵活的商品展示方式
 * 3. 集成省分筛选功能，支持按省分和服务商进行商品筛选
 * 4. 提供排序和筛选功能，包括价格排序、销量排序、品牌筛选等
 * 5. 支持无限滚动加载，提供流畅的商品浏览体验
 * 6. 集成购物车功能，支持商品加购和购物车跳转
 * 7. 支持地址切换功能，根据配送地址筛选可配送商品
 *
 * 技术特点：
 * - 使用防抖技术优化搜索性能，避免频繁请求
 * - 采用响应式设计，支持不同屏幕尺寸的设备
 * - 集成状态管理，实现数据持久化和组件间通信
 * - 支持动态头部高度计算，适配不同业务场景的头部组件
 * - 实现智能分页加载，自动处理空页面和加载完成状态
 *
 * 使用场景：
 * - 用户搜索特定商品时的结果展示页面
 * - 商品分类浏览和筛选场景
 * - 需要支持多维度筛选的商品列表展示
 */
-->

<template>
  <!-- 商品搜索列表页面主容器 -->
  <div class="search-list-page">
    <!-- 页面头部区域，包含省分筛选、搜索框、排序筛选栏 -->
    <!-- 使用ref获取头部高度，用于动态计算页面内容区域的padding-top -->
    <header ref="pageHeaderRef" class="page-header">
      <!-- 省分筛选组件，支持按省分和服务商筛选商品 -->
      <!-- 当用户确认筛选条件时触发handleProvinceFilterConfirm事件 -->
      <ProvinceFilter @confirm="handleProvinceFilterConfirm" />

      <!-- 搜索头部组件，提供搜索输入框和布局切换按钮 -->
      <!-- 使用v-model双向绑定搜索关键词，当用户搜索时触发handleSearch事件 -->
      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" @search="handleSearch">
        <!-- 右侧操作区域插槽，包含布局切换按钮 -->
        <template #right-action>
          <!-- 布局切换按钮，仅在非zq业务场景下显示 -->
          <!-- 根据当前布局模式显示不同的图标，点击切换列表/瀑布流布局 -->
          <button v-if="bizCode !== 'zq'" class="layout-toggle" @click="toggleLayout" type="button">
            <img :src="isWaterfallLayout ? switchLayout2Img : switchLayoutImg" alt="切换布局" width="20" height="20" />
          </button>
        </template>
      </SearchHeader>

      <!-- 排序筛选栏，提供排序和筛选功能 -->
      <!-- 仅在非zq业务场景下显示，传递当前排序类型、排序顺序和筛选条件状态 -->
      <!-- 监听排序变化和筛选切换事件 -->
      <SortFilterBar v-if="bizCode !== 'zq'" :sort-type="sortType" :sort-order="sortOrder"
        :has-filter-conditions="hasFilterConditions" @sort-change="handleSortChange" @filter-toggle="toggleFilter" />
    </header>

    <!-- 商品内容展示区域 -->
    <main class="goods-content">
      <!-- 商品列表布局组件，支持列表和瀑布流两种布局模式 -->
      <!-- 传递商品列表数据、加载状态、布局模式等属性 -->
      <!-- 监听加载更多、商品点击、加购等事件 -->
      <GoodsListLayout :goods-list="goodsList" :is-loading="isLoading" :loading="loading" :finished="finished"
        :is-waterfall="isWaterfallLayout" :breakpoints="breakpoints" :empty-image="isWishShow ? 'wishlist' : 'default'" empty-description="未搜到相关商品"
        @load-more="onLoad" @item-click="goToDetail" @add-cart="addOneCart" @update:loading="(val) => loading = val"
        :empty-button="isWishShow ? {
          text: '填写心愿单',
          type: 'primary',
          size: 'medium',
        } : null"
        @empty-button-click="handleEmptyButtonClick" />
    </main>

    <!-- 悬浮购物车按钮，提供快速访问购物车的入口 -->
    <!-- 根据业务场景和用户角色控制显示状态 -->
    <FloatingBubble :offset="floatingBubbleOffset" @go-to-cart="goToCart"
      :is-show-cart="bizCode !== 'zq' || (bizCode === 'zq' && roleType !== '2')" />

    <!-- 筛选弹窗，提供详细的商品筛选功能 -->
    <!-- 仅在非zq业务场景下显示，支持品牌、价格区间等多维度筛选 -->
    <!-- 使用v-model双向绑定弹窗显示状态和筛选条件 -->
    <FilterPopup v-if="bizCode !== 'zq'" v-model:show="isPopupShow" v-model="filterCriteria"
      :location-text="locationText" :category-id="categoryId" :keyword="searchKeyword"
      @switch-address="setSwitchAddressPopupShow" @confirm="handleFilterConfirm" @reset="handleFilterReset" />

    <!-- 地址切换弹窗，支持用户切换配送地址 -->
    <!-- 仅在非zq业务场景下显示，当地址变化时重新筛选可配送商品 -->
    <AddressSwitchPopup v-if="bizCode !== 'zq'" v-model:show="isSwitchAddressPopupShow"
      @address-changed="handleAddressChanged" />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce, compact } from 'es-toolkit'
import { get } from 'es-toolkit/compat'
import SearchHeader from '@components/Common/SearchHeader.vue'
import AddressSwitchPopup from '@components/Common/FilterTools/AddressSwitchPopup.vue'
import FilterPopup from '@components/Common/FilterTools/FilterPopup.vue'
import FloatingBubble from '@components/Common/FloatingBubble.vue'
import SortFilterBar from '@components/Common/FilterTools/SortFilterBar.vue'
import GoodsListLayout from '@components/GoodsListCommon/GoodsListLayout.vue'
import { useGoodsList } from '@/composables/useGoodsList.js'
import { getBizCode } from '@utils/curEnv.js'
import { searchKeyWord } from '@api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import switchLayoutImg from '@/static/images/switch-layout.png'
import switchLayout2Img from '@/static/images/switch-layout2.png'
import { useUserStore } from '@store/modules/user.js'
import { getDefaultBreakpoints } from '@/config/responsive.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo } from '@utils/zqInfo.js'

// ==================== 组件和工具导入 ====================
// 异步加载省分筛选组件，优化首屏加载性能
const ProvinceFilter = defineAsyncComponent(() => import('@components/ZQCommon/ProvinceFilter.vue'))

// ==================== 基础配置和状态管理 ====================
// 获取当前业务代码，用于区分不同业务场景的功能展示
const bizCode = getBizCode()

// 心愿单功能显示控制 - 仅特定业务线支持心愿单功能
// ziying: 自营商城, labor: 劳保商城, ygjd: 京东商城
const isWishShow = computed(() => {
  const bizCode = getBizCode()
  switch (bizCode) {
    case 'ziying':
    case 'labor':
    case 'ygjd':
      return true
    default:
      return false
  }
})

// 用户状态管理store实例，用于获取用户信息和地址信息
const userStore = useUserStore()
// 路由相关实例，用于页面跳转和参数获取
const router = useRouter()
const route = useRoute()

// 计算用户角色类型，用于控制特定功能的显示
// 从企业管理员信息或客户管理员信息中获取角色类型
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// ==================== 商品列表相关状态和方法 ====================
// 使用商品列表组合函数，获取商品列表相关的状态和方法
// 包含商品数据、加载状态、筛选条件、分页信息等
const {
  goodsList,              // 商品列表数据
  loading,                // 加载状态
  finished,               // 是否加载完成
  isLoading,              // 是否正在加载
  pageNo,                 // 当前页码
  pageSize,               // 每页数量
  filterCriteria,         // 筛选条件
  hasFilterConditions,    // 是否有筛选条件
  locationText,           // 位置文本
  addressInfo,            // 地址信息
  resetList,              // 重置列表方法
  processGoodsData,       // 处理商品数据方法
  applyStockFilter,       // 应用库存筛选方法
  goToDetail,             // 跳转商品详情方法
  goToCart,               // 跳转购物车方法
  addOneCart,             // 添加商品到购物车方法
  handleFilterReset,      // 处理筛选重置方法
  handleAddressChanged    // 处理地址变更方法
} = useGoodsList()

// ==================== 页面特有状态管理 ====================
// 悬浮购物车按钮的位置偏移配置
const floatingBubbleOffset = ref({ bottom: 150 })
// 搜索关键词，与搜索输入框双向绑定
const searchKeyword = ref('')
// 排序类型，如价格排序、销量排序等
const sortType = ref('sort')
// 排序顺序，升序或降序
const sortOrder = ref('')
// 是否使用瀑布流布局，false为列表布局，true为瀑布流布局
const isWaterfallLayout = ref(false)
// 商品分类ID，用于分类筛选
const categoryId = ref('')
// 筛选弹窗显示状态
const isPopupShow = ref(false)
// 地址切换弹窗显示状态
const isSwitchAddressPopupShow = ref(false)

// ==================== 头部高度动态计算 ====================
// 头部高度，用于动态计算页面内容区域的padding-top
const headerHeight = ref(0)
// 页面头部元素引用，用于获取实际高度
const pageHeaderRef = ref(null)

// 计算动态的padding-top值，确保内容不被固定头部遮挡
const dynamicPaddingTop = computed(() => {
  return headerHeight.value > 0 ? `${headerHeight.value}px` : '85px'
})

// ==================== 瀑布流布局配置 ====================
// 瀑布流布局的断点配置，用于响应式设计
const breakpoints = ref(getDefaultBreakpoints())

// ==================== 页面交互事件处理 ====================
// 切换布局模式，在列表布局和瀑布流布局之间切换
const toggleLayout = () => {
  isWaterfallLayout.value = !isWaterfallLayout.value
}

// 切换筛选弹窗显示状态
const toggleFilter = () => {
  isPopupShow.value = !isPopupShow.value
}

// 显示地址切换弹窗
const setSwitchAddressPopupShow = () => {
  isSwitchAddressPopupShow.value = true
}

// ==================== 搜索功能实现 ====================
// 防抖搜索函数，避免用户频繁输入时的重复请求
// 延迟300ms执行搜索，提升用户体验和性能
const debouncedSearch = debounce(() => {
  resetList()        // 重置列表状态
  fetchGoodsList()   // 重新获取商品列表
}, 300)

// 处理搜索事件，更新URL参数并触发搜索
const handleSearch = () => {
  // 更新URL中的搜索关键词参数，保持其他查询参数不变
  router.replace({
    path: route.path,
    query: {
      ...route.query,
      keyword: searchKeyword.value
    }
  })
  // 触发防抖搜索
  debouncedSearch()
}

// ==================== 筛选功能处理 ====================
// 处理省分筛选确认事件
// 当用户在省分筛选组件中确认选择后触发
const handleProvinceFilterConfirm = (selection) => {
  console.log('省分筛选选择:', selection)
  // 重置列表状态并重新加载数据
  resetList()
  fetchGoodsList()
}

// 处理排序变化事件
// 支持价格排序、销量排序等多种排序方式
const handleSortChange = ({ type, currentSortType, currentSortOrder }) => {
  // 如果点击的是当前已选中的排序类型
  if (currentSortType === type) {
    // 对于价格和销量排序，支持升序降序切换
    if (type === 'price' || type === 'sale') {
      sortOrder.value = currentSortOrder === 'asc' ? 'desc' : 'asc'
    }
  } else {
    // 切换到新的排序类型，重置排序顺序
    sortType.value = type
    sortOrder.value = ''
  }

  // 重置列表状态并重新加载数据
  resetList()
  fetchGoodsList()
}

// 处理筛选确认事件
// 当用户在筛选弹窗中确认筛选条件后触发
const handleFilterConfirm = () => {
  resetList()
  fetchGoodsList()
}

// ==================== 搜索状态管理 ====================
// 连续空页面计数，用于判断是否停止加载
const consecutiveEmptyPages = ref(0)
// 最大连续空页面数，超过此数量则停止加载
const maxEmptyPages = 2

// ==================== URL参数解析工具函数 ====================
// 获取URL查询参数中的省分代码
// 支持字符串和数组格式的参数处理
const getQueryProvinceCode = () => {
  const queryProvinceCode = route.query.proStr
  if (queryProvinceCode) {
    // 如果是字符串格式直接返回，如果是数组格式则用逗号连接
    return typeof queryProvinceCode === 'string' ? queryProvinceCode : queryProvinceCode.join(',')
  }
  return ''
}

// 获取URL查询参数中的供应商代码列表
// 统一处理为数组格式，支持单个和多个供应商代码
const getQuerySupplierCodeList = () => {
  const querySupplierCodeList = route.query.supplierCode
  if (querySupplierCodeList) {
    // 如果已经是数组格式，直接返回
    if (Array.isArray(querySupplierCodeList)) {
      return querySupplierCodeList
    } else if (typeof querySupplierCodeList === 'string') {
      // 如果是逗号分隔的字符串，分割成数组
      // 如果是单个字符串，包装成数组
      return querySupplierCodeList.indexOf(',') > -1
        ? querySupplierCodeList.split(',')
        : [querySupplierCodeList]
    }
  }
  return []
}

// ==================== 商品列表数据获取 ====================
// 获取商品列表的主要方法，支持分页加载和多种筛选条件
const fetchGoodsList = async () => {
  // 显示加载提示
  showLoadingToast()

  // 如果是第一页，需要初始化相关状态
  if (pageNo.value === 1) {
    // 强制查询用户默认地址，确保地址信息最新
    await userStore.queryDefaultAddr({ force: true })
    isLoading.value = true           // 设置加载状态
    goodsList.value = []             // 清空商品列表
    consecutiveEmptyPages.value = 0  // 重置连续空页面计数
    finished.value = false           // 重置加载完成状态
  }

  // 处理品牌筛选条件，提取已选中的品牌值
  const brandList = compact(
    filterCriteria.value.brandsList
      .filter(item => item.isSelected)  // 筛选已选中的品牌
      .map(item => item.value)          // 提取品牌值
  )

  // 获取测试参数，用于特殊测试场景
  const testDMX = get(route.query, 'testDMX', 'false')

  // 构建基础搜索参数对象
  let searchParams = {
    keyword: searchKeyword.value,                    // 搜索关键词
    bizCode: getBizCode('GOODS'),                   // 业务代码
    pageNumber: pageNo.value,                       // 当前页码
    pageSize: pageSize.value,                       // 每页数量
    orderType: '00',                                // 订单类型
    orderRule: sortOrder.value,                     // 排序规则
    brands: brandList,                              // 品牌筛选列表
    minPrice: filterCriteria.value.minPrice !== '' ? Number(filterCriteria.value.minPrice) : '', // 最低价格
    maxPrice: filterCriteria.value.maxPrice !== '' ? Number(filterCriteria.value.maxPrice) : '', // 最高价格
    addressJsonInfo: addressInfo.value,             // 地址信息
    testDMX                                         // 测试参数
  }

  // 针对zq业务场景的特殊处理
  if (bizCode === 'zq') {
    // 获取zq业务相关信息，包含省分和供应商筛选条件
    const zqInfo = userStore.zqInfo || {}
    searchParams = {
      ...searchParams,
      needFilter: true,  // 启用筛选功能
      // 省分代码：优先使用URL参数，其次使用用户信息中的省分代码
      provinceCode: getQueryProvinceCode() || (zqInfo.provinceCode ? zqInfo.provinceCode.join(',') : ''),
      // 供应商代码列表：优先使用URL参数，其次使用用户信息中的供应商列表
      supplierCodeList: getQuerySupplierCodeList().length > 0
        ? getQuerySupplierCodeList()
        : (zqInfo.isvList ? zqInfo.isvList.map(item => item.isvId) : [])
    }
  }

  // 调用搜索API获取商品数据
  const [err, json] = await searchKeyWord(searchParams)

  // 关闭加载提示并更新加载状态
  closeToast()
  loading.value = false
  isLoading.value = false

  // 处理API响应结果
  if (!err) {
    // 请求成功，处理返回的商品数据
    if (json && json.length > 0) {
      // 处理商品数据，包含格式化、价格计算等
      const processedList = processGoodsData(json)
      // 应用库存筛选，过滤掉无库存商品
      const filteredList = applyStockFilter(processedList)

      if (filteredList.length > 0) {
        // 有有效商品数据，重置连续空页面计数
        consecutiveEmptyPages.value = 0
        // 将新商品添加到现有列表中
        goodsList.value = goodsList.value.concat(filteredList)
        // 页码递增，准备加载下一页
        pageNo.value++
      } else {
        // 当前页面没有有效商品，增加连续空页面计数
        consecutiveEmptyPages.value++
        // 如果连续空页面数达到上限，停止加载
        if (consecutiveEmptyPages.value >= maxEmptyPages) {
          finished.value = true
          return
        }
        // 继续尝试加载下一页
        pageNo.value++
        await nextTick(() => onLoad())
      }
    } else {
      // 返回数据为空，增加连续空页面计数
      consecutiveEmptyPages.value++
      // 如果连续空页面数达到上限，停止加载
      if (consecutiveEmptyPages.value >= maxEmptyPages) {
        finished.value = true
        return
      }
      // 继续尝试加载下一页
      pageNo.value++
      await nextTick(() => onLoad())
    }
  } else {
    // 请求失败，记录错误并显示提示
    // console.error('获取商品列表失败:', err.msg)
    showToast({ message: err.msg })
    // 增加连续空页面计数
    consecutiveEmptyPages.value++
    // 如果连续空页面数达到上限，停止加载
    if (consecutiveEmptyPages.value >= maxEmptyPages) {
      finished.value = true
    }
  }
}

// ==================== 分页加载处理 ====================
// 处理无限滚动加载更多商品
const onLoad = () => {
  // 只有在未加载完成时才继续加载
  if (!finished.value) {
    fetchGoodsList()
  }
}

// ==================== 头部高度计算 ====================
// 动态计算页面头部高度，确保内容区域正确显示
const calculateHeaderHeight = () => {
  nextTick(() => {
    if (pageHeaderRef.value) {
      // 优先使用实际DOM元素的高度
      headerHeight.value = pageHeaderRef.value.offsetHeight
    } else {
      // 备用计算方式：根据组件组合估算总高度
      let totalHeight = 0

      // 省分筛选组件高度（仅在特定角色下显示）
      if (roleType.value === '4') {
        totalHeight += 44 // ProvinceFilter组件的预估高度
      }

      // 搜索头部组件高度
      totalHeight += 44 // SearchHeader组件的预估高度

      // 排序筛选栏高度（仅在非zq业务场景下显示）
      if (bizCode !== 'zq') {
        totalHeight += 44 // SortFilterBar组件的预估高度
      }

      headerHeight.value = totalHeight
    }
  })
}

// ==================== 响应式监听器 ====================
// 监听业务代码变化，重新计算头部高度
// 使用flush: 'post'确保在DOM更新后执行
watch(() => bizCode, () => {
  calculateHeaderHeight()
}, { flush: 'post' })

// ===================== 心愿单 =======================
const handleEmptyButtonClick
  = () => {
    router.push('/user/wish')
  }


// ==================== 生命周期钩子 ====================
// 组件挂载时的初始化操作
onMounted(async () => {
  // 如果URL中包含搜索关键词，初始化搜索输入框
  if (route.query.keyword) {
    searchKeyword.value = route.query.keyword
  }

  // 强制查询用户默认地址，确保地址信息最新
  await userStore.queryDefaultAddr({ force: true })

  // 计算并设置头部高度
  calculateHeaderHeight()

  // 加载商品列表数据
  fetchGoodsList()
})
</script>

<style scoped lang="less">
.search-list-page {
  padding-top: v-bind(dynamicPaddingTop);

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .layout-toggle {
      margin-left: 12px;
      padding: 4px;
      background: none;
      border: none;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .goods-content {
    padding: 0 10px;
  }
}
</style>
