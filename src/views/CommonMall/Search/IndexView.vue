<!--
==================== 商品搜索首页组件 ====================
主要功能：
1. 提供商品搜索入口，支持关键词输入和搜索
2. 展示用户搜索历史记录，支持快速选择历史关键词
3. 支持清空所有搜索历史记录
4. 自动聚焦搜索框，提升用户体验
5. 搜索后跳转到搜索结果列表页面

技术特性：
- 使用防抖和节流优化搜索性能
- 支持无障碍访问（ARIA标签）
- 响应式设计，适配移动端
- 异步加载历史记录，不阻塞页面渲染
- 使用CSS contain优化渲染性能

使用场景：
- 商城首页搜索入口
- 商品分类页面搜索
- 用户主动搜索商品时的入口页面
==================== 商品搜索首页组件 ====================
-->

<template>
  <!-- 搜索页面主容器 -->
  <main class="search">
    <!-- 搜索头部组件，包含搜索框和搜索按钮 -->
    <SearchHeader
      ref="searchHeaderRef"
      v-model="searchKeyword"
      placeholder="搜索商品"
      @search="handleSearch"
    />

    <!-- 搜索历史记录区域，仅在有历史记录时显示 -->
    <section
      v-if="historyRecords.length > 0"
      class="search__history"
      aria-label="搜索历史"
    >
      <!-- 历史记录标题栏，包含标题和清空按钮 -->
      <div class="search__history-header">
        <h2 class="search__history-title">搜索历史</h2>
        <!-- 清空所有历史记录按钮 -->
        <button
          type="button"
          class="search__clear-btn"
          @click="handleClearAllHistory"
          aria-label="清空搜索历史"
        >
          <img
            src="../../../static/images/delete.png"
            alt=""
            class="search__clear-icon"
            loading="lazy"
            width="16"
            height="16"
          />
        </button>
      </div>

      <!-- 历史记录关键词列表 -->
      <div class="search__history-list">
        <!-- 历史记录关键词按钮，点击可快速搜索 -->
        <button
          v-for="(keyword, index) in historyRecords"
          :key="`history-${index}-${keyword}`"
          type="button"
          class="search__history-keyword"
          @click="handleUseHistoryKeyword(keyword)"
          :aria-label="`搜索 ${keyword}`"
        >
          {{ keyword }}
        </button>
      </div>
    </section>
  </main>
</template>
<script setup>
import SearchHeader from '@components/Common/SearchHeader.vue'
import { onMounted, ref, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { delHistoryRecord, getHistoryRecords } from '@api/interface/search.js'
import { debounce, throttle } from 'es-toolkit'
import { get } from 'es-toolkit/compat'
import { closeToast, showLoadingToast } from 'vant'

// ==================== 路由实例 ====================
const route = useRoute() // 当前路由信息，用于获取查询参数
const router = useRouter() // 路由实例，用于页面跳转

// ==================== 响应式数据 ====================
const searchKeyword = ref('') // 当前搜索关键词
const historyRecords = ref([]) // 搜索历史记录列表
const searchHeaderRef = ref(null) // 搜索头部组件引用，用于聚焦输入框
const isLoading = ref(false) // 加载状态，防止重复请求

// ==================== 搜索历史记录管理 ====================
// 获取搜索历史记录，使用节流防止频繁请求
const fetchHistoryRecords = throttle(async () => {
  // 防止重复请求
  if (isLoading.value) return

  try {
    isLoading.value = true
    showLoadingToast() // 显示加载提示
    const [err, data] = await getHistoryRecords() // 调用API获取历史记录
    closeToast() // 关闭加载提示
    // 请求成功且有数据时更新历史记录
    if (!err && data) {
      historyRecords.value = data
    }
  } catch (error) {
    // 静默处理错误，避免影响用户体验
  } finally {
    isLoading.value = false
  }
}, 500) // 500ms节流间隔

// 使用历史记录关键词进行搜索，使用节流防止快速点击
const handleUseHistoryKeyword = throttle((keyword) => {
  // 验证关键词有效性
  if (!keyword?.trim()) return
  // 设置搜索关键词并触发搜索
  searchKeyword.value = keyword
  handleSearch()
}, 200) // 200ms节流间隔



// 清空所有搜索历史记录，使用防抖防止误操作
const handleClearAllHistory = debounce(async () => {
  // 防止重复请求
  if (isLoading.value) return

  try {
    isLoading.value = true
    showLoadingToast() // 显示加载提示
    // 调用API删除所有历史记录
    const [err] = await delHistoryRecord({
      type: 'ALL'
    })
    closeToast() // 关闭加载提示
    // 删除成功时清空本地历史记录数组
    if (!err) {
      historyRecords.value = []
    }
  } catch (error) {
    // 静默处理错误，避免影响用户体验
  } finally {
    isLoading.value = false
  }
}, 300) // 300ms防抖延迟

// ==================== 搜索功能处理 ====================
// 处理搜索操作，使用防抖优化性能
const handleSearch = debounce(() => {
  // 获取并验证搜索关键词
  const keyword = searchKeyword.value?.trim()
  if (!keyword) return

  // 获取测试参数，用于特殊环境标识
  const testDMX = get(route.query, 'testDMX', false)

  // 跳转到搜索结果列表页面，携带搜索关键词和测试参数
  router.push({
    path: '/search/list',
    query: {
      keyword,
      testDMX
    }
  })

  // 搜索后异步刷新历史记录，不阻塞页面跳转
  nextTick(() => {
    fetchHistoryRecords()
  })
}, 300) // 300ms防抖延迟

// ==================== 组件初始化 ====================
// 组件初始化函数，并行执行多个初始化任务
const initializeComponent = async () => {
  // 并行执行初始化任务，提升加载性能
  await Promise.all([
    fetchHistoryRecords(), // 获取搜索历史记录
    nextTick(() => {
      // 确保DOM渲染完成后聚焦搜索框，提升用户体验
      searchHeaderRef.value?.inputRef?.focus()
    })
  ])
}

// ==================== 生命周期钩子 ====================
// 组件挂载后执行初始化
onMounted(async () => {
  await initializeComponent()
})
</script>
<style scoped lang="less">
.search {
  min-height: 100vh;
  background-color: #FFFFFF;
  transform: translateZ(0);
  will-change: scroll-position;
  contain: layout style paint;
}

.search__history {
  padding: (10px);
  contain: layout style;
}

.search__history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  contain: layout;
}

.search__history-title {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #171E24;
  line-height: 1.2;
}

.search__clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  background: none;
  border: none;
  color: #718096;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  min-width: 25px;
  min-height: 25px;

  &:hover {
    background-color: #F8F9FA;
  }

  &:active {
    opacity: 0.7;
  }
}

.search__clear-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  image-rendering: -webkit-optimize-contrast;
}

.search__history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  contain: layout;
}

.search__history-keyword {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #F8F9FA;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  color: #4A5568;
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 200px;
  min-height: 32px;
  overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;

  // 性能优化：使用transform代替其他属性变化
  &:hover {
    background-color: darken(#F8F9FA, 5%);
    color: #171E24;
  }

  &:active {
    transform: scale(0.98);
    background-color: darken(#F8F9FA, 8%);
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2) {
  .search__clear-icon {
    image-rendering: -webkit-optimize-contrast;
  }
}

// 减少动画的用户偏好支持
@media (prefers-reduced-motion: reduce) {
  .search__clear-btn,
  .search__history-keyword {
    transition: none;
  }

  .search__history-keyword:active {
    transform: none;
  }
}
</style>
