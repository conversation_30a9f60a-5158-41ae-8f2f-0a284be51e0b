<!--
/**
 * 购物车主页面组件
 *
 * 主要功能：
 * 1. 提供完整的购物车管理功能，包括商品查看、编辑、删除、数量调整等操作
 * 2. 支持用户登录状态检测，未登录时引导用户登录
 * 3. 集成收货地址管理，支持地址选择和验证功能
 * 4. 实现商品分组展示，区分有效商品和失效商品
 * 5. 提供编辑模式，支持批量选择和删除商品
 * 6. 集成赠品展示功能，支持查看商品赠品详情
 * 7. 支持商品结算流程，包括订单验证和跳转
 * 8. 提供相似商品推荐功能
 *
 * 技术特点：
 * - 使用Pinia状态管理，实现购物车数据的统一管理
 * - 采用防抖技术优化用户操作体验，避免频繁请求
 * - 实现骨架屏加载效果，提升用户体验
 * - 支持长按和滑动手势操作
 * - 集成地址验证和订单SKU验证
 *
 * 使用场景：
 * - 用户查看和管理购物车商品
 * - 商品数量调整和删除操作
 * - 购物车商品结算和下单
 */
-->

<template>
  <MainLayout>
    <!-- ===================== 初始加载状态区域 ===================== -->
    <!-- 页面首次加载时显示骨架屏，提供良好的加载体验 -->
    <section v-if="isInitialLoading" class="cart">
      <CartLoadingSkeleton />
    </section>

    <!-- ===================== 未登录状态区域 ===================== -->
    <!-- 用户未登录时显示登录引导页面 -->
    <CartEmptyState v-else-if="!isLogin" type="login" @login="handleLogin" />

    <!-- ===================== 已登录主要内容区域 ===================== -->
    <!-- 用户已登录时显示完整的购物车功能界面 -->
    <section v-else class="cart">
      <!-- 购物车头部区域，包含收货地址选择和编辑模式切换 -->
      <CartHeader
        :address-display="addressDisplay"
        :is-edit-mode="isEditMode"
        @select-address="handleSelectAddress"
        @toggle-edit="handleEditToggle"
      />

      <!-- 购物车主要内容区域 -->
      <main class="cart-main">
        <!-- 数据加载状态，显示不包含头部的骨架屏 -->
        <CartLoadingSkeleton v-if="isLoading" :show-header="false" />

        <!-- 空购物车状态，引导用户去购物 -->
        <CartEmptyState v-else-if="isEmptyState" type="empty" @go-shopping="$router.push('/home')" />

        <!-- 购物车商品列表区域 -->
        <section v-else class="cart-goods">
          <!-- 有效商品列表组件，支持选择、编辑、数量调整等操作 -->
          <ValidGoodsList
            :valid-goods-list="validGoodsList"
            :is-edit-mode="isEditMode"
            :temp-selected-items="tempSelectedItems"
            @toggle-group-select="handleToggleGroupSelect"
            @toggle-item-select="handleToggleItemSelect"
            @show-stepper="showStepper"
            @quantity-change="handleQuantityChange"
            @look-similar="handleLookSimilar"
            @delete-item="handleDeleteItem"
            @close-menu="closeLongPressMenu"
            @swipe-open="handleSwipeOpen"
            @swipe-close="handleSwipeClose"
            @set-ref="setGoodsItemRef"
            @long-press="handleLongPress"
            @gift-click="handleGiftClick"
            @content-click="handleContentClick"
          />

          <!-- 失效商品列表组件，显示无法购买的商品 -->
          <InvalidGoodsList
            :has-invalid-goods="hasInvalidGoods"
            :invalid-goods-list="invalidGoodsList"
            :invalid-goods-count="invalidGoodsCount"
            @clear-invalid-goods="handleClearInvalidGoods"
            @toggle-item-select="handleToggleItemSelect"
            @look-similar="handleLookSimilar"
          />
        </section>
      </main>

      <!-- 底部操作栏占位符，为固定定位的底部栏预留空间 -->
      <WoActionBarPlaceholder />
    </section>

    <!-- ===================== 底部固定操作栏 ===================== -->
    <!-- 购物车底部结算栏，包含全选、价格显示、结算按钮等 -->
    <WoActionBar :bottom="49" v-if="showFooter">
      <CartFooter
        :is-edit-mode="isEditMode"
        :edit-selected-count="tempSelectedItems.size"
        :is-edit-all-selected="isEditModeAllSelected"
        :selected-count="cartStore.selectCountAll"
        :total-price="cartStore.selectTotalPrice"
        :is-all-selected="cartStore.isSelectAll"
        :has-selected-goods="cartStore.hasSelectedGoods"
        @toggle-all-select="handleToggleAllSelect"
        @edit-toggle-all="handleEditModeToggleAll"
        @edit-delete="handleEditModeDelete"
        @checkout="handleCheckout"
      />
    </WoActionBar>
  </MainLayout>

  <!-- ===================== 弹窗组件区域 ===================== -->
  <!-- 赠品展示弹窗，显示商品的赠品详情信息 -->
  <GiftDisplayPopup v-model:visible="giftPopupVisible" :gift-list="giftList" :goods-num="currentGoodsNum" />
  <!-- 地址快速选择弹窗，支持用户快速切换收货地址 -->
  <AddressQuickSelectionPopup v-model:visible="addressPopupVisible" @select="handleAddressSelect" />
</template>

<script setup>
import { ref, computed, onMounted, shallowRef, markRaw, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showLoadingToast, closeToast, showToast } from 'vant'
import { isEmpty, get } from 'es-toolkit/compat'
import { useNewCartStore, CART_QUERY_STATUS } from '@store/modules/newCart.js'
import { useUserStore } from '@store/modules/user.js'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import GiftDisplayPopup from '@components/Common/GiftDisplayPopup.vue'
import AddressQuickSelectionPopup from '@components/Common/Address/AddressQuickSelectionPopup.vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import CartHeader from './components/CartHeader.vue'
import CartFooter from './components/CartFooter.vue'
import CartLoadingSkeleton from './components/CartLoadingSkeleton.vue'
import CartEmptyState from './components/CartEmptyState.vue'
import ValidGoodsList from './components/ValidGoodsList.vue'
import InvalidGoodsList from './components/InvalidGoodsList.vue'
import { getBizCode } from '@utils/curEnv.js'
import { useAlert } from '@/composables/index.js'
import { similarity, getGiftDetails, checkOrderSku, jdAddressCheck } from '@api/index.js'
import { BatchRequest } from '@utils/tools.js'
import { buyProductCart, buyProductCartSession } from '@utils/storage.js'

// ==================== 工具函数定义 ====================
// 防抖函数实现，用于优化用户操作频率，避免过度请求
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// ==================== 状态管理和路由实例 ====================
// 获取弹窗提示工具实例，用于显示确认对话框
const $alert = useAlert()
// 获取路由实例，用于页面跳转
const router = useRouter()
// 获取购物车状态管理实例，管理购物车数据和操作
const cartStore = useNewCartStore()
// 获取用户状态管理实例，管理用户登录状态和地址信息
const userStore = useUserStore()

// ==================== 弹窗状态管理 ====================
// 赠品展示弹窗的显示状态控制
const giftPopupVisible = ref(false)
// 地址选择弹窗的显示状态控制
const addressPopupVisible = ref(false)

// ==================== 赠品功能相关状态 ====================
// 当前查看的商品赠品列表数据
const giftList = shallowRef([])
// 当前商品的数量，用于赠品计算
const currentGoodsNum = ref(0)
// 赠品详情缓存Map，避免重复请求相同商品的赠品信息
const cacheSkuGiftDetailsList = markRaw(new Map())

// ==================== 编辑模式状态管理 ====================
// 购物车编辑模式开关，控制是否显示编辑相关功能
const isEditMode = ref(false)
// 编辑模式下临时选中的商品ID集合，格式为"goodsId_skuId"
const tempSelectedItems = shallowRef(new Set())

// ==================== 商品交互状态管理 ====================
// 当前长按操作的商品项引用
const longPressedItem = shallowRef(null)
// 商品组件引用集合，用于直接操作商品组件
const goodsItemRefs = shallowRef({})

// ==================== 页面加载状态管理 ====================
// 页面初始加载状态，控制首次进入时的骨架屏显示
const isInitialLoading = ref(true)
// 首次渲染标记，用于性能优化
const isFirstRender = ref(true)


// ==================== 用户状态计算属性 ====================
// 用户登录状态，从用户store中获取
const isLogin = computed(() => userStore.isLogin)

// 收货地址显示文本，优先显示详细地址，否则拼接省市区名称
const addressDisplay = computed(() => {
  const addr = userStore.curAddressInfo
  if (!addr) return '请选择收货地址'
  return addr.addrDetail || `${addr.provinceName}${addr.cityName}${addr.countyName}`
})

// ==================== 页面状态计算属性 ====================
// 购物车数据加载状态，用于显示加载中的骨架屏
const isLoading = computed(() => cartStore.getCartLoadingStatus === CART_QUERY_STATUS.LOADING)

// 空购物车状态，当没有有效商品和失效商品且加载成功时为true
const isEmptyState = computed(() =>
  !cartStore.hasValidGoods &&
  !cartStore.hasInvalidGoods &&
  cartStore.getCartLoadingStatus === CART_QUERY_STATUS.SUCCESS
)

// 底部操作栏显示状态，只有在有有效商品且加载成功时才显示
const showFooter = computed(() =>
  cartStore.getCartLoadingStatus === CART_QUERY_STATUS.SUCCESS && cartStore.hasValidGoods
)

// ==================== 商品列表计算属性 ====================
// 有效商品列表，从购物车store中获取可购买的商品
const validGoodsList = computed(() => cartStore.getCartValidList)

// 失效商品列表，获取第一组失效商品的商品列表
const invalidGoodsList = computed(() => cartStore.getCartInvalidList[0]?.goodsList || [])

// 是否存在失效商品的标识
const hasInvalidGoods = computed(() => cartStore.hasInvalidGoods)

// 失效商品总数量
const invalidGoodsCount = computed(() => invalidGoodsList.value.length)

// ==================== 编辑模式计算属性 ====================
// 有效商品总数量，用于编辑模式的全选判断
const totalValidItems = computed(() =>
  validGoodsList.value.reduce((total, group) => total + group.goodsList.length, 0)
)

// 编辑模式下是否全选状态，当临时选中数量等于总商品数量时为true
const isEditModeAllSelected = computed(() =>
  totalValidItems.value > 0 && tempSelectedItems.value.size === totalValidItems.value
)

// ==================== 地址验证功能 ====================
// 检查收货地址是否符合京东配送要求
const addressCheck = async () => {
  // 调用京东地址验证接口
  const [err, json] = await jdAddressCheck()

  // 接口调用失败时显示错误信息并返回false
  if (err) {
    showToast(err.msg)
    return false
  }

  // 地址验证失败时提示用户修改地址
  if (!json) {
    $alert({
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存',
      confirmButtonText: '修改地址',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: () => {
        // 跳转到地址编辑页面，传递当前地址ID和无效标识
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      },
      onCancelCallback: () => { }
    })
    return false
  }

  // 地址验证通过
  return true
}

// ==================== 商品属性初始化 ====================
// 为商品列表中的每个商品初始化UI交互相关的属性
const initializeGoodsProperties = () => {
  validGoodsList.value.forEach(group => {
    group.goodsList.forEach(item => {
      // 为每个商品添加UI交互状态属性，使用空值合并运算符设置默认值
      Object.assign(item, {
        stepperVisible: get(item, 'stepperVisible', false),     // 数量步进器显示状态
        isSwipeOpen: get(item, 'isSwipeOpen', false),           // 滑动删除菜单打开状态
        showLongPressMenu: get(item, 'showLongPressMenu', false) // 长按操作菜单显示状态
      })
    })
  })
}

// ==================== 购物车初始化 ====================
// 页面加载时的购物车初始化流程
const initializeCart = async () => {
  try {
    // 首先检查用户登录状态
    await userStore.queryLoginStatus()

    // 无论登录状态如何，都结束初始加载状态，避免无限加载
    isInitialLoading.value = false

    // 用户已登录时执行购物车数据加载
    if (userStore.isLogin) {
      showLoadingToast()

      // 验证收货地址有效性
      await addressCheck()
      // 查询购物车数据
      await cartStore.query()
      // 初始化商品UI属性
      initializeGoodsProperties()

      closeToast()
    }
  } catch (error) {
    closeToast()
    // 确保即使出错也结束初始加载状态，避免页面卡死
    isInitialLoading.value = false
  }
}

// ==================== 防抖处理函数 ====================
// 地址选择后的防抖处理，避免用户快速切换地址时的频繁请求
const debouncedAddressSelect = debounce(async () => {
  try {
    // 地址选择后直接更新购物车数据，不需要重新显示初始加载状态
    if (userStore.isLogin) {
      showLoadingToast()
      // 验证新地址的有效性
      await addressCheck()
      // 重新查询购物车数据，因为地址变更可能影响商品可购买性
      await cartStore.query()
      // 重新初始化商品UI属性
      initializeGoodsProperties()
      closeToast()
    }
  } catch (error) {
    closeToast()
    showToast('更新地址失败')
  }
}, 300)

// 全选操作的防抖处理，避免用户快速点击全选按钮
const debouncedToggleAllSelect = debounce(async () => {
  try {
    showLoadingToast()
    // 调用购物车store的全选方法
    await cartStore.checkedAllValid()
    closeToast()
  } catch (error) {
    closeToast()
    showToast('全选操作失败')
  }
}, 300)

// 商品数量变更的防抖处理，避免用户快速调整数量时的频繁请求
const debouncedQuantityChange = debounce(async (item) => {
  try {
    showLoadingToast()

    // 调用购物车store更新商品数量
    const error = await cartStore.updateGoodsNum({
      goodsId: item.cartGoodsId,
      skuId: item.cartSkuId,
      goodsNum: item.skuNum
    })

    closeToast()

    // 处理更新失败的情况，特别是库存不足
    if (error) {
      // 库存不足错误的特殊处理
      if (error.code === 'FE2001' && error.stock !== undefined) {
        // 使用nextTick确保DOM更新完成后再修改数据
        await nextTick()

        // 在商品列表中找到对应商品并更新为实际库存数量
        const targetItem = validGoodsList.value
          .flatMap(group => group.goodsList)
          .find(goods => goods.cartGoodsId === item.cartGoodsId && goods.cartSkuId === item.cartSkuId)

        if (targetItem) {
          targetItem.skuNum = error.stock
        }
      }
      showToast(error.msg || '更新商品数量失败')
    }
  } catch (error) {
    closeToast()
    showToast('更新商品数量失败')
  }
}, 300)

// ==================== 用户操作处理函数 ====================
// 用户登录处理，调用用户store的登录方法
const handleLogin = () => userStore.login()

// 显示地址选择弹窗
const handleSelectAddress = () => {
  addressPopupVisible.value = true
}

// 地址选择完成后的处理，使用防抖避免频繁更新
const handleAddressSelect = () => debouncedAddressSelect()

// 切换编辑模式状态
const handleEditToggle = () => {
  // 退出编辑模式时清空临时选中的商品
  if (isEditMode.value) {
    tempSelectedItems.value.clear()
  }
  isEditMode.value = !isEditMode.value
}

// ==================== 商品选择操作处理 ====================
// 处理单个商品的选中状态切换
const handleToggleItemSelect = async (item) => {
  if (isEditMode.value) {
    // 编辑模式下只更新临时选中状态，不调用接口
    handleEditModeSelect(item)
  } else {
    // 普通模式下更新购物车后端选中状态
    try {
      const newSelectState = item.selected !== 'true'
      await cartStore.updateGoodsSelectMuti([{
        goodsId: item.cartGoodsId,
        skuId: item.cartSkuId,
        select: newSelectState
      }])
    } catch (error) {
      showToast('更新商品选中状态失败')
    }
  }
}

// 处理商品分组的选择/取消选择操作（仅政企商城显示，编辑/普通模式均支持）
const handleToggleGroupSelect = async (group) => {
  // 参数校验，确保分组数据有效
  if (!group || !Array.isArray(group.goodsList) || group.goodsList.length === 0) return

  if (isEditMode.value) {
    // 编辑模式：切换临时选中集合
    const allSelected = group.goodsList.every(it =>
      tempSelectedItems.value.has(`${it.cartGoodsId}_${it.cartSkuId}`)
    )

    if (allSelected) {
      // 当前分组全选状态，执行取消全选
      group.goodsList.forEach(it =>
        tempSelectedItems.value.delete(`${it.cartGoodsId}_${it.cartSkuId}`)
      )
    } else {
      // 当前分组非全选状态，执行全选
      group.goodsList.forEach(it =>
        tempSelectedItems.value.add(`${it.cartGoodsId}_${it.cartSkuId}`)
      )
    }
    return
  }

  // 普通模式：批量更新后端选中状态
  try {
    const total = group.goodsList.length
    const selectedCount = group.goodsList.reduce((sum, it) =>
      sum + (it.selected === 'true' ? 1 : 0), 0
    )
    // 未全选则全选；已全选则全不选
    const targetSelect = selectedCount !== total

    const payload = group.goodsList.map(it => ({
      goodsId: it.cartGoodsId,
      skuId: it.cartSkuId,
      select: targetSelect
    }))

    await cartStore.updateGoodsSelectMuti(payload)
  } catch (error) {
    showToast('分组选择切换失败')
  }
}


// 处理全选切换操作，使用防抖避免频繁请求
const handleToggleAllSelect = () => debouncedToggleAllSelect()

// ==================== 商品交互操作处理 ====================
// 显示商品数量步进器
const showStepper = (item) => {
  item.stepperVisible = true
}

// 处理商品数量变更，使用防抖优化用户体验
const handleQuantityChange = (item) => debouncedQuantityChange(item)

// 删除单个商品
const handleDeleteItem = async (item) => {
  try {
    await cartStore.removeMuti(item)
  } catch (error) {
    showToast('删除商品失败')
  }
}

// 一键清空所有失效商品
const handleClearInvalidGoods = async () => {
  try {
    // 构建删除商品列表，包含所有失效商品的ID信息
    const deleteGoodsList = invalidGoodsList.value.map(item => ({
      goodsId: item.cartGoodsId,
      skuId: item.cartSkuId
    }))

    await cartStore.removeInvalidGoods({ deleteGoodsList })
  } catch (error) {
    showToast('清空失效商品失败')
  }
}

// ==================== 商品手势操作处理 ====================
// 处理商品滑动菜单打开
const handleSwipeOpen = (item) => {
  item.isSwipeOpen = true
}

// 处理商品滑动菜单关闭
const handleSwipeClose = (item) => {
  item.isSwipeOpen = false
}

// 处理商品长按事件，显示长按操作菜单
const handleLongPress = (item) => {
  // 关闭其他商品的长按菜单，确保同时只有一个菜单显示
  validGoodsList.value.forEach(group => {
    group.goodsList.forEach(good => {
      if (good !== item) {
        good.showLongPressMenu = false
      }
    })
  })

  // 显示当前商品的长按菜单
  longPressedItem.value = item
  item.showLongPressMenu = true
}

// 关闭长按菜单
const closeLongPressMenu = () => {
  if (longPressedItem.value) {
    longPressedItem.value.showLongPressMenu = false
    longPressedItem.value = null
  }
}

// 设置商品组件引用，用于直接操作商品组件
const setGoodsItemRef = (el, key) => {
  goodsItemRefs.value[key] = el
}

// ==================== 编辑模式操作处理 ====================
// 处理编辑模式下的单个商品选择状态切换
const handleEditModeSelect = (item) => {
  // 构建商品唯一标识，格式为"goodsId_skuId"
  const itemId = `${item.cartGoodsId}_${item.cartSkuId}`

  // 切换选中状态：已选中则移除，未选中则添加
  if (tempSelectedItems.value.has(itemId)) {
    tempSelectedItems.value.delete(itemId)
  } else {
    tempSelectedItems.value.add(itemId)
  }
}

// 编辑模式下的全选/取消全选操作
const handleEditModeToggleAll = () => {
  if (tempSelectedItems.value.size === 0) {
    // 当前无选中商品，执行全选操作
    validGoodsList.value.forEach(group => {
      group.goodsList.forEach(item => {
        const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
        tempSelectedItems.value.add(itemId)
      })
    })
  } else {
    // 当前有选中商品，执行取消全选操作
    tempSelectedItems.value.clear()
  }
}

// 编辑模式下的批量删除操作
const handleEditModeDelete = async () => {
  // 检查是否有选中的商品
  if (tempSelectedItems.value.size === 0) return

  try {
    // 显示确认删除对话框
    $alert({
      title: '确认删除',
      message: `确定要删除选中的 ${tempSelectedItems.value.size} 件商品吗？`,
      showCancelButton: true,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        try {
          // 构建删除商品列表，将商品ID字符串拆分为goodsId和skuId
          const deleteGoodsList = Array.from(tempSelectedItems.value).map(itemId => {
            const [goodsId, skuId] = itemId.split('_')
            return { goodsId, skuId }
          })

          // 执行批量删除操作
          await cartStore.removeMuti(deleteGoodsList)

          // 删除成功后退出编辑模式并清空选中状态
          isEditMode.value = false
          tempSelectedItems.value.clear()
        } catch (error) {
          showToast('删除商品失败')
        }
      }
    })
  } catch (error) {
    showToast('操作失败')
  }
}



// ==================== 赠品功能处理 ====================
// 获取商品赠品详情信息，支持缓存机制避免重复请求
const getGiftsDetails = async (goodsInfo, giftList) => {
  // 从商品信息中提取SKU ID和供应商代码
  const skuId = goodsInfo.goods?.skuList?.[0]?.skuId
  const supplierCode = goodsInfo.goods?.supplierCode || 'jd_yg'

  // 参数校验：SKU ID和赠品列表都必须有效
  if (!skuId || isEmpty(giftList)) return []

  // 检查缓存：如果已有该SKU的赠品详情，直接返回缓存结果
  if (cacheSkuGiftDetailsList.has(skuId)) {
    return cacheSkuGiftDetailsList.get(skuId)
  }

  // 使用批量请求工具并发获取所有赠品详情，提升性能
  const batchRequest = new BatchRequest()
  const tempGiftDetailsList = []

  // 为每个赠品创建详情请求
  giftList.forEach((item, index) => {
    const promise = getGiftDetails({
      supplierSkuId: item.giftId,
      supplierCode
    }).then(([err, json]) => {
      // 构建赠品详情对象，合并原始赠品信息和详情数据
      const giftDetail = err ? {} : {
        ...json,
        giftType: item.giftType,
        giftNum: item.giftNum,
        belongToSkuMaxNum: item.belongToSkuMaxNum,
        belongToSkuMinNum: item.belongToSkuMinNum
      }
      // 使用splice确保结果顺序与原始列表一致
      tempGiftDetailsList.splice(index, 0, giftDetail)
    })

    batchRequest.push(promise)
  })

  // 返回Promise，等待所有赠品详情请求完成
  return new Promise((resolve) => {
    batchRequest.onComplete = () => {
      // 缓存结果以供后续使用
      cacheSkuGiftDetailsList.set(skuId, tempGiftDetailsList)
      resolve(tempGiftDetailsList)
    }
  })
}

// 处理商品赠品图标点击事件，显示赠品详情弹窗
const handleGiftClick = async (data) => {
  const { goods: goodsInfo, giftList: goodsInfoGiftList } = data

  try {
    showLoadingToast()

    // 设置当前商品数量，用于赠品数量计算
    currentGoodsNum.value = goodsInfo.skuNum || 0

    // 获取赠品详情列表
    const giftDetails = await getGiftsDetails(goodsInfo, goodsInfoGiftList)
    // 过滤掉空的赠品详情
    giftList.value = giftDetails.filter(item => !isEmpty(item))

    closeToast()
    // 显示赠品详情弹窗
    giftPopupVisible.value = true
  } catch (error) {
    closeToast()
    // 即使获取赠品详情失败也显示弹窗，避免用户点击无响应
    giftList.value = []
    currentGoodsNum.value = goodsInfo.skuNum || 0
    giftPopupVisible.value = true
  }
}

// ==================== 商品导航功能处理 ====================
// 查看相似商品功能，为用户提供商品推荐
const handleLookSimilar = async (item) => {
  try {
    showLoadingToast()

    // 兼容不同的商品ID字段，支持字符串和对象两种参数格式
    const goodsId = typeof item === 'string' ? item : item.cartGoodsId || item.goodsId

    // 调用相似商品接口获取推荐商品列表ID
    const [err, json] = await similarity({
      goodsId,
      bizCode: getBizCode('GOODS')
    })

    closeToast()

    if (!err && json) {
      // 接口调用成功，跳转到相似商品列表页
      router.push(`/goodslist/${json}`)
    } else {
      // 接口调用失败，跳转到商品分类页作为降级方案
      router.push({ path: '/category' })
    }
  } catch (error) {
    closeToast()
    // 异常情况下跳转到分类页，确保用户有地方可去
    router.push({ path: '/category' })
  }
}

// 处理商品内容区域点击事件，跳转到商品详情页
const handleContentClick = (data) => {
  const { goodsId, skuId } = data
  // 跳转到商品详情页，传递商品ID和SKU ID
  router.push(`/goodsdetail/${goodsId}/${skuId}`)
}

// ==================== 结算流程处理 ====================
// 处理购物车结算流程，包括商品验证、地址验证、订单创建等
const handleCheckout = async () => {
  // 第一步：检查是否有选中的商品
  if (cartStore.selectCountAll < 1) {
    showToast('请选择下单商品')
    return
  }

  // 第二步：验证收货地址有效性
  const isPassed = await addressCheck()
  if (!isPassed) {
    return
  }

  try {
    // 第三步：构建购买商品列表，提取选中商品的关键信息
    const buyGoodsList = validGoodsList.value
      .flatMap(group => group.goodsList)
      .filter(item => item.selected === 'true')
      .map(item => ({
        cartGoodsId: item.cartGoodsId,
        cartSkuId: item.cartSkuId,
        skuNum: item.skuNum,
        supplierCode: item.supplierCode,
        nowPrice: item.nowPrice
      }))

    // 第四步：二次检查商品列表，防止异步操作导致的数据不一致
    if (isEmpty(buyGoodsList)) {
      showToast('请选择下单商品')
      return
    }

    // 第五步：保存购买商品到本地存储，供订单确认页使用
    buyProductCart.set(buyGoodsList)
    buyProductCartSession.set(buyGoodsList)

    // 第六步：检查收货地址信息完整性
    const info = userStore.curAddressInfo
    if (!info) {
      showToast('请先选择收货地址')
      return
    }

    // 第七步：构建地址信息对象，包含完整的省市区街道信息
    const addressInfo = JSON.stringify({
      provinceId: info.provinceId,
      provinceName: info.provinceName,
      cityId: info.cityId,
      cityName: info.cityName,
      countyId: info.countyId,
      countyName: info.countyName,
      townId: info.townId,
      townName: info.townName
    })

    // 第八步：执行订单SKU验证，确保商品可购买性
    showLoadingToast()
    const [err] = await checkOrderSku({
      bizCode: getBizCode('ORDER'),
      addressInfo,
      buyGoodsList: JSON.stringify(buyGoodsList)
    })
    closeToast()

    if (!err) {
      // 验证成功，跳转到订单确认页面
      router.push('/orderconfirm')
    } else {
      // 验证失败，显示错误信息并刷新购物车数据
      showToast(err.msg || '订单验证失败')

      // 延迟刷新购物车，避免用户操作过于频繁
      setTimeout(async () => {
        try {
          showLoadingToast()
          await cartStore.query()
          closeToast()
        } catch (refreshError) {
          closeToast()
          showToast('刷新购物车失败')
        }
      }, 1500)
    }
  } catch (error) {
    closeToast()
    showToast('结算失败，请重试')
  }
}

// ==================== 生命周期钩子 ====================
onMounted(() => {
  // 使用nextTick确保DOM渲染完成后再执行初始化
  nextTick(() => {
    // 使用requestIdleCallback优化初始化时机，避免阻塞关键渲染路径
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        initializeCart()
      }, { timeout: 100 })
    } else {
      // 降级方案：在不支持requestIdleCallback的浏览器中使用setTimeout
      setTimeout(() => {
        initializeCart()
      }, 0)
    }
  })

  // 标记首次渲染完成，用于性能监控
  isFirstRender.value = false
})
</script>

<style scoped lang="less">
.cart {
  height: 100%;
  padding: 10px 10px 0 10px;
  box-sizing: border-box;
  background: #F8F9FA;
  user-select: none;
  overflow: auto;
  contain: layout style paint;
  transform: translateZ(0);
  will-change: scroll-position;
  backface-visibility: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  content-visibility: auto;
}

.cart-main {
  flex: 1;
  overflow: hidden;
}

.cart-goods {
  contain: layout style;
}
</style>
