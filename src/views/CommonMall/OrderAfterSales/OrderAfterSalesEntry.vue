<!--
/**
 * 订单售后入口页面组件
 *
 * 主要功能：
 * 1. 展示需要申请售后的商品信息，包括商品图片、名称和规格
 * 2. 提供售后类型选择功能，支持退款和退货退款两种类型
 * 3. 动态检测并显示支持的售后类型，不支持的类型会显示提示信息
 * 4. 根据用户选择跳转到对应的售后申请页面
 * 5. 集成商品数据存储，确保售后流程数据连续性
 * 6. 提供友好的用户界面和交互体验
 *
 * 技术特点：
 * - 使用响应式数据管理商品信息和支持类型
 * - 采用计算属性动态控制选项可用性
 * - 集成本地存储获取商品数据
 * - 支持路由跳转和参数传递
 * - 实现条件渲染和状态控制
 *
 * 使用场景：
 * - 用户需要对已购买商品申请售后服务
 * - 售后类型选择和流程引导
 * - 售后申请前的信息确认
 */
-->

<template>
  <!-- 售后入口主容器 -->
  <div class="after-sales-entry">
    <!-- 商品信息展示区域 -->
    <!-- 显示需要申请售后的商品基本信息 -->
    <section class="product-info">
      <!-- 商品图片 -->
      <img class="product-info__image" :src="afterSalesProductInfo.detailImageUrl" :alt="afterSalesProductInfo.name">
      <!-- 商品详细信息 -->
      <div class="product-info__details">
        <!-- 商品名称 -->
        <h3 class="product-info__name">{{ afterSalesProductInfo.name }}</h3>
        <!-- 商品规格 -->
        <p class="product-info__spec">{{ afterSalesProductInfo.spec }}</p>
      </div>
    </section>

    <!-- 区域分隔线 -->
    <div class="section-divider"></div>

    <!-- 售后服务选项区域 -->
    <!-- 提供不同类型的售后服务选择 -->
    <section class="service-options">
      <!-- 退款服务选项 -->
      <!-- 根据支持状态动态设置可用性和点击事件 -->
      <div class="service-option" :class="{ 'service-option--disabled': !supportAfterSalesTypes1 }"
        @click="supportAfterSalesTypes1 && goAfterSalesPage(1)">
        <!-- 选项内容区域 -->
        <div class="service-option__content">
          <!-- 服务类型图标 -->
          <img class="service-option__icon" src="./assets/icon-goods.png" alt="退款图标">
          <!-- 服务信息详情 -->
          <div class="service-option__info">
            <!-- 服务标题 -->
            <h4 class="service-option__title">我要退款</h4>
            <!-- 服务描述 -->
            <p class="service-option__description">未收到货/已拒收，或与商家协商一致不用退货只退款</p>
            <!-- 不支持时的提示信息 -->
            <p v-if="!supportAfterSalesTypes1" class="service-option__notice">
              暂不支持申请退款，请联系在线客服
            </p>
          </div>
        </div>
        <!-- 进入箭头图标 -->
        <img class="service-option__arrow" src="./assets/arrow.png" alt="进入">
      </div>

      <!-- 退货退款服务选项 -->
      <!-- 根据支持状态动态设置可用性和点击事件 -->
      <div class="service-option" :class="{ 'service-option--disabled': !supportAfterSalesTypes2 }"
        @click="supportAfterSalesTypes2 && goAfterSalesPage(2)">
        <!-- 选项内容区域 -->
        <div class="service-option__content">
          <!-- 服务类型图标 -->
          <img class="service-option__icon" src="./assets/icon-goods.png" alt="退货退款图标">
          <!-- 服务信息详情 -->
          <div class="service-option__info">
            <!-- 服务标题 -->
            <h4 class="service-option__title">我要退货退款</h4>
            <!-- 服务描述 -->
            <p class="service-option__description">已收到货，需退还收到的货物</p>
            <!-- 不支持时的提示信息 -->
            <p v-if="!supportAfterSalesTypes2" class="service-option__notice">
              暂不支持申请退货，请联系在线客服
            </p>
          </div>
        </div>
        <!-- 进入箭头图标 -->
        <img class="service-option__arrow" src="./assets/arrow.png" alt="进入">
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getBizCode } from '@utils/curEnv.js'
import { getAfsSupportedType } from '@api/interface/afterSales.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { afterSalesProduct } from '@utils/storage.js'
import { compact, pick } from 'es-toolkit'

// ==================== 路由管理 ====================
// 获取路由实例，用于页面跳转
const router = useRouter()

// ==================== 响应式状态定义 ====================
// 售后商品信息，包含商品的基本信息和规格
const afterSalesProductInfo = reactive({})

// 支持的售后类型列表，从服务器获取
const supportedTypes = ref([])

// ==================== 计算属性定义 ====================
// 判断是否支持退款类型（类型1）
// 检查支持类型列表中是否包含'1'
const supportAfterSalesTypes1 = computed(() => supportedTypes.value.indexOf('1') !== -1)

// 判断是否支持退货退款类型（类型2）
// 检查支持类型列表中是否包含'2'
const supportAfterSalesTypes2 = computed(() => supportedTypes.value.indexOf('2') !== -1)

// ==================== 页面跳转处理 ====================
// 跳转到对应的售后申请页面
// 根据售后类型选择不同的路由路径和参数
const goAfterSalesPage = (type) => {
  // 从本地存储获取售后商品数据
  const data = afterSalesProduct.get()
  // 提取需要传递的基础查询参数
  const baseQuery = pick(data, ['supplierSubOrderId', 'orderState', 'orderPrice', 'skuNum'])

  // 定义不同售后类型对应的路由配置
  const routes = {
    1: { path: '/wo-after-sales-refund', query: baseQuery },
    2: {
      path: '/wo-after-sales-return',
      query: { ...baseQuery, timestamp: Date.now() }
    }
  }

  // 获取对应类型的路由配置并跳转
  const route = routes[type]
  if (route) {
    router.replace(route)
  }
}

// ==================== 售后类型支持检测 ====================
// 获取支持的售后类型列表
// 根据业务代码、供应商代码和订单ID查询可用的售后类型
const getAfsSupportedTypes = async (bizCode, supplierCode, supplierOutSubOrderId) => {
  // 显示加载提示
  showLoadingToast()
  try {
    // 构建请求参数
    const params = { bizCode, supplierCode, supplierOutSubOrderId }
    // 调用API获取支持的售后类型
    const [err, res] = await getAfsSupportedType(params)

    // 处理API响应结果
    if (!err && Array.isArray(res) && res.length > 0) {
      // 成功获取到支持类型列表
      supportedTypes.value = res
    } else {
      // 未获取到支持类型或发生错误
      supportedTypes.value = []
      if (err) {
        showToast(err.msg)
      }
    }
  } catch (error) {
    // 处理请求异常
    supportedTypes.value = []
    showToast('获取售后类型失败')
  } finally {
    // 关闭加载提示
    closeToast()
  }
}

// ==================== 生命周期和初始化 ====================
// 组件挂载时的初始化操作
onMounted(() => {
  // 从本地存储获取售后商品数据
  const afterSalesData = afterSalesProduct.get()
  if (!afterSalesData) return

  // 解构获取商品相关信息
  const { supplierSubOrderId, orderState, orderPrice, skuNum, sku, supplierCode, supplierOutSubOrderId } = afterSalesData

  // 提取商品规格参数
  // 从sku对象中获取param、param1-param4字段并过滤空值
  const paramKeys = ['param', 'param1', 'param2', 'param3', 'param4']
  const params = compact(paramKeys.map(key => sku[key]))

  // 组装商品信息对象
  Object.assign(afterSalesProductInfo, {
    spec: params.join(' '),                    // 规格信息，多个参数用空格连接
    detailImageUrl: sku.detailImageUrl?.[0],   // 商品详情图片，取第一张
    name: sku.name,                            // 商品名称
    supplierSubOrderId,                        // 供应商子订单ID
    orderState,                                // 订单状态
    orderPrice,                                // 订单价格
    skuNum                                     // SKU数量
  })

  // 获取支持的售后类型
  getAfsSupportedTypes(getBizCode(), supplierCode, supplierOutSubOrderId)
})
</script>

<style scoped lang="less">
.after-sales-entry {
  min-height: 100vh;
  background-color: #FFFFFF;
}

.section-divider {
  width: 100%;
  height: 10px;
  background-color: #F8F9FA;
}

.product-info {
  display: flex;
  padding: 10px 17px;
  background-color: #FFFFFF;

  &__image {
    width: 75px;
    height: 75px;
    margin-right: 15px;
    border-radius: 4px;
    object-fit: cover;
  }

  &__details {
    flex: 1;
    overflow: hidden;
  }

  &__name {
    margin: 0 0 10px 0;
    font-size: 15px;
    font-weight: 600;
    color: #171E24;
    line-height: 1.5;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__spec {
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #4A5568;
    line-height: 1.5;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }
}

.service-options {
  padding: 0 17px;
  background-color: #FFFFFF;
}

.service-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 17px 0;
  border-bottom: 1px solid #E2E8EE;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover:not(&--disabled) {
    background-color: rgba(247, 249, 252, 0.5);
  }

  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &__content {
    display: flex;
    align-items: flex-start;
    flex: 1;
    margin-right: 5px;
  }

  &__icon {
    width: 22px;
    height: 22px;
    margin-right: 5px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
  }

  &__title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #171E24;
    line-height: 1.5;
  }

  &__description {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 400;
    color: #718096;
    line-height: 1.5;
  }

  &__notice {
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #F97316;
    line-height: 1.5;
  }

  &__arrow {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }
}
</style>
