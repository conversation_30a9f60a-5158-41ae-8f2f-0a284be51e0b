<!--
/**
 * 售后订单项组件
 *
 * 主要功能：
 * 1. 展示单个售后订单的详细信息，包括服务单号、状态和商品信息
 * 2. 提供服务单号复制功能，方便用户记录和查询
 * 3. 显示售后申请状态和类型，包含状态图标和文本说明
 * 4. 集成商品卡片组件，展示商品图片、名称、规格等信息
 * 5. 提供动态操作按钮，根据订单状态显示不同的操作选项
 * 6. 支持售后期限提醒和状态提示功能
 *
 * 技术特点：
 * - 使用组合式API管理组件状态
 * - 集成剪贴板功能支持文本复制
 * - 采用插槽机制实现灵活的内容定制
 * - 支持响应式数据绑定和计算属性
 * - 实现条件渲染和状态控制
 *
 * 使用场景：
 * - 售后订单列表中的单个订单项展示
 * - 订单状态跟踪和操作入口
 * - 售后信息查看和管理
 */
-->

<template>
  <!-- 售后订单项主容器 -->
  <article class="after-sales-item">
    <!-- 使用WoCard组件包装，提供统一的卡片样式 -->
    <WoCard>
      <!-- 订单头部信息区域 -->
      <!-- 包含服务单号和售后状态 -->
      <header class="after-sales-item__header">
        <!-- 服务单号区域 -->
        <div class="after-sales-item__service-number">
          <!-- 服务单号文本 -->
          <span class="after-sales-item__service-text">
            服务单号：{{ orderData.afterSaleId || '暂无服务单号' }}
          </span>
          <!-- 复制按钮（当有服务单号时显示） -->
          <img
            v-if="orderData.afterSaleId"
            src="../../../../static/images/copy.png"
            alt="复制"
            class="after-sales-item__copy-btn"
            loading="lazy"
            @click.stop="handleCopyOrderNumber(orderData.id)"
          />
        </div>

        <!-- 售后状态区域（当有申请类型时显示） -->
        <div v-if="orderData.applyType" class="after-sales-item__status">
          <!-- 状态图标 -->
          <i class="after-sales-status-icon" :class="afterSalesStatusClass"></i>
          <!-- 状态文本 -->
          <span class="after-sales-status-text">{{ afterSalesStatusText }}</span>
        </div>
      </header>

      <!-- 商品信息区域 -->
      <section class="after-sales-item__goods">
        <!-- 售后商品卡片组件 -->
        <!-- 展示商品详细信息和操作按钮 -->
        <AfterSaleGoodsCard
          :item="orderData"
          :image-size="75"
          :min-height="110"
          :show-actions="true"
          :item-id="orderData.id"
        >
          <!-- 提示信息插槽 -->
          <template #tips>
            <!-- 售后期限过期提示 -->
            <p class="after-sales-tips" v-if="!isExpires && !afterSaleId && orderData.orderState !== '10'">
              该商品已超过售后期 <i class="after-sales-tips__icon" @click="showTipsDetail"></i>
            </p>
            <!-- 订单状态文本 -->
            <p class="after-sales-tips" v-else>{{ orderStateText }}</p>
          </template>

          <!-- 操作按钮插槽 -->
          <template #actions>
            <!-- 动态渲染操作按钮 -->
            <!-- 根据actionButtons数组生成对应的操作按钮 -->
            <WoButton
              v-for="action in actionButtons"
              :key="action.key"
              :type="action.type || 'primary'"
              size="small"
              :disabled="action.disabled"
              @click.stop="action.handler"
            >
              {{ action.label }}
            </WoButton>
          </template>
        </AfterSaleGoodsCard>
      </section>
    </WoCard>

    <!-- 售后申请时效过期提醒弹窗 -->
    <!-- 当用户尝试对过期订单进行售后操作时显示 -->
    <Popup
      class="after-sales-expiration-popup"
      safe-area-inset-bottom
      lock-scroll
      round
      position="bottom"
      v-model:show="afterSalesExpirationPopupShow"
    >
      <!-- 弹窗头部 -->
      <header class="after-sales-expiration-popup__header">
        <h3 class="after-sales-expiration-popup__title"></h3>
        <!-- 关闭按钮 -->
        <button class="after-sales-expiration-popup__close" @click="popupClose">
          <img src="../assets/popupClose.png" alt="关闭" />
        </button>
      </header>

      <!-- 弹窗内容区域 -->
      <main class="after-sales-expiration-popup__content">
        <div class="after-sales-expiration-popup__message">
          <!-- 主要提示信息 -->
          <p class="after-sales-expiration-popup__title-text">抱歉，订单已过售后申请时效</p>
          <!-- 辅助说明信息 -->
          <p class="after-sales-expiration-popup__desc">商品已超过售后期限，如需售后可联系客服处理</p>
        </div>
      </main>

      <!-- 弹窗操作区域 -->
      <footer class="after-sales-expiration-popup__footer">
        <!-- 确定按钮，关闭弹窗 -->
        <button class="after-sales-expiration-popup__confirm-btn" @click="afterSalesExpirationPopupShow = false">
          确定
        </button>
      </footer>
    </Popup>
  </article>
</template>

<script setup>
import { computed, toRefs, ref } from 'vue'
import { debounce } from 'es-toolkit'
import useClipboard from 'vue-clipboard3'
import { showToast, Popup } from 'vant'
import dayjs from 'dayjs'
import WoCard from '@components/WoElementCom/WoCard.vue'
import AfterSaleGoodsCard from '@components/GoodsListCommon/AfterSaleGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 订单数据对象，包含订单的所有相关信息
  orderData: {
    type: Object,
    required: true
  },
  // 操作按钮数组，定义订单项可执行的操作
  actionButtons: {
    type: Array,
    default: () => []
  }
})

// 使用toRefs解构props，保持响应性
const { orderData, actionButtons } = toRefs(props)

// ==================== 工具函数集成 ====================
// 获取剪贴板功能，用于复制服务单号
const { toClipboard } = useClipboard()

// ==================== 响应式状态定义 ====================
// 售后申请时效过期弹窗显示状态
const afterSalesExpirationPopupShow = ref(false)

// ==================== 计算属性定义 ====================
// 获取售后申请ID
const afterSaleId = computed(() => orderData.value?.afterSaleId)

// 判断订单是否在售后有效期内
// 通过比较过期时间和当前时间来确定
const isExpires = computed(() => {
  const expireTimeDateStr = orderData.value?.expireTime
  if (!expireTimeDateStr) return false
  // 过期时间大于当前时间则未过期
  return dayjs(expireTimeDateStr) > dayjs()
})

// 订单状态文本显示
// 根据订单状态返回对应的状态描述
const orderStateText = computed(() => {
  const status = orderData.value?.orderState
  return status === '10' ? '订单已退款' : ''
})

// 售后状态文本显示
// 根据申请类型返回对应的状态文本
const afterSalesStatusText = computed(() => {
  const statusMap = {
    '1': '退款',
    '2': '退货',
    '3': '换货',
    '4': '维修',
    '5': '补发商品'
  }
  return statusMap[orderData.value?.applyType] || ''
})

// 售后状态图标的CSS类名
// 根据申请类型返回对应的图标样式类
const afterSalesStatusClass = computed(() => {
  const classMap = {
    '1': 'after-sales-status-icon--refund',      // 退款图标
    '2': 'after-sales-status-icon--return',      // 退货图标
    '3': 'after-sales-status-icon--exchange',    // 换货图标
    '4': 'after-sales-status-icon--maintenance', // 维修图标
    '5': 'after-sales-status-icon--reissue'      // 补发图标
  }
  return classMap[orderData.value?.applyType] || ''
})

const handleCopyOrderNumber = debounce(async (orderNumber) => {
  try {
    await toClipboard(String(orderNumber))
    showToast('复制成功')
  } catch (e) {
    showToast('复制失败')
  }
}, 300)

const showTipsDetail = () => {
  afterSalesExpirationPopupShow.value = true
}

const popupClose = () => {
  afterSalesExpirationPopupShow.value = false
}
</script>

<style scoped lang="less">
.after-sales-item {
  margin-bottom: 10px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  &__service-number {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__service-text {
    font-size: 11px;
    color: #4A5568;
    margin-right: 3px;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__copy-btn {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  &__goods {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.after-sales-status-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: 10px;
  background-repeat: no-repeat;
  background-size: contain;

  &--reissue {
    background-image: url("../assets/reissue-goods.png");
  }

  &--exchange {
    background-image: url("../assets/exchange-goods.png");
  }

  &--refund {
    background-image: url("../assets/refund-only.png");
  }

  &--return {
    background-image: url("../assets/return-of-goods.png");
  }

  &--maintenance {
    background-image: url("../assets/maintenance.png");
  }
}

.after-sales-status-text {
  font-size: 12px;
  color: #718096;
  font-weight: 400;
}

.after-sales-tips {
  display: flex;
  align-items: center;
  color: #718096;
  text-align: left;
  font-weight: 400;
  font-size: 12px;

  &__icon {
    display: inline-block;
    width: 15px;
    height: 15px;
    background-image: url("../assets/question.png");
    background-repeat: no-repeat;
    background-size: contain;
    cursor: pointer;
  }
}

.after-sales-expiration-popup {
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 240px;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  &__title {
    flex: 1;
    font-size: 17px;
    color: #171E24;
    text-align: center;
    line-height: 1;
    font-weight: 400;
  }

  &__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;

    img {
      width: 14px;
      height: 14px;
    }
  }

  &__content {
    margin-bottom: 50px;
  }

  &__message {
    text-align: center;
  }

  &__title-text {
    font-size: 18px + 1px;
    color: #171E24;
    font-weight: 700;
    margin-bottom: 15px;
  }

  &__desc {
    margin-top: 10px;
    font-size: 13px;
    color: #4A5568;
    font-weight: 400;
  }

  &__footer {
    width: 100%;
    height: 35px;
    margin-top: 20px;
  }

  &__confirm-btn {
    background: var(--wo-biz-theme-gradient-1);
    border-radius: 9999px;
    font-size: 17px;
    color: #FFFFFF;
    font-weight: 400;
    width: 100%;
    height: 35px;
    text-align: center;
    line-height: 35px;
    cursor: pointer;
    border: none;
  }
}
</style>
