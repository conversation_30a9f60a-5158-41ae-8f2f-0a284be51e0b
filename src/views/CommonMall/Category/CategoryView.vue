<!--
===================== 商品分类页面组件 =====================
主要功能：
1. 展示三级商品分类结构（一级分类侧边栏 + 二三级分类主内容区）
2. 支持分类懒加载和滚动监听优化性能
3. 集成搜索功能，支持跳转到搜索页面
4. 响应式布局，自适应不同屏幕尺寸
5. 支持省份筛选和业务代码区分
6. 提供骨架屏加载状态和空状态展示

技术特性：
- 使用Vue 3 Composition API
- 集成VueUse工具库实现响应式布局
- 采用Map和Set数据结构优化性能
- 实现滚动懒加载减少API调用
- 支持路由参数驱动的分类选择
- 集成Vant UI组件库

使用场景：
- 电商平台商品分类浏览
- 支持多业务线分类展示
- 移动端友好的分类导航
===================== 商品分类页面组件 =====================
-->

<template>
  <MainLayout scroll="auto">
    <!-- 省份筛选组件 -->
    <ProvinceFilter/>

    <div class="category-page">
      <!-- 搜索头部区域 -->
      <SearchHeader
        v-model="searchKeyword"
        placeholder="搜索商品"
        :redirectToSearch="bizCode !== 'zq'"
        :redirectUrl="'/search'"
        @search="handleSearch"
      />

      <!-- 分类主体布局容器 -->
      <div class="category-page__layout">
        <!-- 一级分类侧边栏 -->
        <CategorySidebar
          :categories="firstCategories"
          :is-loading="isFirstCategoryLoading"
          :active-index="activeFirstCategory"
          @change="handleFirstCategoryChange"
          ref="sidebarRef"
        />

        <!-- 二三级分类主内容区 -->
        <main class="category-page__main" ref="categoryMainRef">
          <!-- 一级分类数据加载中的骨架屏 -->
          <CategorySkeleton
            v-if="isFirstCategoryLoading"
            type="content"
            :count="3"
            :items-per-section="3"
          />

          <!-- 二级分类数据加载中的骨架屏 -->
          <CategorySkeleton
            v-else-if="isSecondCategoryLoading"
            type="content"
            :count="3"
            :items-per-section="3"
          />

          <!-- 分类内容展示区域 -->
          <div v-else class="category-page__content">
            <!-- 遍历二级分类组，每个组包含对应的三级分类 -->
            <section
              v-for="(group, index) in thirdCategoriesGroups"
              :key="group.id || index"
              class="category-page__section"
              :data-category-id="group.id"
            >
              <!-- 二级分类标题 -->
              <h3 class="category-page__section-title">{{ group.title }}</h3>

              <!-- 三级分类项目列表 -->
              <div v-if="group.items.length > 0" class="category-page__items">
                <CategoryItem
                  v-for="item in group.items"
                  :key="item.id"
                  :id="item.id"
                  :name="item.name"
                  :image-url="item.img"
                  :width-style="itemWidthStyle"
                  :default-icon="defaultIcon"
                  @click="handleCategoryClick"
                />
              </div>

              <!-- 三级分类加载中的骨架屏 -->
              <CategorySkeleton
                v-else-if="group.isLoading"
                type="grid"
                :count="3"
              />

              <!-- 无分类数据的空状态 -->
              <div v-else-if="group.isEmpty" class="category-page__empty">
                <span class="category-page__empty-text">暂无商品分类</span>
              </div>

              <!-- 懒加载触发区域 -->
              <div v-else class="category-page__placeholder">
                <div class="category-page__load-trigger" :data-second-id="group.id"></div>
              </div>
            </section>
          </div>
        </main>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useElementSize } from '@vueuse/core' // 响应式元素尺寸监听
import { debounce } from 'es-toolkit'
import { get } from 'es-toolkit/compat'
import { closeToast, showLoadingToast, showToast } from 'vant' // Toast消息提示
import SearchHeader from '@components/Common/SearchHeader.vue' // 搜索头部组件
import MainLayout from '@components/Common/MainLayout/MainLayout.vue' // 主布局组件
import CategorySkeleton from './components/CategorySkeleton.vue' // 分类骨架屏组件
import CategoryItem from './components/CategoryItem.vue' // 分类项组件
import CategorySidebar from './components/CategorySidebar.vue' // 分类侧边栏组件
import ProvinceFilter from '@components/ZQCommon/ProvinceFilter.vue' // 省份筛选组件
import { getClassification } from '@api/interface/goods.js' // 分类数据API
import { getBizCode } from '@utils/curEnv.js' // 业务代码获取工具
import { getCustomerManagerInfo, getEnterpriseManagerInfo, queryZqInfo } from '@utils/zqInfo.js' // 用户信息工具
import { useProvinceServiceStore } from '@store/modules/provinceService.js' // 省份服务状态管理
const router = useRouter() // 路由实例，用于页面跳转
const route = useRoute() // 当前路由信息，用于获取路由参数
const provinceServiceStore = useProvinceServiceStore() // 省份服务状态管理
const bizCode = getBizCode() // 当前业务代码

// 用户角色类型计算属性，用于区分不同用户权限
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// ===================== 常量定义 =====================
const INITIAL_LOAD_COUNT = 3 // 初始加载的二级分类数量
const LOAD_THRESHOLD = 200 // 懒加载触发阈值（像素）
const defaultIcon = 'https://img01.yzcdn.cn/vant/cat.jpeg' // 默认分类图标

// ===================== 搜索功能相关 =====================
const searchKeyword = ref('') // 搜索关键词

// 搜索处理函数，构建搜索参数并跳转到搜索页面
const handleSearch = (keyWord) => {
  if (!keyWord) {
    showToast('请输入搜索内容')
    return
  }

  const timestamp = Date.parse(new Date()) // 时间戳，用于缓存控制
  const testDMX = get(route.query, 'testDMX', false) // 测试参数

  // 根据用户角色类型决定供应商代码和省份参数来源
  const zqInfo = queryZqInfo()
  let supplierCode = roleType.value !== '4' ? (zqInfo.isvList[0]?.isvId || '') : ''
  let proStr = roleType.value !== '4' ? zqInfo.provinceCode.join(',') : ''

  // 角色类型为4时，从省份服务store获取参数
  if (roleType.value === '4') {
    supplierCode = provinceServiceStore.selectedIsvId || ''
    proStr = provinceServiceStore.selectedAreaId || ''
  }

  // 构建搜索URL并跳转
  const searchUrl = `/search/list?timestamp=${timestamp}&keyword=${keyWord}&testDMX=${testDMX}&supplierCode=${supplierCode}&proStr=${proStr}`
  router.push(searchUrl)
  searchKeyword.value = '' // 清空搜索框
}

// ===================== 组件引用 =====================
const sidebarRef = ref(null) // 侧边栏组件引用
const categoryMainRef = ref(null) // 主内容区组件引用

// ===================== 分类数据管理 =====================
const firstCategories = ref([]) // 一级分类数据
const secondCategories = ref([]) // 二级分类数据
const thirdCategories = ref(new Map()) // 三级分类数据，使用Map结构提高查询性能
const activeFirstCategory = ref(0) // 当前选中的一级分类索引

// ===================== 加载状态管理 =====================
const isFirstCategoryLoading = ref(true) // 一级分类加载状态
const isSecondCategoryLoading = ref(false) // 二级分类加载状态
const loadingThirdCategories = ref(new Set()) // 正在加载的三级分类ID集合
const loadedSecondCategoryIds = ref(new Set()) // 已加载的二级分类ID集合

// ===================== 响应式布局计算 =====================
const { width: containerWidth } = useElementSize(categoryMainRef) // 容器宽度监听
const itemsPerRow = computed(() => Math.max(Math.floor(containerWidth.value / 100), 3)) // 每行显示的分类项数量
const itemWidthStyle = computed(() => `${100 / itemsPerRow.value}%`) // 分类项宽度样式

// 计算三级分类分组显示数据，将二级分类和对应的三级分类组合
const thirdCategoriesGroups = computed(() => {
  const groups = []
  secondCategories.value.forEach(secondCategory => {
    const items = thirdCategories.value.get(secondCategory.id) || [] // 获取对应的三级分类
    const isLoading = loadingThirdCategories.value.has(secondCategory.id) // 是否正在加载
    const isLoaded = loadedSecondCategoryIds.value.has(secondCategory.id) // 是否已加载

    groups.push({
      id: secondCategory.id,
      title: secondCategory.name,
      items: items,
      isLoading: isLoading,
      isLoaded: isLoaded,
      isEmpty: isLoaded && items.length === 0 // 已加载但无数据
    })
  })
  return groups
})

// ===================== 分类交互事件处理 =====================
// 一级分类切换处理函数
const handleFirstCategoryChange = (index, category) => {
  if (!category) return

  // 滚动主内容区到顶部
  if (categoryMainRef.value) {
    categoryMainRef.value.scrollTop = 0
  }

  // 更新路由，保持URL与当前选中分类同步
  router.replace(`/category/${category.id}`)

  // 获取选中一级分类对应的二级和三级分类数据
  fetchSecondAndThirdCategories(category.id)
}

// 分类项点击处理函数，跳转到商品列表页
const handleCategoryClick = (categoryData) => {
  router.push({ path: `/goodslist/${categoryData.id}` })
}

// ===================== 分类数据获取和处理 =====================
// 获取分类数据的通用函数，id为空时获取一级分类，否则获取子分类
const fetchCategories = async (id = '') => {
  try {
    // 根据请求类型设置对应的加载状态
    if (id === '') {
      isFirstCategoryLoading.value = true // 获取一级分类时的加载状态
    } else if (!isFirstCategoryLoading.value) {
      isSecondCategoryLoading.value = true // 获取二级分类时的加载状态
      showLoadingToast() // 显示加载提示
    }

    // 调用分类数据API
    const [err, data] = await getClassification({
      bizCode: getBizCode('GOODS'), // 商品业务代码
      category_pid: id, // 父分类ID，空字符串表示获取顶级分类
      page_no: 1, // 页码
      page_size: 500 // 每页数量
    })

    // 处理API错误
    if (err) {
      showToast('获取分类数据失败')
      return
    }

    // 处理返回的分类数据
    if (data && Array.isArray(data)) {
      await processCategories(data, id)
    }
  } catch {
    showToast('获取分类数据失败')
  } finally {
    // 关闭加载状态和提示
    if (id === '') {
      isFirstCategoryLoading.value = false
    } else {
      isSecondCategoryLoading.value = false
    }
    closeToast()
  }
}

// 处理分类数据的核心函数，根据id判断处理一级分类还是二级分类
const processCategories = async (data, id) => {
  if (id === '') {
    // 处理一级分类数据
    firstCategories.value = data.filter(item => item.depth === 1)

    // 根据路由参数设置当前选中的分类
    const routeCategoryId = route.params.id

    if (routeCategoryId && firstCategories.value.length > 0) {
      // 查找路由参数对应的一级分类索引（处理字符串和数字类型转换）
      const categoryIndex = firstCategories.value.findIndex(category =>
        String(category.id) === String(routeCategoryId)
      )

      if (categoryIndex !== -1) {
        // 找到对应分类，设置为当前选中并获取子分类
        activeFirstCategory.value = categoryIndex
        await fetchSecondAndThirdCategories(firstCategories.value[categoryIndex].id)

        // 延迟滚动到对应的一级分类位置，确保DOM更新完成
        nextTick(() => {
          setTimeout(() => {
            if (sidebarRef.value) {
              sidebarRef.value.scrollToCategory(categoryIndex)
            }
          }, 200)
        })
      } else {
        // 路由参数无效，默认选择第一个分类
        activeFirstCategory.value = 0
        await fetchSecondAndThirdCategories(firstCategories.value[0].id)
      }
    } else if (firstCategories.value.length > 0) {
      // 无路由参数时，默认选择第一个分类
      activeFirstCategory.value = 0
      await fetchSecondAndThirdCategories(firstCategories.value[0].id)
    }
  } else {
    // 处理二级分类数据
    const secondLevel = data.filter(item => item.depth === 2)
    secondCategories.value = secondLevel

    // 清空之前的三级分类相关数据，避免数据混乱
    thirdCategories.value.clear()
    loadedSecondCategoryIds.value.clear()
    loadingThirdCategories.value.clear()

    // 加载初始的几个二级分类对应的三级分类数据
    if (secondLevel.length > 0) {
      await loadInitialThirdCategories(secondLevel)
      // DOM更新后设置滚动监听器
      nextTick(() => setupScrollListener())
    }
  }
}

// 获取指定一级分类的二级和三级分类数据
const fetchSecondAndThirdCategories = async (firstCategoryId) => {
  if (!firstCategoryId) return
  await fetchCategories(firstCategoryId)
}

// 获取单个二级分类对应的三级分类数据
const fetchThirdCategories = async (secondCategoryId) => {
  if (!secondCategoryId) return []

  try {
    const [err, data] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: secondCategoryId, // 以二级分类ID作为父ID获取三级分类
      page_no: 1,
      page_size: 500
    })

    if (err || !data) return []

    return data.filter(item => item.depth === 3) // 只返回三级分类数据
  } catch {
    return []
  }
}

// 加载初始的几个二级分类对应的三级分类，提升首屏加载体验
const loadInitialThirdCategories = async (secondLevel) => {
  const initialCategories = secondLevel.slice(0, INITIAL_LOAD_COUNT) // 只加载前几个

  // 并发加载多个二级分类的三级分类数据
  const loadPromises = initialCategories.map(async (secondCategory) => {
    loadingThirdCategories.value.add(secondCategory.id) // 标记为加载中

    try {
      const thirdItems = await fetchThirdCategories(secondCategory.id)
      thirdCategories.value.set(secondCategory.id, thirdItems) // 存储三级分类数据
      loadedSecondCategoryIds.value.add(secondCategory.id) // 标记为已加载
    } catch {
      thirdCategories.value.set(secondCategory.id, []) // 加载失败时设置为空数组
      loadedSecondCategoryIds.value.add(secondCategory.id)
    } finally {
      loadingThirdCategories.value.delete(secondCategory.id) // 移除加载中标记
    }
  })

  await Promise.all(loadPromises) // 等待所有加载完成
}

// 懒加载单个二级分类的三级分类，用于滚动时按需加载
const loadThirdCategoryLazy = async (secondCategoryId) => {
  // 避免重复加载已加载或正在加载的分类
  if (loadedSecondCategoryIds.value.has(secondCategoryId) ||
    loadingThirdCategories.value.has(secondCategoryId)) {
    return
  }

  loadingThirdCategories.value.add(secondCategoryId) // 标记为加载中

  try {
    const thirdItems = await fetchThirdCategories(secondCategoryId)
    thirdCategories.value.set(secondCategoryId, thirdItems)
    loadedSecondCategoryIds.value.add(secondCategoryId)
  } catch {
    thirdCategories.value.set(secondCategoryId, [])
    loadedSecondCategoryIds.value.add(secondCategoryId)
  } finally {
    loadingThirdCategories.value.delete(secondCategoryId)
  }
}




// ===================== 滚动懒加载功能 =====================
let scrollListener = null // 滚动监听器引用

// 设置滚动监听器，实现三级分类的懒加载
const setupScrollListener = () => {
  if (!categoryMainRef.value) return

  // 移除之前的监听器，避免重复绑定
  if (scrollListener) {
    categoryMainRef.value.removeEventListener('scroll', scrollListener)
  }

  // 使用防抖处理滚动事件，避免频繁触发
  const debouncedCheck = debounce(() => {
    checkAndLoadVisibleCategories()
  }, 100)

  scrollListener = debouncedCheck
  // 使用passive选项优化滚动性能
  categoryMainRef.value.addEventListener('scroll', scrollListener, { passive: true })

  // 初始检查一次，加载首屏可见的分类
  nextTick(() => checkAndLoadVisibleCategories())
}

// 检查并加载可视区域内的分类数据
const checkAndLoadVisibleCategories = () => {
  if (!categoryMainRef.value) return

  const container = categoryMainRef.value
  const containerRect = container.getBoundingClientRect()
  const containerTop = containerRect.top
  const containerBottom = containerRect.bottom

  // 获取所有分类区块
  const sections = container.querySelectorAll('.category-page__section')

  sections.forEach(section => {
    const sectionRect = section.getBoundingClientRect()
    const secondCategoryId = section.dataset.categoryId

    // 跳过无效ID或已加载的分类
    if (!secondCategoryId || loadedSecondCategoryIds.value.has(secondCategoryId)) {
      return
    }

    // 检查区块是否在可视区域内或即将进入（预加载）
    const isVisible = sectionRect.top < containerBottom + LOAD_THRESHOLD &&
      sectionRect.bottom > containerTop - LOAD_THRESHOLD

    if (isVisible) {
      loadThirdCategoryLazy(secondCategoryId) // 懒加载该分类的三级数据
    }
  })
}

// ===================== 生命周期钩子 =====================
// 组件挂载时获取初始分类数据
onMounted(async () => {
  await fetchCategories() // 获取一级分类数据
})

// 组件卸载时清理事件监听器，防止内存泄漏
onBeforeUnmount(() => {
  if (scrollListener && categoryMainRef.value) {
    categoryMainRef.value.removeEventListener('scroll', scrollListener)
  }
})
</script>

<style scoped lang="less">
.category-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F8F9FA;

  &__layout {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  &__main {
    flex: 1;
    overflow-y: auto;
    background-color: #FFFFFF;
    contain: layout style paint;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };
  }

  &__content {
    padding: 10px;
  }

  &__section {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(227, 227, 242, 0.5);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__section-title {
    margin: 0 0 10px 0;
    padding: 0;
    font-size: 14px;
    font-weight: 500;
    color: #171E24;
    line-height: 1.4;
  }

  &__items {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;
  }

  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
  }

  &__empty-text {
    font-size: 13px;
    color: #718096;
  }

  &__placeholder {
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__load-trigger {
    width: 100%;
    height: 20px;
    background: transparent;
  }
}
</style>
