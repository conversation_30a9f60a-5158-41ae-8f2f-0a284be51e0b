import { ref } from 'vue'
import { cloneDeep, intersection } from 'es-toolkit'
import { pullAll } from 'es-toolkit/compat'
import { getGoodsDetail, isWhiteUserLimitCheck } from '@api/interface/goods.js'
import { getBizCode } from '@utils/curEnv.js'
import { useUserStore } from '@/store/modules/user.js'

// 获取商品详情Hooks
export function useGoodsDetail(goodsId, skuId) {
  const spu = ref(null)
  const curSpecs = ref([])
  const curSkuId = ref(skuId || '')
  const specsList = ref(null)
  const loading = ref(false)
  const userStore = useUserStore()

  const querySpu = async () => {
    loading.value = true
    try {
      await userStore.queryDefaultAddr()
      const info = userStore.curAddressInfo
      const addressInfo = JSON.stringify({
        provinceId: info.provinceId,
        provinceName: info.provinceName,
        cityId: info.cityId,
        cityName: info.cityName,
        countyId: info.countyId,
        countyName: info.countyName,
        townId: info.townId,
        townName: info.townName
      })
      const [json, goodsDetail] = await getGoodsDetail({
        bizCode: getBizCode('GOODS'),
        goodsId,
        skuId,
        addressInfo
      })

      if (!goodsDetail || !goodsDetail.skuList || goodsDetail.skuList.length === 0) {
        loading.value = false
        return json
      }

      spu.value = goodsDetail
      let skuCache = {}

      if (curSkuId.value) {
        const sku = spu.value.skuList.find(sku => curSkuId.value === sku.skuId)
        if (sku) skuCache = sku
      }

      if (!skuCache.skuId) {
        // 找到第一个可用的sku
        const availableSku = spu.value.skuList.find(sku => {
          const { status } = checkSkuAvailable(sku)
          return status === 0
        })

        if (availableSku) {
          curSkuId.value = availableSku.skuId
          skuCache = availableSku
        } else {
          // 如果没有可用sku，使用第一个
          curSkuId.value = spu.value.skuList[0].skuId
          skuCache = spu.value.skuList[0]
        }
      }

      const { param, param1, param2, param3 } = skuCache
      curSpecs.value = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3].filter(p => !!p)
      loading.value = false
      return cloneDeep(json)
    } catch (error) {
      loading.value = false
      console.error('querySpu error:', error)
      return { code: '9999', msg: error.message || '获取商品详情失败' }
    }
  }

  const querySku = () => {
    if (!spu.value || !spu.value.skuList || !curSkuId.value) {
      return null
    }
    return cloneDeep(spu.value.skuList.find(sku => sku.skuId === curSkuId.value))
  }

  const querySkuCount = () => {
    if (!spu.value || !spu.value.skuList) {
      return 0
    }
    return spu.value.skuList.length
  }

  const querySpecsList = () => {
    if (specsList.value) return specsList.value
    if (!spu.value || !spu.value.skuList) {
      return [[], [], [], []]
    }

    const specs = [[], [], [], []]
    spu.value.skuList.forEach(sku => {
      const { param, param1, param2, param3 } = sku
      if (param) {
        const has0 = specs[0].indexOf('_p0_' + param) >= 0
        if (!has0) specs[0].push('_p0_' + param)
      }
      if (param1) {
        const has1 = specs[1].indexOf('_p1_' + param1) >= 0
        if (!has1) specs[1].push('_p1_' + param1)
      }
      if (param2) {
        const has2 = specs[2].indexOf('_p2_' + param2) >= 0
        if (!has2) specs[2].push('_p2_' + param2)
      }
      if (param3) {
        const has3 = specs[3].indexOf('_p3_' + param3) >= 0
        if (!has3) specs[3].push('_p3_' + param3)
      }
    })
    specsList.value = cloneDeep(specs)
    return specsList.value
  }

  const queryCurSpecs = () => {
    const specsListArr = querySpecsList()
    const newList = []
    specsListArr.forEach(item => {
      curSpecs.value.forEach(curSpec => {
        if (item.indexOf(curSpec) >= 0) newList.push(curSpec)
      })
    })
    return newList
  }

  const setSpecs = (spec) => {
    if (!spu.value || !spu.value.skuList) {
      return false
    }

    if (queryDisabledSpecs().indexOf(spec) >= 0) return false
    let cur = queryCurSpecs()
    const isSelected = cur.find(s => s === spec)
    if (isSelected) {
      cur = cur.filter(s => s !== spec)
    } else {
      const curGroup = getSpecsGroupFromSpec(querySpecsList(), spec)
      pullAll(cur, curGroup)
      cur.push(spec)
    }
    curSpecs.value = cur
    const list = getSkuListFromSpec(spu.value.skuList, cur)
    if (list.length === 1) {
      curSkuId.value = list[0].skuId
      return true
    } else {
      return false
    }
  }

  const queryDisabledSpecs = () => {
    if (!spu.value || !spu.value.skuList) {
      return []
    }

    const specsListArr = querySpecsList()
    const cur = queryCurSpecs()
    let disabledSpecs = [...specsListArr[0], ...specsListArr[1], ...specsListArr[2], ...specsListArr[3]]

    const runner = (specsListGroup, paramName) => {
      if (specsListGroup.length > 0) {
        const filteredSpecs = pullAll(cloneDeep(cur), specsListGroup)
        const skuList = spu.value.skuList.filter(sku => {
          const ret = checkSkuAvailable(sku)
          return ret.status === 0
        }).filter(sku => {
          if (filteredSpecs.length === 0) return true
          const { param, param1, param2, param3 } = sku
          const specs = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3].filter(p => !!p)
          const intersectionedSpecs = intersection(specs, filteredSpecs)
          return intersectionedSpecs.length === filteredSpecs.length
        })
        const validSpecs = skuList.map(sku => paramPrefix(paramName) + sku[paramName]).filter(spec => spec !== paramPrefix(paramName) + 'undefined')
        disabledSpecs = disabledSpecs.filter(spec => {
          return validSpecs.indexOf(spec) === -1
        })
      }
    }
    runner(specsListArr[0], 'param')
    runner(specsListArr[1], 'param1')
    runner(specsListArr[2], 'param2')
    runner(specsListArr[3], 'param3')
    return disabledSpecs
  }

  const checkSkuAvailable = (sku) => {
    const skuu = sku || querySku()
    if (skuu.state !== '1') return { status: 2, err: '该商品已下架，请选购其他商品~' }
    if (Number(skuu.stock) <= 0) return { status: 1, err: '所选地区暂时无货，非常抱歉！' }
    return { status: 0 }
  }

  const checkSpuAvailable = async () => {
    if (spu.value.isCheckWhiteUser === '1') {
      if (userStore.isLogin) {
        const [err, json] = await isWhiteUserLimitCheck(goodsId)
        if (!err && !json) return true
      }
    }
    return false
  }

  const isSpecsComplete = () => {
    const specsListArr = querySpecsList()
    const cur = queryCurSpecs()
    const count = specsListArr.reduce((a, b) => {
      if (b.length > 0) a++
      return a
    }, 0)
    return count === cur.length
  }

  // 工具函数
  const getSpecsGroupFromSpec = (specsList, spec) => {
    return specsList.filter(group => group.indexOf(spec) >= 0)[0]
  }
  const getSkuListFromSpec = (skuList, specs) => {
    let list = skuList
    specs.forEach(spec => {
      list = list.filter(sku => {
        return ('_p0_' + sku.param === spec) || ('_p1_' + sku.param1 === spec) || ('_p2_' + sku.param2 === spec) || ('_p3_' + sku.param3 === spec)
      })
    })
    return list
  }
  const paramPrefix = (name) => {
    switch (name) {
      case 'param': return '_p0_'
      case 'param1': return '_p1_'
      case 'param2': return '_p2_'
      case 'param3': return '_p3_'
      default: return ''
    }
  }

  return {
    spu,
    curSpecs,
    curSkuId,
    loading,
    querySpu,
    querySku,
    querySkuCount,
    querySpecsList,
    queryCurSpecs,
    setSpecs,
    queryDisabledSpecs,
    checkSkuAvailable,
    checkSpuAvailable,
    isSpecsComplete
  }
}
