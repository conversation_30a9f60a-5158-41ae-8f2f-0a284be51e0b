import { ref, computed, watch } from 'vue'
import {
  pick,
  compact,
  debounce,
  isNil,
  get,
  defaultTo,
  isEmpty,
  filter,
  map,
  find,
  some,
  sortBy,
  orderBy,
  slice,
  merge
} from 'es-toolkit'
import { useRouter } from 'vue-router'
import { useCartStore } from '@store/modules/cart'
import { useUserStore } from '@store/modules/user'
import { ElMessage } from 'element-plus'

/**
 * 商品列表管理组合式函数
 * 提供商品列表展示、筛选、排序、分页、购物车操作等功能
 * @returns {Object} 包含商品列表相关的响应式数据和方法
 */
export function useGoodsList() {
  const router = useRouter()
  const cartStore = useCartStore()
  const userStore = useUserStore()

  /** @type {import('vue').Ref<Array>} 商品列表数据 */
  const goodsList = ref([])

  /** @type {import('vue').Ref<boolean>} 加载状态 */
  const loading = ref(false)

  /** @type {import('vue').Ref<number>} 商品总数 */
  const total = ref(0)

  /** @type {import('vue').Ref<number>} 当前页码 */
  const currentPage = ref(1)

  /** @type {import('vue').Ref<number>} 每页显示数量 */
  const pageSize = ref(20)

  /** @type {import('vue').Ref<Object>} 筛选条件 */
  const filters = ref({
    keyword: '',
    categoryId: null,
    brandId: null,
    priceRange: [null, null],
    sortBy: 'default', // default, price_asc, price_desc, sales_desc
    inStock: true
  })

  /**
   * 计算属性：筛选后的商品列表
   * 根据筛选条件对商品列表进行过滤和排序
   * @type {import('vue').ComputedRef<Array>}
   */
  const filteredGoodsList = computed(() => {
    let result = [...goodsList.value]

    // 使用 es-toolkit 的 filter 方法进行关键词筛选
    if (!isEmpty(filters.value.keyword)) {
      const keyword = filters.value.keyword.toLowerCase()
      result = filter(result, item => {
        const name = get(item, 'name', '').toLowerCase()
        const description = get(item, 'description', '').toLowerCase()
        return name.includes(keyword) || description.includes(keyword)
      })
    }

    // 使用 es-toolkit 的 filter 方法进行分类筛选
    if (!isNil(filters.value.categoryId)) {
      result = filter(result, item => item.categoryId === filters.value.categoryId)
    }

    // 使用 es-toolkit 的 filter 方法进行品牌筛选
    if (!isNil(filters.value.brandId)) {
      result = filter(result, item => item.brandId === filters.value.brandId)
    }

    // 价格区间筛选
    const [minPrice, maxPrice] = filters.value.priceRange
    if (!isNil(minPrice)) {
      result = filter(result, item => get(item, 'price', 0) >= minPrice)
    }
    if (!isNil(maxPrice)) {
      result = filter(result, item => get(item, 'price', 0) <= maxPrice)
    }

    // 库存筛选
    if (filters.value.inStock) {
      result = filter(result, item => get(item, 'stock', 0) > 0)
    }

    // 使用 es-toolkit 的排序方法
    switch (filters.value.sortBy) {
      case 'price_asc':
        result = orderBy(result, ['price'], ['asc'])
        break
      case 'price_desc':
        result = orderBy(result, ['price'], ['desc'])
        break
      case 'sales_desc':
        result = orderBy(result, [item => get(item, 'sales', 0)], ['desc'])
        break
      default:
        // 保持原有顺序
        break
    }

    return result
  })

  /**
   * 计算属性：分页后的商品列表
   * 对筛选后的商品列表进行分页处理
   * @type {import('vue').ComputedRef<Array>}
   */
  const paginatedGoodsList = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    // 使用 es-toolkit 的 slice 方法进行分页
    return slice(filteredGoodsList.value, start, end)
  })

  /**
   * 计算属性：总页数
   * @type {import('vue').ComputedRef<number>}
   */
  const totalPages = computed(() => {
    return Math.ceil(filteredGoodsList.value.length / pageSize.value)
  })

  /**
   * 防抖搜索函数
   * 使用 es-toolkit 的 debounce 方法优化搜索性能
   */
  const debouncedSearch = debounce(() => {
    currentPage.value = 1 // 重置到第一页
    // 这里可以调用API搜索
  }, 300)

  // 监听筛选条件变化
  watch(
    () => filters.value,
    () => {
      debouncedSearch()
    },
    { deep: true }
  )

  /**
   * 设置商品列表
   * @param {Array} list - 商品列表数据
   */
  const setGoodsList = (list) => {
    // 使用 es-toolkit 的 defaultTo 提供默认值
    goodsList.value = defaultTo(list, [])
  }

  /**
   * 更新筛选条件
   * @param {Object} newFilters - 新的筛选条件
   */
  const updateFilters = (newFilters) => {
    // 使用 es-toolkit 的 merge 方法合并筛选条件
    filters.value = merge(filters.value, newFilters)
  }

  /**
   * 重置筛选条件
   */
  const resetFilters = () => {
    filters.value = {
      keyword: '',
      categoryId: null,
      brandId: null,
      priceRange: [null, null],
      sortBy: 'default',
      inStock: true
    }
  }

  /**
   * 跳转到商品详情页
   * @param {string|number} goodsId - 商品ID
   */
  const goToDetail = (goodsId) => {
    router.push({ name: 'GoodsDetail', params: { id: goodsId } })
  }

  /**
   * 添加商品到购物车
   * @param {Object} goods - 商品信息
   * @param {number} quantity - 数量，默认为1
   * @returns {Promise<boolean>} 是否添加成功
   */
  const addToCart = async (goods, quantity = 1) => {
    try {
      // 检查库存
      const stock = get(goods, 'stock', 0)
      if (stock < quantity) {
        ElMessage.warning('库存不足')
        return false
      }

      // 检查用户登录状态
      if (!userStore.isLoggedIn) {
        ElMessage.warning('请先登录')
        router.push('/login')
        return false
      }

      // 使用 es-toolkit 的 pick 方法提取必要的商品信息
      const cartItem = pick(goods, [
        'id', 'name', 'price', 'image', 'skuId'
      ])

      await cartStore.addToCart({
        ...cartItem,
        quantity
      })

      ElMessage.success('已添加到购物车')
      return true
    } catch (error) {
      ElMessage.error('添加失败，请重试')
      return false
    }
  }

  /**
   * 批量添加商品到购物车
   * @param {Array} goodsItems - 商品项数组
   * @returns {Promise<boolean>} 是否添加成功
   */
  const batchAddToCart = async (goodsItems) => {
    try {
      if (!userStore.isLoggedIn) {
        ElMessage.warning('请先登录')
        router.push('/login')
        return false
      }

      // 使用 es-toolkit 的 compact 和 map 方法过滤和转换商品项
      const validItems = compact(map(goodsItems, item => {
        const stock = get(item, 'goods.stock', 0)
        if (isNil(item.goods) || stock < item.quantity) {
          return null
        }

        const cartItem = pick(item.goods, [
          'id', 'name', 'price', 'image', 'skuId'
        ])

        return {
          ...cartItem,
          quantity: item.quantity
        }
      }))

      if (isEmpty(validItems)) {
        ElMessage.warning('没有有效的商品可添加')
        return false
      }

      await cartStore.batchAddToCart(validItems)
      ElMessage.success(`成功添加${validItems.length}件商品到购物车`)
      return true
    } catch (error) {
      ElMessage.error('批量添加失败，请重试')
      return false
    }
  }

  /**
   * 获取商品信息
   * @param {string|number} goodsId - 商品ID
   * @returns {Object|undefined} 商品信息
   */
  const getGoodsInfo = (goodsId) => {
    // 使用 es-toolkit 的 find 方法查找商品
    return find(goodsList.value, item => item.id === goodsId)
  }

  /**
   * 检查商品是否在购物车中
   * @param {string|number} goodsId - 商品ID
   * @returns {boolean} 是否在购物车中
   */
  const isInCart = (goodsId) => {
    // 使用 es-toolkit 的 some 方法检查
    return some(cartStore.items, item => item.id === goodsId)
  }

  /**
   * 获取购物车中的商品数量
   * @param {string|number} goodsId - 商品ID
   * @returns {number} 商品数量
   */
  const getCartQuantity = (goodsId) => {
    // 使用 es-toolkit 的 find 和 get 方法
    const cartItem = find(cartStore.items, item => item.id === goodsId)
    return get(cartItem, 'quantity', 0)
  }

  /**
   * 更新商品库存
   * @param {string|number} goodsId - 商品ID
   * @param {number} newStock - 新库存数量
   */
  const updateStock = (goodsId, newStock) => {
    // 使用 es-toolkit 的 find 和 defaultTo 方法
    const goods = find(goodsList.value, item => item.id === goodsId)
    if (!isNil(goods)) {
      goods.stock = defaultTo(newStock, 0)
    }
  }

  /**
   * 切换商品收藏状态
   * @param {string|number} goodsId - 商品ID
   * @returns {Promise<boolean>} 是否操作成功
   */
  const toggleFavorite = async (goodsId) => {
    try {
      if (!userStore.isLoggedIn) {
        ElMessage.warning('请先登录')
        return false
      }

      const goods = getGoodsInfo(goodsId)
      if (isNil(goods)) return false

      // 这里应该调用API切换收藏状态
      goods.isFavorite = !goods.isFavorite

      ElMessage.success(goods.isFavorite ? '已收藏' : '已取消收藏')
      return true
    } catch (error) {
      ElMessage.error('操作失败，请重试')
      return false
    }
  }

  return {
    // 响应式数据
    goodsList,
    loading,
    total,
    currentPage,
    pageSize,
    filters,

    // 计算属性
    filteredGoodsList,
    paginatedGoodsList,
    totalPages,

    // 方法
    setGoodsList,
    updateFilters,
    resetFilters,
    goToDetail,
    addToCart,
    batchAddToCart,
    getGoodsInfo,
    isInCart,
    getCartQuantity,
    updateStock,
    toggleFavorite
  }
}
