/**
 * @fileoverview 应用初始化模块
 * @description 负责应用启动时的全局配置初始化，包括登录、分享、Toast等功能的设置
 */

import { loginRun, shareInit } from 'commonkit'
import { setEntryQuerystring } from '@/utils/entryQuerystring'
import { getBizCode } from '@/utils/curEnv'
import imgToastLoading from '@/assets/images/woLoading.gif'
import jdImgToastLoading from '@/assets/images/jdLoading.gif'
import { setToastDefaultOptions } from 'vant';

/**
 * 初始化应用全局配置
 * @description 设置应用启动时需要的各种全局配置，包括commonkit登录配置、分享功能、Toast样式等
 * @function initializeApp
 * @returns {void}
 * @example
 * // 在应用启动时调用
 * initializeApp()
 */
export function initializeApp () {
  // 处理 commonkit login 的默认方法
  window.COMMONKIT_ENV_APP_NAME = 'PS-CCMS-BIZ-WEB'
  window.COMMONKIT_LOGIN_PATH = '/ps-ccms-core-front/api'
  window.COMMONKIT_WOPAY_CLIENT_ID = 'a4c07010-f246-4ee9-9824-19c4dbcef6b3'

  // 处理相关参数
  setEntryQuerystring(window.location.search.substring(1))

  // 分享功能初始化
  shareInit()

  // 登录初始化
  loginRun({ autoQueryStatus: false })

  // 设置 Toast loading
  setToastDefaultOptions('loading', {
    duration: 0,
    icon: getBizCode() !== 'ygjd' ? imgToastLoading : jdImgToastLoading,
    forbidClick: true,
    loadingType: 'circular',
    className: 'wo-loading'
  })

  // 设置 Toast 默认选项
  setToastDefaultOptions({
    forbidClick: true,
    duration: 2000
  })
}

/**
 * 修复特定环境下的兼容性问题
 * @description 针对特定客户端环境（如手厅测试客户端）的样式和功能修复
 * @function fixEnvironmentIssues
 * @returns {void}
 * @example
 * // 在应用初始化后调用
 * fixEnvironmentIssues()
 */
export function fixEnvironmentIssues () {
  /**
   * 修复手厅测试客户端高度问题
   * @description 检测联通手厅客户端，当屏幕高度等于body高度时添加修复样式类
   * @inner
   */
  const fixStHeight = () => {
    const useragent = window.navigator.userAgent
    const isSt = /unicom{version/i.test(useragent)
    if (isSt && window.screen.height === document.body.offsetHeight) {
      document.body.classList.add('st-height-fix')
    }
  }
  fixStHeight()
}
