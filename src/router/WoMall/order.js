export const orderRoutes = [
  {
    path: '/user/order/detail',
    name: 'user-order-detail',
    meta: { login: true },
    component: () => import('@views/CommonMall/Order/OrderDetail/OrderDetail.vue'),
  },
  {
    path: '/user/order/search',
    name: 'user-order-search',
    meta: { login: true },
    component: () => import('@views/CommonMall/Order/OrderSearch/IndexView.vue'),
  },
  {
    path: '/user/order/searchList',
    name: 'user-order-search-list',
    meta: { login: true },
    component: () => import('@views/CommonMall/Order/OrderSearch/OrderSearch.vue'),
  },
  {
    path: '/user/order/recycle',
    name: 'user-order-recycle',
    meta: { login: true },
    component: () => import('@views/CommonMall/Order/OrderRecycleBin/OrderRecycleBin.vue'),
  },
  {
    path: '/user/order/entry-express',
    name: 'user-order-entry-express',
    meta: { login: true },
    component: () => import('@views/CommonMall/Order/OrderExpress/OrderEntryExpress.vue'),
  },
  {
    path: '/user/order/express',
    name: 'user-order-express',
    meta: { login: true },
    component: () => import('@views/CommonMall/Order/OrderExpress/OrderExpress.vue'),
  },
  {
    path: '/user/order/express/muti',
    name: 'user-order-muti-express',
    meta: { login: true },
    component: () => import('@views/CommonMall/Order/OrderExpress/OrderMutiExpress.vue'),
  },
  {
    path: '/orderconfirm',
    name: 'order-confirm',
    meta: { login: true },
    component: () => import('@views/CommonMall/Order/OrderConfirm/OrderConfirm.vue'),
  },
  {
    path: '/user/order/list',
    name: 'user-order-list',
    meta: { login: true },
    component: () => import('@views/CommonMall/Order/OrderList/OrderList.vue'),
  },
  {
    path: '/orderconfirm/callback',
    name: 'user-order-confirm-callback',
    component: () => import('@views/CommonMall/Order/OrderCallback/OrderCallback.vue'),
  }
]
